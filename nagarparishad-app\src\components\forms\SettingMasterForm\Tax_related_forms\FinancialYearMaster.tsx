import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "@/components/ui/use-toast";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { z } from "zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { GlobalContext } from "@/context/GlobalContext";
import { ResponseData } from "@/model/auth/authServices";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { t } from "i18next";
import { useFinancialYearController } from "@/controller/tax/useFinancialYearController";
import Api from "@/services/ApiServices";
import { useQueryClient } from "@tanstack/react-query";

const FinancialYearMaster = () => {
  const [financialYears, setFinancialYears] = useState([]);
  const { FinancialYearList, financialYearLoading } = useFinancialYearController();
  const [formData, setFormData] = useState({
    year: "",
    start_date: "",
    end_date: "",
  });
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [editId, setEditId] = useState(null);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false); // Add loading state
  const userRef = useRef(null);
  const { setOpen, toggleCollapse, setMasterComponent } = useContext(GlobalContext);
  const queryClient = useQueryClient(); // Initialize useQueryClient

  const schema = z.object({
    year: z.string().min(1, t("errorsRequiredField")),
    start_date: z.string().min(1, t("errorsRequiredField")),
    end_date: z.string().min(1, t("errorsRequiredField")),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      year: "",
      start_date: "",
      end_date: "",
    },
  });

  const {
    formState: { errors },
    reset,
    control,
  } = form;

  const Form = FormProvider;
  const dynamicValues = {
    name: t("setting.financialYear"),
  };

  function handleEdit(item) {
    setIsEditing(true);
    setEditId(item.id);
    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      year: item.year,
      start_date: item.start_date,
      end_date: item.end_date,
    });
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const checkDuplicate = (year: string) => {
    return financialYears.some(
      (item) =>
        item.year === year &&
        (!isEditing || item.id !== editId)
    );
  };

  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();

    if (checkDuplicate(data.year)) {
      toast({
        title: t("This financial year already exists"),
        variant: "destructive",
      });
      return;
    }
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "year",
      header: t("setting.financialYear"),
      cell: ({ row }) => <div>{row.original?.financial_year_range}</div>,
    },
    {
      accessorKey: "start_date",
      header: t("setting.startDate"),
      cell: ({ row }) => <div>{row.original?.from_date}</div>,
    },
    {
      accessorKey: "end_date",
      header: t("setting.endDate"),
      cell: ({ row }) => <div>{row.original?.to_date}</div>,
    },
    {
      accessorKey: "status",
      header: t("Status"),
      cell: ({ row }) => (
        <div className={row.original?.is_active ? "text-green-500" : "text-red-500"}>
          {row.original?.is_active ? "Active" : "Inactive"}
        </div>
      ),
    },
  ];

  const handleGenrate = async () => {
    setIsLoading(true); // Set loading state to true
    try {
      const response = await Api.genrateFyYear();
      console.log("responseresponse", response);
      if (response.data.statusCode === 201) {
        toast({
          title: t("api.financialYearGenerated"),
          variant: "success",
        });
        queryClient.invalidateQueries({queryKey: ["financialYears"]}); // Refetch financial years

      }
    } catch (error) {
      // Handle error
      toast({
        title: t("This financial year already exists"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false); // Set loading state to false
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">{t("setting.financialYear")}</h1>

      <WhiteContainer className="mt-5">
        {/* <div className="flex justify-end mb-2">
          <Button onClick={handleGenrate} disabled={isLoading}>
            {isLoading ? t("generating") : t("generate")}
          </Button>
        </div> */}
        <TanStackTable columns={columns} data={FinancialYearList}                  loader={financialYearLoading ? true : false}
        />
      </WhiteContainer>
    </div>
  );
};

export default FinancialYearMaster;
