import React from "react";
import { useTranslation } from "react-i18next";
import WhiteContainer from "../WhiteContainer";

import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Download, ShieldCheck, Eye, ArrowLeft, ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import BreadCrumb from "../BreadCrumb";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { t } from "i18next";
import { Loader } from "@/components/globalcomponent/Loader";
const PropertyViewDetail = () => {
  // const { usePropertyDetails } = usePropertyRegistrationController();

  // const propertyId: string =
  //   location?.state || "9b21d9a3-8824-430e-bb39-877d7119997c";
  // console.log("property id", propertyId);
  // const {
  //   data: propertyDetails,
  //   isLoading,
  //   error,
  // } = usePropertyDetails(propertyId);
  // if (isLoading) {
  //   return (
  //     <div className="flex justify-center items-center my-4">
  //     <Loader/>
  //     </div>
  //   );
  // }

  // if (error) {
  //   return (
  //     <div className="flex justify-center items-center my-4 text-red-500">
  //       {t("propertyView.errorMessage", "An error occurred while loading the property details.")}
  //     </div>
  //   );
  // }


  // const singlePropertyDetails = propertyDetails?.data;
  // const ownerDetails = singlePropertyDetails?.property_owner_details;
  // const propertyUsageDetails = singlePropertyDetails?.property_usage_details;

const navigate = useNavigate();

  const location = useLocation();
  const propertyData = location.state?.propertyData;
const ownerDetails = propertyData?.property_owner_details;
const propertyUsageDetails = propertyData?.property_usage_details;
  const singlePropertyDetails = {
    sr_no: "123456",
    city_survey_number: "CSV789",
    ward: { ward_name: "Ward A" },
    zone: { zoneName: "North Zone" },
    house_or_apartment_name: "Green Valley Apartments",
    street: { street_name: "Maple Street" },
    gis_number: "GIS456789",
    latitude: "23.0225° N",
    longitude: "72.5714° E",
    plot_area: "1200 sq ft",
    Plot_construction_area: "900 sq ft",
    Plot_empty_area: "300 sq ft",
    waste_tax: "₹500"
  };

  const ownerDetailss = [
    {
      name: "John Doe",
      owner_type: { owner_type: "Primary" },
      mobile_number: "9876543210",
      email_id: "<EMAIL>",
      aadhar_number: "123456789012"
    }
  ];

  const propertyUsageDetailss = [
    {
      propertyType: { propertyType: "Residential" },
      construction_year: "2020",
      usageType: { usage_type: "Self Occupied" },
      usageSubType: { usage_sub_type: "Apartment" },
      length: "40 ft",
      width: "30 ft",
      are_sq_ft: "1200",
      are_sq_meter: "111.48",
      authorized: true
    }
  ];
  const ownerColumn = [
    {
      accessorKey: "index",
      header: `${t("ownerDetails.SrNo")}`,
     cell: ({ row }: { row: any })  => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "name",
      header: `${t("ownerDetails.name")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.name}</div>,
    },
    {
      accessorKey: "owner_type",
      header: `${t("ownerDetails.type")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.owner_type?.owner_type}</div>,
    },
    {
      accessorKey: "mobile_number",
      header: `${t("ownerDetails.mobileNumber")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.mobile_number}</div>,
    },
    {
      accessorKey: "email_id",
      header: `${t("ownerDetails.emailId")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.email_id}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => (
        <div>{formatAdharNo(row.original?.aadhar_number)}</div>
      ),
    },
  ];

  const usageColumns = [
    {
      accessorKey: "index",
      header: `${t("propertyDetail.SrNo")}`,
     cell: ({ row }: { row: any })  => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "propertyType",
      header: `${t("propertyDetail.propertyType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.propertyType?.propertyType}</div>,
    },
    {
      accessorKey: "construction_year",
      header: `${t("propertyDetail.constructionDate")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.construction_year}</div>,
    },
    {
      accessorKey: "usageType",
      header: `${t("propertyDetail.usageType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.usageType?.usage_type}</div>,
    },
    {
      accessorKey: "usageSubType",
      header: `${t("propertyDetail.usageSubType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.usageSubType?.usage_sub_type}</div>,
    },
    {
      accessorKey: "length",
      header: `${t("propertyDetail.length")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.length}</div>,
    },
    {
      accessorKey: "width",
      header: `${t("propertyDetail.width")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.width}</div>,
    },
    {
      accessorKey: "are_sq_ft",
      header: `${t("propertyDetail.areaSqFt")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.are_sq_ft}</div>,
    },
    {
      accessorKey: "are_sq_meter",
      header: `${t("propertyDetail.areaSqMt")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.are_sq_meter}</div>,
    },
    {
      accessorKey: "authorized",
      header: `${t("propertyDetail.propertyStatus")}`,
     cell: ({ row }: { row: any })  => (
        <div>{row.original.authorized ? "अधिकृत" : "अनधिकृत"}</div>
      ),
    },
  ];

  return (
    <>
    <div className="bg-Secondary w-full py-9">

      <div className="max-w-[80%]  mx-auto py- px-2">
        <button
            onClick={() => navigate(-1)}
            className="flex items-center text-lg font-bold text-blue-700 hover:text-blue-600"
          >
            <ChevronLeft className="mr-2 h-7 w-9 font-bold" />
          </button>
      <WhiteContainer className="shadow-xl">
            <div className="flex items-centerjustify-between w-full h-full">
              <h1 className="text-[18px] font-semibold font-Poppins w-full ml-3">
                {t("propertyView.ownerDetail")}
              </h1>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mx-3 my-2">
              
                <div>
                  <p className="font-semibold">
                    {t("propertyView.SrNo")}:{" "}
                    <span className="font-normal">
                      {propertyData?.sr_no}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.citySurveyNumber")}:{" "}
                    <span className="font-normal">
                      {propertyData?.city_survey_number}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.ward")}:{" "}
                    <span className="font-normal">
                      {propertyData?.ward?.ward_name}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.zone")}:{" "}
                    <span className="font-normal">
                      {propertyData?.zone?.zoneName}
                    </span>
                  </p>
                </div>
           
         
                <div>
                  <p className="font-semibold">
                    {t("propertyView.houseName")}:{" "}
                    <span className="font-normal">
                      {propertyData?.house_or_apartment_name}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.street")}:{" "}
                    <span className="font-normal">
                      {propertyData?.street?.street_name}
                    </span>
                  </p>
                </div>
  
                <div>
                  <p className="font-semibold">
                    {t("propertyView.GisNumber")}:{" "}
                    <span className="font-normal">
                      {propertyData?.gis_number}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.latitude")}:{" "}
                    <span className="font-normal">
                      {propertyData?.latitude}
                    </span>
                  </p>
                </div>
            
           
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotArea")}:{" "}
                    <span className="font-normal">
                      {propertyData?.plot_area}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotConstructionArea")}:{" "}
                    <span className="font-normal">
                      {propertyData?.Plot_construction_area}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotEmptyArea")}:{" "}
                    <span className="font-normal">
                      {propertyData?.Plot_empty_area}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.longitude")}:{" "}
                    <span className="font-normal">
                      {propertyData?.longitude}
                    </span>
                  </p>
                </div>
             
          
                <div>
                  <p className="font-semibold">
                    {t("propertyView.waste_tax")}:{" "}
                    <span className="font-normal">
                      {propertyData?.waste_tax}
                    </span>
                  </p>
                </div>
             
            </div>
          </WhiteContainer>


        <WhiteContainer className="overflow-x-auto shadow-xl">
          <Tabs defaultValue="owner" className="w-full ">
            <TabsList className="w-[100%] md:w-fit !h-12">
              <TabsTrigger
                className="w-1/2 p-[10px]  rounded text-[15px]  px-10"
                value="owner"
              >
                {t("propertyView.ownerDetails")}{" "}
              </TabsTrigger>
              <TabsTrigger
                className="w-1/2 p-[10px]  rounded text-[15px] "
                value="property"
              >
                {t("propertyView.propertyDetails")}{" "}
              </TabsTrigger>
              {/* <TabsTrigger
                className="w-1/2 p-[10px]  rounded text-[15px] "
                value="account"
              >
                {t("propertyView.taxDetails")}{" "}
              </TabsTrigger>
              <TabsTrigger
                className="w-1/2 p-[10px]  rounded text-[16px] "
                value="documents"
              >
                {t("propertyView.documents")}{" "}
              </TabsTrigger> */}
            </TabsList>
            <TabsContent value="owner">
              <TanStackTable columns={ownerColumn} data={ownerDetails || []} />
            </TabsContent>
            <TabsContent value="property">
              <TanStackTable
                columns={usageColumns}
                data={propertyUsageDetails || []}
              />
            </TabsContent>
            <TabsContent value="account">
              <Table className="w-full text-sm text-left text-zinc-500 mt-8 ">
                <TableHeader className="text-[16px] text-zinc-700 capitalize bg-gray-100">
                  <TableRow>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("taxDetails.date")}{" "}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("taxDetails.taxYear")}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("taxDetails.amount")} {"(₹)"}{" "}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("taxDetails.status")}{" "}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="py-3 px-6">2023-01-15</TableCell>
                    <TableCell className="py-3 px-6">2022</TableCell>
                    <TableCell className="py-3 px-6">1,200.00</TableCell>
                    <TableCell className="py-3 px-6">
                      <span className="w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight text-green-700 bg-green-100 rounded-full">
                        Paid
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-3 px-6">2023-02-20</TableCell>
                    <TableCell className="py-3 px-6">2022</TableCell>
                    <TableCell className="py-3 px-6">900.00</TableCell>
                    <TableCell className="py-3 px-6">
                      <span className=" w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight text-green-700 bg-green-100  rounded-full">
                        Paid
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-3 px-6">2023-03-10</TableCell>
                    <TableCell className="py-3 px-6">2022</TableCell>
                    <TableCell className="py-3 px-6">1,500.00</TableCell>
                    <TableCell className="py-3 px-6">
                      <span className=" w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight text-green-700 bg-green-100 rounded-full">
                        Paid
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-3 px-6">2023-04-05</TableCell>
                    <TableCell className="py-3 px-6">2022</TableCell>
                    <TableCell className="py-3 px-6">750.00</TableCell>
                    <TableCell className="py-3 px-6">
                      <span className="w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight text-green-700 bg-green-100 rounded-full">
                        Paid
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-3 px-6">2023-05-22</TableCell>
                    <TableCell className="py-3 px-6">2022</TableCell>
                    <TableCell className="py-3 px-6">2,000.00</TableCell>
                    <TableCell className="py-3 px-6">
                      <span className="w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight text-green-700 bg-green-100 rounded-full">
                        Paid
                      </span>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TabsContent>
            <TabsContent value="documents">
              <Table className="w-full text-sm text-left text-zinc-500 mt-8">
                <TableHeader className="text-[16px] text-zinc-700 capitalize bg-gray-100">
                  <TableRow>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("documents.SrNo")}{" "}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("documents.documentName")}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("documents.documentType")}{" "}
                    </TableHead>
                    <TableHead scope="col" className="py-3 px-6 font-semibold">
                      {t("documents.action")}{" "}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="py-4 px-6">1</TableCell>
                    <TableCell className="py-4 px-6">
                      Report Q1 2024 Sales
                    </TableCell>
                    <TableCell className="py-4 px-6">PDF</TableCell>
                    <TableCell className="py-4 px-6 flex ">
                      {" "}
                      <Download className="w-5 h-5 text-BlueText cursor-pointer" />
                      <Eye className="w-5 h-5 ml-4 cursor-pointer" />
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-4 px-6">2</TableCell>
                    <TableCell className="py-4 px-6">
                      Project X Proposal
                    </TableCell>
                    <TableCell className="py-4 px-6">PNG</TableCell>
                    <TableCell className="py-4 px-6 flex">
                      {" "}
                      <Download className="w-5 h-5 text-BlueText cursor-pointer" />
                      <Eye className="w-5 h-5 ml-4 cursor-pointer" />
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </WhiteContainer>
      </div>
      </div>
    </>
  );
};

export default PropertyViewDetail;
