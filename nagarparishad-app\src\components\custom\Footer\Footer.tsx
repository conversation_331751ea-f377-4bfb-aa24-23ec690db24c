import React from "react";
import { useTranslation } from "react-i18next";
import logo from "../../../assets/img/homepage/logo.png";
import Twitter from "../../../assets/img/Dashboard/twitter.svg";
import { Phone } from "lucide-react";
import { MapPin } from "lucide-react";
import { Mail } from "lucide-react";
import { Clock3 } from "lucide-react";
import { BookUser } from "lucide-react";
import { Facebook } from "lucide-react";
import { Youtube } from "lucide-react";
import { Instagram } from "lucide-react";

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <section className="bg-Primary ">
      {/* <div className="flex justify-center sm:gap-x-16 gap-x-10 mb-4 mt-2 mx-2">
        <h3 className="text-base font-semibold mb-2 text-lightwhite-Secondary hover:text-white">
          <a href="javascript:void(0)"> {t("footerWebPolicies")}</a>
        </h3>
        <h3 className="text-base font-semibold mb-2 text-lightwhite-Secondary hover:text-white">
          <a href="javascript:void(0)"> {t("footerSiteMap")}</a>
        </h3>
        <h3 className="text-base font-semibold mb-2 text-lightwhite-Secondary hover:text-white">
          <a href="/contact-us"> {t("footerContactUs")}</a>
        </h3>
      </div> */}
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 sm:gap-0 gap-6 px-4 sm:py-10 py-6 sm:px-6 lg:px-8  ">
        <div className="flex sm:flex-row justify-between flex-col-reverse md:grid-cols-3 gap-12">
          <div className="flex flex-col">
            <h3 className="text-base font-semibold mb-2 flex leading-5 text-lightwhite-Secondary hover:text-white ">
              <Phone className="w-5 mr-2" />
              <span className="ml-2">{t("footerContactNumber")}</span>
            </h3>
            <h3 className="text-base font-semibold mb-2 flex leading-5 text-lightwhite-Secondary hover:text-white">
              <BookUser className="w-5 mr-2" />
              <span className="ml-2">{t("footerLandLineNumber")}</span>
            </h3>

            <h3 className="text-base font-semibold mb-2 flex leading-5 text-lightwhite-Secondary hover:text-white">
              <MapPin className="w-5 min-w-5 mr-2" />
              <span>{t("footerAddress")}</span>
            </h3>

            <h3 className="text-base font-semibold mb-2 flex leading-5 text-lightwhite-Secondary hover:text-white">
              <Mail className="w-5 mr-2" />
              <span>{t("footerEmailId")}</span>
            </h3>

            <h3 className="text-base font-semibold mb-2 flex leading-5 text-lightwhite-Secondary hover:text-white">
              <Clock3 className="w-5 mr-2" />
              <span className="mr-2">{t("footerOpening")} </span>
            </h3>
          </div>

          <div className="flex flex-col items-center md:translate-x-1/2">
            <img src={logo} alt="Logo" className="h-24" />
            <h2 className="text-xl font-bold text-lightwhite-Secondary hover:text-white text-center">
              {t("footerOrganizationName")}
            </h2>
            {/* <div className="flex gap-4 my-3">
              <a href="javascript:void(0)" className="group">
                <Facebook
                  className="text-gray-500 group-hover:text-white"
                  size={25}
                />
              </a>
              <a href="javascript:void(0)" className="group">
                <Instagram
                  className="text-gray-500 group-hover:text-white"
                  size={25}
                />
              </a>
              <a href="javascript:void(0)" className="group">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  x="0px"
                  y="0px"
                  width="25"
                  height="25"
                  viewBox="0 0 30 30"
                  className="fill-current text-gray-500 group-hover:text-white"
                >
                  <path d="M26.37,26l-8.795-12.822l0.015,0.012L25.52,4h-2.65l-6.46,7.48L11.28,4H4.33l8.211,11.971L12.54,15.97L3.88,26h2.65 l7.182-8.322L19.42,26H26.37z M10.23,6l12.34,18h-2.1L8.12,6H10.23z"></path>
                </svg>
              </a>
              <a href="javascript:void(0)" className="group">
                <Youtube
                  className="text-gray-500 group-hover:text-white"
                  size={25}
                />
              </a>
            </div> */}
          </div>
        </div>
        <hr className="border-1 border-lightwhite-Secondary sm:hidden block" />
        <div className="flex flex-col sm:items-end items-start sm:ml-0 ml-6">
          {/* <h3 className="text-lg font-semibold mb-2 text-lightwhite-Secondary ml-4 ">
            {t("footerLinks")}
          </h3> */}
          <div className="flex">
            <ul className="flex flex-col gap-y-1 text-base font-semibold mb-2 ">
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a href="https://maharashtra.gov.in/" target="_blank">
                  {" "}
                  {t("footerListItem1")}
                </a>
              </li>
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a
                  href="https://aaplesarkar.maharashtra.gov.in/"
                  target="_blank"
                >
                  {t("footerListItem2")}
                </a>
              </li>
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a href="https://bhulekh.mahabhumi.gov.in/" target="_blank">
                  {t("footerListItem3")}
                </a>
              </li>
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a
                  href="https://kolhapur.gov.in/en/department/mp/"
                  target="_blank"
                >
                  {t("footerListItem4")}
                </a>
              </li>
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a
                  href="https://kolhapur.gov.in/department/%E0%A4%9C%E0%A4%BF-%E0%A4%AA/"
                  target="_blank"
                >
                  {t("footerListItem5")}
                </a>
              </li>
              <li className="list-disc text-lightwhite-Secondary hover:text-white">
                <a
                  href="https://mahalandbank.maharashtra.gov.in/"
                  target="_blank"
                >
                  {t("footerListItem6")}
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="bg-PrimaryDark py-2 flex justify-between px-5 flex-wrap text-white">
        <p className="text-center text-sm sm:mb-0 mb-3">
          Copyright © {currentYear} Shirol Municipal Committee, All rights
          reserved.
        </p>
        <p className="text-center text-sm sm:mb-0 mb-3 sm:w-fit w-full">
          Download Mobile App
        </p>
        <p className="text-center text-sm sm:mb-0 mb-3">
          Design and Developed By{" "}
          <a href="https://onpointsoft.com/" target="_blank">
            onPoint Software Services
          </a>
        </p>
      </div>
    </section>
  );
};

export default Footer;
