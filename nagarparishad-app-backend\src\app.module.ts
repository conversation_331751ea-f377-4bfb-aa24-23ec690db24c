import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm'; // Import TypeOrmModule
import { DatabaseModule } from 'libs/database';

import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { WardMasterService } from './ward_master/ward_master.service';
import { WardMasterController } from './ward_master/ward_master.controller';
import { ZoneMasterController } from './zone_master/zone_master.controller';
import { ZoneMasterService } from './zone_master/zone_master.service';
import { LocationMasterController } from './location_master/location_master.controller';
import { LocationMasterService } from './location_master/location_master.service';

import { StreetMasterController } from './street_master/street_master.controller';
import { StreetMasterService } from './street_master/street_master.service';
import { AreaMasterController } from './area_master/area_master.controller';
import { AreaMasterService } from './area_master/area_master.service';
import { AdminBoundaryMasterController } from './admin-boundary_master/admin-boundary_master.controller';
import { AdminBoundaryMasterService } from './admin-boundary_master/admin-boundary_master.service';
import { ElectionBoundaryMasterController } from './election-boundary_master/election-boundary_master.controller';
import { ElectionBoundaryMasterService } from './election-boundary_master/election-boundary_master.service';
import { HelpersModule } from '@helper/helpers';
import { PropertyTypeMasterController } from './property-type_master/property-type_master.controller';
import { PropertyTypeMasterService } from './property-type_master/property-type_master.service';
import { PropertySubTypeMasterController } from './property-sub-type_master/property-sub-type_master.controller';
import { PropertySubTypeMasterService } from './property-sub-type_master/property-sub-type_master.service';
import { UsageMasterController } from './usage_master/usage_master.controller';
import { UsageMasterService } from './usage_master/usage_master.service';
import { UsageSubMasterController } from './usage_sub_master/usage_sub_master.controller';
import { UsageSubMasterService } from './usage_sub_master/usage_sub_master.service';
import { ConstructionClassController } from './construction_class/construction_class.controller';
import { ConstructionClassService } from './construction_class/construction_class.service';
import { PropertyMasterController } from './property_master/property_master.controller';
import { PropertyMasterService } from './property_master/property_master.service';
import { RoleMasterController } from './role_master/role_master.controller';
import { RoleMasterService } from './role_master/role_master.service';
import { UserController } from './user/user.controller';
import { UserService } from './user/user.service';
import { AuthController } from './auth/auth.controller';
import { AuthService } from './auth/auth.service';
import { AccessTokenGuard, JwtAuthModule } from '@jwt/jwt-auth';
import { PermissionController } from './permission/permission.controller';
import { PermissionService } from './permission/permission.service';
import { FormMasterController } from './form_master/form_master.controller';
import { FormMasterService } from './form_master/form_master.service';
import { ModuleMasterController } from './module_master/module_master.controller';
import { ModuleMasterService } from './module_master/module_master.service';
import { PermissionHelper } from './permission/permission.helper';

import { TaxCalculateController } from './tax-calculate/tax-calculate.controller';
import { TaxCalculateService } from './tax-calculate/tax-calculate.service';
import { Tax_billGenerateService } from './tax-calculate/tax-billGenarete.service';
import { BillingController } from './billing/billing.controller';
import { BillingService } from './billing/billing.service';
import { ImportModuleController } from './import-module/import-module.controller';
import { ImportModuleService } from './import-module/import-module.service';
import { LogsController } from './logs/log.controller';
import { LogsService } from './logs/log.service';
import { KarAkaraniController } from './kar-akarani/karAkarani.controller';
import { KarAkaraniService } from './kar-akarani/karAkarani.service';
import { Logs } from 'libs/database/entities'; // Import Logs entity
import { AnnualKarAkaraniController } from './annual-kar-akarani/annual-kar-akarani.controller';
import { AnnualKarAkaraniService } from './annual-kar-akarani/annual-kar-akarani.service';
import { PropertyOwnerController} from './property_master/property_owner.controller';
import { PropertyOwnerService} from './property_master/property_owner.service';
import { OwnerTypeMasterController } from './owner-type_master/owner-type_master.controller';
import { OwnerTypeMasterService } from './owner-type_master/owner-type_master.service';
import { FinancialMasterController } from './financial-master/financial_master.controller';
import { FinancialMasterService } from './financial-master/financial_master.service';
import { UserPropertyController } from './public/user-property/user-property.controller';
import { UserPropertyService } from './public/user-property/user-property.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GlobalSearchController } from './gloabl_serch/global_serch.controller';
import { GlobalSearchService } from './gloabl_serch/gloabal_serch.service';
import { FloorMasterService } from './floor_master/floor_master.service';
import { FloorMasterController } from './floor_master/floor_master.controller';
import { PropertyTypeClassMasterController } from './property_type_class/property_type_ClassMaster.controller';
import { PropertyTypeClassMasterService } from './property_type_class/propertyTypeClassMaster.service';
import { MasterDepreciationService } from './tax_Masters/depreciation_master/master_depreciation.service';
import { MasterRRRateService } from './tax_Masters/rr_rate_master/master-rr-rate.service';
import { MasterDepreciationController } from './tax_Masters/depreciation_master/master-depreciation.controller';
import { Master_weightingController } from './tax_Masters/weighting_master/master-weighting.controller';
import { Master_tax_valueController } from './tax_Masters/tax_value_master/master-tax-value.controller';
import { Master_rr_construction_rateController } from './tax_Masters/rr-construction-rate_master/master-rr-construction-rate.controller';
import { Master_rr_construction_rateService } from './tax_Masters/rr-construction-rate_master/master-rr-construction-rate.service';
import { Master_tax_valueService } from './tax_Masters/tax_value_master/master-tax-value.service';
import { Master_weightingService } from './tax_Masters/weighting_master/master-weighting.service';
import { MasterGhanKachraRateService } from './tax_Masters/ghan_kachara_master/masterGhanKachraRate.service';
import { MasterGhanKachraRateController } from './tax_Masters/ghan_kachara_master/masterGhanKachraRate.controller';
import { MasterRRRateController } from './tax_Masters/rr_rate_master/master-rr-rate.controller';
import { TaxPendingDuesController } from './tax-pending-dues/tax-pending_dues.controller';
import { TaxPendingDuesService } from './tax-pending-dues/tax-pending_dues.service';
import { PaymentLogMasterService } from './payment_log_master/payment_log_master.service';
import { PaymentLogMasterController } from './payment_log_master/payment_log_master.controller';
import { BookNumberMasterController } from './book_number_master/book_number_master.controller';
import { BookNumberMasterService } from './book_number_master/book_number_master.service';
import { BackupMigrationPropertyUsageDetailsController } from './backup-migration-property-usage-details/backup-migration-property-usage-details.controller';
import { BackupMigrationPropertyUsageDetailsService } from './backup-migration-property-usage-details/backup-migration-property-usage-details.service';
import { DashboardService } from './dashboard_stats/dashboard_stat.service';
import { DashboardController } from './dashboard_stats/dashboard_stat.controller';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from '@helper/helpers/role-based-access/permission.guard';
import { RegisterController } from './register/register.controller';
import { RegisterService } from './register/register.service';
import { CollectorMasterController } from './collector_master/collector_master.controller';
import { CollectorMasterService } from './collector_master/collector_master.service';
import { PropertyDivideController } from './property_divide/property_divide.controller';
import { PropertyDivideService } from './property_divide/property_divide.service';
import { ReassessmentRangeController } from './reassesment_master/reassesment.controller';
import { ReassessmentRangeService } from './reassesment_master/reassesment.service';
import { PenaltyFeeYearWiseModule } from './penalty-fee-yearWise/penalty_fee.module';
import { PenaltyFeeYearWiseController } from './penalty-fee-yearWise/penalty_fee.controller';
import { PenaltyFeeYearWiseService } from './penalty-fee-yearWise/penalty_fee.services';
import { PaidDataModule } from './paid-data/paid-data.module';
import { PaidDataController } from './paid-data/paid-data.controller';
import { PaidDataService } from './paid-data/paid-data.service';
import { CronJobsController } from './cron-jobs/cron-jobs.controller';
import { CronJobsService } from '@helper/helpers/cron-jobs/cron-jobs.service';
import { NotificationsController } from './notifications/notifications.controller';
import { NotificationsService } from './notifications/notifications.service';
import { OfflineNotificationRepository } from '../libs/database/src/repositories/offline-notification.repository';
import { FileStorageService } from './notifications/file-storage.service';
import { NotificationCleanupService } from './notifications/notification-cleanup.service';
import { OfflineNotificationEntity } from 'libs/database/entities';
import { DemandReportController } from './reports/demand-report.controller';
import { DemandReportService } from './reports/demand-report.service';
import { EmailService } from './utils/email.service';



@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,
    HelpersModule,
    JwtAuthModule,
    TypeOrmModule.forFeature([Logs, OfflineNotificationEntity]),
    EventEmitterModule.forRoot(),
    PenaltyFeeYearWiseModule,
    PaidDataModule,
    
    

     // Register the Logs entity



  ],
  controllers: [
    AppController,
    WardMasterController,
    ZoneMasterController,
    LocationMasterController,
    StreetMasterController,
    AreaMasterController,
    AdminBoundaryMasterController,
    ElectionBoundaryMasterController,
    PropertyTypeMasterController,
    PropertySubTypeMasterController,
    UsageMasterController,
    UsageSubMasterController,
    ConstructionClassController,
    PropertyMasterController,
    RoleMasterController,
    UserController,
    AuthController,
    PermissionController,
    FormMasterController,
    ModuleMasterController,
    TaxCalculateController,
    BillingController,
    ImportModuleController,
    LogsController,
    KarAkaraniController,
    AnnualKarAkaraniController,
    PropertyOwnerController,
    OwnerTypeMasterController,
    FinancialMasterController,
    UserPropertyController,
    GlobalSearchController,
    FloorMasterController,
    PropertyTypeClassMasterController,
    Master_weightingController,
    Master_tax_valueController,
    Master_rr_construction_rateController,
    MasterDepreciationController,
    MasterGhanKachraRateController,
    MasterRRRateController,
    TaxPendingDuesController,
    PaymentLogMasterController,
    BookNumberMasterController,
    BackupMigrationPropertyUsageDetailsController,
    DashboardController,
    RegisterController,
    CollectorMasterController,
    PropertyDivideController,
    ReassessmentRangeController,
    PenaltyFeeYearWiseController,
    PaidDataController,
    CronJobsController,
    NotificationsController,
    DemandReportController,

  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AccessTokenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    WardMasterService,
    ZoneMasterService,
    LocationMasterService,
    StreetMasterService,
    AreaMasterService,
    AdminBoundaryMasterService,
    ElectionBoundaryMasterService,
    PropertyTypeMasterService,
    PropertySubTypeMasterService,
    UsageMasterService,
    UsageSubMasterService,
    ConstructionClassService,
    PropertyMasterService,
    RoleMasterService,
    UserService,
    AuthService,
    PermissionService,
    FormMasterService,
    ModuleMasterService,
    PermissionHelper,
    TaxCalculateService,
    BillingService,
    Tax_billGenerateService,
    ImportModuleService,
    ImportModuleService,
    LogsService,
    KarAkaraniService,
    AnnualKarAkaraniService,
    PropertyOwnerService,
    OwnerTypeMasterService,
    FinancialMasterService,
    UserPropertyService,
    GlobalSearchService,
    FloorMasterService,
    PropertyTypeClassMasterService,
    MasterGhanKachraRateService,
    MasterDepreciationService,
    Master_rr_construction_rateService,
    Master_tax_valueService,
    Master_weightingService,
    MasterRRRateService,
    TaxPendingDuesService,
    PaymentLogMasterService,
    BookNumberMasterService,
    BackupMigrationPropertyUsageDetailsService,
    DashboardService,
    RegisterService,
    CollectorMasterService,
    PropertyDivideService,
    ReassessmentRangeService,
    PenaltyFeeYearWiseService,
    PaidDataService,
    CronJobsService,
    NotificationsService,
    OfflineNotificationRepository,
    FileStorageService,
    NotificationCleanupService,
    DemandReportService,
    EmailService,
    DashboardService
  ],
})
export class AppModule {}
