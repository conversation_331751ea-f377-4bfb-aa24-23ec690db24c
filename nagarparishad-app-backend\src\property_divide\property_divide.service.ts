import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  PropertyEntity,
  Property_Fod_Details_Entity,
  Property_Owner_Details_Entity,
  Property_Usage_Details_Entity,
} from 'libs/database/entities';
import {
  PropertyMasterRepository,
  Property_Owner_DetailsRepository,
  Property_Usage_DetailsRepository,
} from 'libs/database/repositories';
import { PropertyOwnerService } from 'src/property_master/property_owner.service';
import { Connection, In, QueryRunner } from 'typeorm';

@Injectable()
export class PropertyDivideService {
  constructor(
    private readonly connection: Connection, // Use Connection to create QueryRunner
    private readonly propertyRepository: PropertyMasterRepository,
    private readonly ownerRepository: Property_Owner_DetailsRepository,
    private readonly usageRepository: Property_Usage_DetailsRepository,
    private readonly propertyOwnerService: PropertyOwnerService
  ) {}

  async checkNewPropertyNumberUniqueness(propertyNumber: string) {
    const existingProperty =
      await this.propertyRepository.findByPropertyNumber(propertyNumber);
    return {
      message: '',
      data: !existingProperty, // Returns true if the number is unique
    };
  }

  async checkNewPropertyNumbers(propertyNumber: string[]) {
    const validPropertyNumber: any[] = [];

    for (const number of propertyNumber) {
      const isUnique = await this.checkNewPropertyNumberUniqueness(
        number.trim(),
      );
      validPropertyNumber.push({
        propertyNumber: number,
        isUnique: isUnique.data,
      });
    }

    return {
      message: 'Property numbers checked successfully',
      data: validPropertyNumber,
    };
  }

  async divideProperty(
    originalPropertyId: string,
    newPropertyData: Partial<PropertyEntity>,
  ) {
    const queryRunner: QueryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Fetch the original property including relations
      const originalProperty = await this.propertyRepository.findOne({
        where: { property_id: originalPropertyId },
        relations: ['property_owner_details', 'property_usage_details'], // Load related data
      });

      if (!originalProperty) {
        throw new NotFoundException('Original property not found');
      }

      // Check if new property number is unique
      const isUnique = await this.checkNewPropertyNumberUniqueness(
        newPropertyData.propertyNumber,
      );
      if (!isUnique.data) {
        throw new BadRequestException('Property number already exists');
      }

      // Create a new property
      const newProperty = this.propertyRepository.create({
        ...originalProperty, // Copy all original property fields
        property_id: undefined, // Prevent ID conflicts
        propertyNumber:
          newPropertyData.propertyNumber ?? originalProperty.propertyNumber, // Override property number
        parent_propertyNumber: originalProperty.propertyNumber, // Link to original
      });

      const savedProperty = await queryRunner.manager.save(newProperty);

      // 🔹 Transfer Owners (Check if data exists)
      if (
        newPropertyData.property_owner_details &&
        newPropertyData.property_owner_details.length > 0
      ) {
        const ownerIds = newPropertyData.property_owner_details.map(
          (owner) => owner.property_owner_details_id,
        );

        const owners = await this.ownerRepository.findBy({
          property_owner_details_id: In(ownerIds),
        });

        const newOwners = owners.map((owner) =>
          this.ownerRepository.create({
            ...owner,
            property: savedProperty, // Link to new property
          }),
        );
        await queryRunner.manager.save(newOwners);
      }

      // 🔹 Transfer Usage Details (Check if data exists)
      if (
        newPropertyData.property_usage_details &&
        newPropertyData.property_usage_details.length > 0
      ) {
        const usageIds = newPropertyData.property_usage_details.map(
          (usage) => usage.property_usage_details_id,
        );

        const usages = await this.usageRepository.findBy({
          property_usage_details_id: In(usageIds),
        });

        const newUsages = usages.map((usage) =>
          this.usageRepository.create({
            ...usage,
            property: savedProperty, // Link to new property
          }),
        );
        await queryRunner.manager.save(newUsages);
      }

      await queryRunner.commitTransaction();
      return savedProperty;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async genratePropertyNumbers(
    numbersToDivide: number,
    basePropertyNumber: string,
  ) {
    const generatedNumbers: { propertyNumber: string; isUnique: boolean }[] =
      [];
    let currentIndex = 1;
    console.log(
      'basePropertyNumber',
      basePropertyNumber,
      'numbersToDivide',
      numbersToDivide,
    );
    while (generatedNumbers.length < numbersToDivide) {
      const newPropertyNumber = `${basePropertyNumber}/${currentIndex}`;

      // Check if the number already exists in the database or in the generatedNumbers list
      const existsInDb = await this.propertyRepository.findOne({
        where: { propertyNumber: newPropertyNumber.trim() },
      });
      const existsInGenerated = generatedNumbers.some(
        (generated) => generated.propertyNumber === newPropertyNumber,
      );
      console.log('existsInDb', newPropertyNumber);

      if (!existsInDb && !existsInGenerated) {
        generatedNumbers.push({
          propertyNumber: newPropertyNumber,
          isUnique: true,
        });
      }

      currentIndex++;
    }
    return {
      message: 'Property numbers generated successfully',
      data: generatedNumbers, // Returns true if the number is unique
    };
  }

  async createFodProperties(propertyData: any): Promise<any> {
    const queryRunner: QueryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
  
    try {
      // Assuming actualPropertyId is the ID of the property to be updated
      const actualPropertyId = propertyData.actualPropertyId;
      const properties = propertyData.properties;
      const otherdata = propertyData?.otherdata || null;
      const deletedOwnerIds = propertyData?.deletedOwnerIds || null;

  
      // Retrieve the actual property to get street, ward, and zone
      const actualProperty = await queryRunner.manager.findOne(PropertyEntity, {
        where: { property_id: actualPropertyId },
        relations: ['street', 'ward', 'zone'], // Load related data
      });
  
      if (!actualProperty) {
        throw new Error('Actual property not found');
      }
  
      const { street, ward, zone } = actualProperty;
      console.log('actualProperty', actualProperty);
  
      // Retrieve the maximum sr_no from the existing properties
      console.log('maxSrNoResult go');
  
      const maxSrNoResult = await queryRunner.manager
        .createQueryBuilder(PropertyEntity, 'property')
        .select('MAX(property.sr_no)', 'maxSrNo')
        .getRawOne();
      console.log('maxSrNoResult', maxSrNoResult);
  
      const maxSrNo = maxSrNoResult ? parseInt(maxSrNoResult.maxSrNo, 10) : 0;
  
      // Update the first property number and its owner and usage details
      const firstProperty = properties[0];
      await queryRunner.manager.update(PropertyEntity, actualPropertyId, {
        propertyNumber: firstProperty.propertyNumber,
      });
  
      // Update owner details for the first property
      for (const owner of firstProperty.owners) {
        if (owner.isNew) {
                      let { property_owner_details_id, isNew, ...ownerData } = owner;

          const newOwner = queryRunner.manager.create(
            Property_Owner_Details_Entity,
            {
              ...ownerData,
              property: actualProperty,
            },
          );
          await queryRunner.manager.save(newOwner);
        } else {
          await queryRunner.manager.update(
            Property_Owner_Details_Entity,
            { property_owner_details_id: owner.property_owner_details_id },
            owner
          );
        }
      }
        
  
      // Update usage details for the first property
      for (const usage of firstProperty.usages) {
        if (usage.isNew) {
          console.log('isNew', usage.isNew);
          const { label, value, ...newUsageData } = usage;
  
          const newUsage = queryRunner.manager.create(
            Property_Usage_Details_Entity,
            {
              ...newUsageData,
              property: actualProperty,
            },
          );
          await queryRunner.manager.save(newUsage);
        } else {
          console.log('isNew', usage.isNew);
  
          const {
            property_usage_details_id,
            label,
            isNew,
            value,
            ...updateData
          } = usage;
          // Perform the update operation
          const result = await queryRunner.manager.update(
            Property_Usage_Details_Entity,
            { property_usage_details_id: property_usage_details_id },
            {
              length: updateData?.length || null,
              width: updateData?.width || null,
              are_sq_ft: updateData?.are_sq_ft || null,
              are_sq_meter: updateData?.are_sq_meter || null,
              property: actualProperty,
            },
          );
  
          // Log the result of the update operation
          console.log('Update Result:', result);
        }
      }
  
      // Create new properties for the remaining property numbers
      for (let i = 1; i < properties.length; i++) {
        const property = properties[i];
        const newProperty = queryRunner.manager.create(PropertyEntity, {
          propertyNumber: property.propertyNumber,
          parent_propertyId: actualPropertyId,
          sr_no: maxSrNo + i, // Increment sr_no for each new property
          street: street,
          ward: ward,
          zone: zone,
        });
        await queryRunner.manager.save(newProperty);
  
        // Update owner details for the new property
        for (const owner of property.owners) {
          if (owner.isNew) {

            let { property_owner_details_id, isNew, ...ownerData } = owner;
            const newOwner = queryRunner.manager.create(
              Property_Owner_Details_Entity,
              {
                ...ownerData,
                property: newProperty,
              },
            );
            await queryRunner.manager.save(newOwner);
          } else {

            await queryRunner.manager.update(
              Property_Owner_Details_Entity,
              { property_owner_details_id: owner.property_owner_details_id },
              {
              ...owner,
                              property: newProperty,
              }

            );
          }
        }
      
  
        // Update usage details for the new property
        for (const usage of property.usages) {
          if (usage.isNew) {
            console.log('isNew', usage.isNew);
            const { label, value, ...newUsageData } = usage;
  
            const newUsage = queryRunner.manager.create(
              Property_Usage_Details_Entity,
              {
                ...newUsageData,
                property: newProperty,
              },
            );
            await queryRunner.manager.save(newUsage);
          } else {
            console.log('isNew', usage.isNew);
  
            const {
              property_usage_details_id,
              label,
              isNew,
              value,
              ...updateData
            } = usage;
            // Perform the update operation
            const result = await queryRunner.manager.update(
              Property_Usage_Details_Entity,
              { property_usage_details_id: property_usage_details_id },
              {
                length: updateData?.length || null,
                width: updateData?.width || null,
                are_sq_ft: updateData?.are_sq_ft || null,
                are_sq_meter: updateData?.are_sq_meter || null,
                property: newProperty,
              },
            );
  
            // Log the result of the update operation
            console.log('Update Result:', result);
          }
        }
      }
      for (const ownerId of deletedOwnerIds) {
        await this.propertyOwnerService.deleteOwner(ownerId);
      }
  
      if (otherdata) {
        const newFodDetails = queryRunner.manager.create(Property_Fod_Details_Entity, {
          property: actualProperty,
          new_property_numbers: properties.map(prop => prop.propertyNumber),
          reason: otherdata.reason,
          photo_image_paths: otherdata.photo_image_paths,
          document_image_path: otherdata.document_image_path,
          user_email_id: otherdata.user_email_id,
        });
        await queryRunner.manager.save(newFodDetails);
      }
  
      await queryRunner.commitTransaction();
      return { message: 'Food properties created successfully' };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error creating food properties:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  
}
