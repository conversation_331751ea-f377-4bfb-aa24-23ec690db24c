

import axios from "axios";
import Api from "./ApiServices";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const apiClient = axios.create({
  baseURL: baseURL,
});

apiClient.interceptors.request.use(
  (config) => {
    const token = Api.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

class DashboardService {
  getRecentTransactions = async () => {
    try {
      const response = await apiClient.get(`/v1/dashboard/recent-transactions`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  getTaxCollectionTrend = async (financialYear: string) => {
    try {
      const response = await apiClient.get(`/v1/dashboard/tax-collection-trend?financialYear=${financialYear}`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  getWardPerformance = async () => {
    try {
      const response = await apiClient.get(`/v1/dashboard/ward-performance`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  getDefaulterAnalysis = async () => {
    try {
      const response = await apiClient.get(`/v1/dashboard/defaulter-analysis`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };
}

export default new DashboardService();

