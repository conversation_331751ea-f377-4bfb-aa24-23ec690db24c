// helperFuntion.ts
export function formatTimestamp(isoString: string): string {
  if (!isoString) {
    return "—";
  }

  const date = new Date(isoString);

  // Check if the date is invalid
  if (isNaN(date.getTime())) {
    return "—";
  }

  const day = date.getDate();
  const monthNames = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();

  return `${day} ${month} ${year}`;
}
