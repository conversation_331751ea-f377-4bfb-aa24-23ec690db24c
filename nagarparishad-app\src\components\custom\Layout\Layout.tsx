import React, { ReactNode } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Navbar from "@/components/custom/Navbar";
import Header from "@/components/custom/Header";
import Footer from "@/components/custom/Footer"; // Import your footer component

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = () => {
  const { pathname } = useLocation();

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      {pathname === "/login" || pathname === "/payment" ? " " : <Header />}
      <main className=" flex flex-col flex-1">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
