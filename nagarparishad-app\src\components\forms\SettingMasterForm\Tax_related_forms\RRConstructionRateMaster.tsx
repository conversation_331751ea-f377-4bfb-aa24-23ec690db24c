import React, { useState, useContext, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { GlobalContext } from "@/context/GlobalContext";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { useConstructionRateController } from "@/controller/tax/ConstructionRateController";
import {
  ConstructionRateSetting,
  ConstructionRateUpdateApi,
} from "@/model/tax/constructionRate";
import { toast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import AsyncSelect from "@/components/ui/react-select";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ReactselectInterface } from "@/model/global-master";
import { PropertyClassMasterListApiOject } from "@/model/PropertyClassMaster";
import { z } from "zod";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { propertyTypeClassFilter } from "@/components/globalcomponent/TanstackFilter";
import { Loader } from "@/components/globalcomponent/Loader";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ResponseData } from "@/model/auth/authServices";
import Api from "@/services/ApiServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

interface constructionRateInterface {
  editData?: ConstructionRateUpdateApi;
}

const RRConstructionRateMaster = ({ editData }: constructionRateInterface) => {
  const { t } = useTranslation();
  const {
    constructionRateList,
    updateConstructionRate,
    createConstructionRate,
    deleteConstructionRate,
    propertyLoading,
  } = useConstructionRateController();
  const constructionRateLists = constructionRateList?.data || []; // Access the data array
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const userRef = useRef(null);
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
    const schema = z.object({
      financial_year: z.string().min(1, t("errorsRequiredField")),
   value: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),      status: z.string().min(1, t("errorsRequiredField")),
      property_type_class_id: z.string().min(1, t("errorsRequiredField")),
    });
  // const [formData, setFormData] = useState({
  //   financial_year: "",
  //   value: 0,
  //   status: "Active",
  //   property_type_class_id: "",
  // });
  const { propertyClassList } = usePropertyClassMasterController();
  const Form = FormProvider;

  const propertyClassOption: ReactselectInterface[] = propertyClassList?.map(
    (propertyClass: PropertyClassMasterListApiOject) => ({
      value: propertyClass.property_type_class_id,
      label: propertyClass.property_type_class,
    })
  );
  // const { setMasterComponent, setOpen } = useContext(GlobalContext);
     const { canPerformAction } = usePermissions();
      const canRead = canPerformAction(ModuleName.Setting, FormName.RRConstructionRate,Action.CanRead );
      const canUpdate = canPerformAction(ModuleName.Setting, FormName.RRConstructionRate,Action.CanUpdate );
      const CanCreate = canPerformAction(ModuleName.Setting, FormName.RRConstructionRate,Action.CanCreate );
      const CanDelete = canPerformAction(ModuleName.Setting, FormName.RRConstructionRate,Action.CanDelete );

  const [financialYears, setFinancialYears] = useState([]);
  const dynamicValues = {
    name: t("setting.constructionRateTitle"),
  };
  useEffect(() => {
    const fetchConstructionRates = async () => {
      try {
        setIsLoading(true); // Set loading to true before fetch
        await constructionRateList; // Assuming constructionRateList fetches data
      } finally {
        setIsLoading(false); // Set loading to false after data is fetched
      }
    };
    fetchConstructionRates();
  }, [constructionRateList]);

  // const handleInputChange = (e) => {
  //   const { name, value } = e.target;
  //     [name]: name === "value" ? Number(value) : value,

  // };

  // const handleSelectChange = (name, value) => {
  //   setFormData({ ...formData, [name]: value });
  // };

  // const handleZoneChange = (selectedOption) => {
  //   setFormData({ ...formData, property_type_class_id: selectedOption.value });
  // };
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      financial_year:editData?.financial_year||"",
      value: editData?.value || null,
      status: editData?.status || "Active",
      property_type_class_id: editData?.property_type_class_id || "",
    },
  });
  const {  formState: { errors }, reset, control } = form;

  const checkDuplicate = (year: string, propertyClassId: string) => {
    return constructionRateLists.some(
      (item) =>
        item.financial_year === year &&
        item.property_type_class_id.property_type_class_id ===
          propertyClassId &&
        (!isEditing || item.rr_construction_rate_id !== editingId)
    );
  };


  const handleEdit = (item: ConstructionRateSetting) => {
    setOpen(true);
    setIsEditing(true);
    setEditingId(item.rr_construction_rate_id);

    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      financial_year: item.financial_year,
      value: item.value,
      status: item.status,
      property_type_class_id: item.property_type_class_id.property_type_class_id,
    });

  };
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] =
    useState<ConstructionRateSetting | null>(null);

  function handleDelete(item: ConstructionRateSetting): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDeletes = async () => {
    if (selectedItem) {
      try {
        await deleteConstructionRate(selectedItem.rr_construction_rate_id);
        toast({
          title: t("deleteSuccess", { name: selectedItem.financial_year }),
          variant: "success",
        });
      } catch (error) {
        toast({
          title: error.message,
          variant: "destructive",
        });
      }
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteConstructionRate(selectedItem.rr_construction_rate_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete",dynamicValues ),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const handleDeletes = async (item: ConstructionRateSetting) => {
    try {
      await deleteConstructionRate(item.rr_construction_rate_id);
      toast({
        title: t("deleteSuccess", { name: item.financial_year }),
        variant: "success",
      });
    } catch (error) {
      toast({
        title: error.message,
        variant: "destructive",
      });
    }
  };
  // const resetForm = () => {
  //   setFormData({
  //     financial_year: "",
  //     value: 0,
  //     status: "Active",
  //     property_type_class_id: "",
  //   });
  //   setIsEditing(false);
  //   setEditingId(null);
  // };

  // const handleSubmit = async (e) => {
  //   console.log("parseResult: ");

  //   e.preventDefault();
  //   console.log("parseResult: ee", e);

  //   const parseResult = schema.safeParse("");
  //   console.log("parseResult: " + parseResult);
  //   if (!parseResult.success) {
  //     toast({
  //       title: t("errorsRequiredField"),
  //       variant: "destructive",
  //     });
  //     return;
  //   }

  //   // Check for duplicates
  //   // if (
  //   //   checkDuplicate(formData.financial_year, formData.property_type_class_id)
  //   // ) {
  //   //   toast({
  //   //     title: t(
  //   //       "या वर्षासाठी आणि  मालमत्ता वापर उपप्रकारसाठी दर आधीच अस्तित्वात आहे"
  //   //     ),
  //   //     variant: "destructive",
  //   //   });
  //   //   return;
  //   // }

  //   try {
  //     if (isEditing) {
  //       await updateConstructionRate({
  //         id: editingId,
  //         payload: formData,
  //       });
  //       toast({
  //         title: t("api.formupdate"),
  //         variant: "success",
  //       });
  //     } else {
  //       console.log("created formss");
  //       await createConstructionRate(formData);
  //       toast({
  //         title: t("api.formcreate"),
  //         variant: "success",
  //       });
  //       resetForm();
  //     }

  //     resetForm();
  //   } catch (error) {
  //     toast({
  //       title: error.message,
  //       variant: "destructive",
  //     });
  //   }
  // };

  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();

    console.log("Form event:", e);


    console.log("Form data:", data);


    if (
      checkDuplicate(data.financial_year, data.property_type_class_id)
    ) {
      toast({
        title: t(
          "या वर्षासाठी आणि  मालमत्ता वापर उपप्रकारसाठी दर आधीच अस्तित्वात आहे"
        ),
        variant: "destructive",
      });
      return;
    }

      if (isEditing) {
        updateConstructionRate(
          {
            id: editingId,
            payload: data,
          },
          {
            onSuccess: (response) => {
              toast({
                title: t("api.formupdate",dynamicValues),
                variant: "success",
              });
              reset({
                financial_year:"",
                value: '',
                status:"Active",
                property_type_class_id:"",
              });
              setIsEditing(false);
              setEditingId(null);
            },
            onError: (error) => {
              console.log("error",error)
              toast({
                title:"error",
                variant: "destructive",
              });
            },
          }
        );
      } else {
        console.log("in creationprocess");
        createConstructionRate(data, {
          onSuccess: (response) => {
            toast({
              title: t("api.formcreate",dynamicValues),
              variant: "success",
            });
           reset({
              financial_year:"",
              value: '',
              status:"Active",
              property_type_class_id:"",
            });
          },
          onError: (error) => {
            console.log("error", error)
            toast({
              title: error.message || t("api.error"),
              variant: "destructive",
            });
          },
        });

    }
  };
  const onError = (errors) => {
    console.log("Validation errors:", errors); // To log any validation errors
  };

  useEffect(() => {
    fetchFinancialYears();
  }, []);
  const fetchFinancialYears = async () => {
    try {
      console.time("Fetch financial years");
      const response = await Api.fyYears();
      console.timeEnd("Fetch financial years");

      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  const columns: ColumnDef<ConstructionRateSetting>[] = [
    {
      accessorKey: "reassessmentRange",
      header: t("setting.reassessmentRange"),
      cell: ({ row }) => {
        const startRange = row.original?.reassessmentRange?.start_range;
        const endRange = row.original?.reassessmentRange?.end_range;
        return <div>{startRange && endRange ? `${startRange} to ${endRange}` : row.original?.financial_year}</div>;
      },
    },
    // {
    //   accessorKey: "property_type_class",
    //   header: t("मालमत्ता प्रकार विभाग"),
    //   cell: ({ row }) => <div>{row.original.property_type_class_id.property_type_class}</div>,
    // },
    {
      accessorFn: (row) =>
        row.property_type_class_id?.property_type_class || "", // Directly access the value here
      id: "property_type_class",
      // accessorKey: "property_type_class",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("मालमत्ता प्रकार विभाग")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="">
          {row.original?.property_type_class_id.property_type_class}
        </div>
      ),
      filterFn: propertyTypeClassFilter,
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: t("setting.status"),
      cell: ({ row }) => <div>{row.original?.status}</div>,
    },
    {
      accessorKey: "value",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("setting.value")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original?.value}</div>,
    },

     ...(canUpdate || CanDelete
          ? [
              {
                accessorKey: "actions",
                header: t("Actions"),
                cell: ({ row }) => (
                  <>
                    {canUpdate && (
                      <button
                        className="h-8 w-8 p-0"
                        onClick={() => handleEdit(row.original)}
                      >
                        <Edit className="text-blue-500" />
                      </button>
                    )}
                    {CanDelete && (
                      <button
                        className="h-8 w-8 p-0 ml-2"
                        onClick={() => handleDelete(row.original)}
                      >
                        <Trash className="text-red-500" />
                      </button>
                    )}
                  </>
                ),
              },
            ]
          : []),
  ];

  return (
    <div className="w-full h-fit p-6" ref={userRef && userRef}>
      <p className="w-full flex items-center justify-between text-2xl font-semibold mb-2">
        {t("setting.constructionRateTitle")}
      </p>
      {(canUpdate || CanCreate) && (
      <WhiteContainer>
      <Form {...form}>

        <form onSubmit={form.handleSubmit(onSubmit, onError)}>
          <div className="grid md:grid-cols-5 gap-x-3">
            <div>
              {/* <Label>
                {t("financialYear")}
                <span className="ml-1 text-red-500">*</span>
              </Label> */}
               <FormField
                control={form.control}
                name="financial_year"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>                {t("financialYear")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Select
                {...field}
                onValueChange={field.onChange}
                      defaultValue={field.value}
              >
                <FormControl>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("selectYear")} />
                </SelectTrigger>
                </FormControl>

                <SelectContent>
                  {financialYears.map((year) => (
                    <SelectItem
                      key={year.financial_year_range}
                      value={year.financial_year_range}
                    >
                      {year.financial_year_range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.financial_year && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>

            <div>
              {/* <Label>{t("मालमत्ता प्रकार विभाग")}</Label> */}
              <FormField
                control={form.control}
                name="property_type_class_id"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel> {t("master.propertyclass")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
                    <Controller
                          control={form.control}
                          name="property_type_class_id"
                          render={({ field: controllerField }) => (
              <AsyncSelect

                placeholder={t("मालमत्ता प्रकार विभाग")}
                className="mt-1 h-[2.5rem]"
                value={
                  propertyClassOption.find(
                    (option: any) =>
                      option.value === controllerField.value
                  ) || null
                }
                options={propertyClassOption}
                colourOptions={propertyClassOption}
                onChange={(selectedOption: any) => {
                  controllerField.onChange(selectedOption.value)
                }}
                 />
                )}
              />
              {errors.property_type_class_id && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>
            <div>
              {/* <Label>{t("setting.status")}</Label> */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>                {t("setting.status")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Select
                {...field}
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("selectStatus")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>

            <div>
              {/* <Label>{t("setting.value")}</Label> */}
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("setting.value")}
                      <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Input
                {...field}
                type="number"
                id="value"
                name="value"
                value={field.value}
                // onChange={handleInputChange}
                onChange={(event) => field.onChange(Number(event.target.value))}
                placeholder={t("setting.value")}
                className="mt-1 block w-full"
                required
                min="0"
              />
              {errors.value && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid  mb-1 ml-1 max-md:flex max-md:justify-end  pt-[32px] ">
              <Button type="submit">
                {isEditing ? t("update") : t("add")}
              </Button>
            </div>
          </div>
        </form>
        </Form>
        <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
            {" "}
            {t("allFieldAreRequire")}{" "}
          </p>
      </WhiteContainer>
      )}

      <WhiteContainer className="mt-5">
        {isLoading ? (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        ) : (
          <TanStackTable
            columns={columns}
            data={constructionRateList}
            searchKey={"property_type_class"}
            searchColumn={"property_type_class"}
            loader={propertyLoading ? true : false}
          />
        )}
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.financial_year}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default RRConstructionRateMaster;


