import React, { useEffect, useState } from "react";
import WhiteContainer from "../custom/WhiteContainer";
import { Input } from "../ui/input";
import { t } from "i18next";
import { <PERSON><PERSON> } from "../ui/button";
import TanStackTable from "../globalcomponent/tanstacktable";
import { Printer } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import AsyncSelect from "@/components/ui/react-select";
import { PROPERTY } from "@/constant/config/api.config";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader } from "../globalcomponent/Loader";
import { toast } from "../ui/use-toast";
import { Label } from "../ui/label";
import axios from "axios";
import Api from "@/services/ApiServices";
import ReportApi from "@/services/ReportServices";
import { useNavigate } from "react-router-dom";
import PropertyApi from "@/services/PropertyServices";

const VarshikAkarniNineReport = () => {
  const navigate = useNavigate();
  const [printloading, setPrintLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [oldPropertyNumber, setOldPropertyNumber] = useState("");
  const [propertyNumber, setPropertyNumber] = useState("");
  const [ownerName, setOwnerName] = useState("");
  const [wardName, setWardName] = useState(null);
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const [selectedValue, setSelectedValue] = useState("");
  const [taxData, setTaxData] = useState([]);
  const [taxTypes, setTaxTypes] = useState([]);
  const { wardList } = useWardMasterController();
  const [loading, setLoading] = useState(false);
  const [searchOn, setSearchOn] = useState("");
  const [checkWarshikKarGenerated, setCheckWarshikKarGenerated] = useState(0);
  const [wardStatistics, setWardStatistics] = useState(null);
  const [wardRemainingCount, setWardRemainingCount] = useState(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [loadingRows, setLoadingRows] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const [showPrintAll, setShowPrintALl] = useState(false);
  const [generationProgress, setGenerationProgress] = useState({
    totalProperties: 0,
    processedProperties: 0,
    currentWard: "",
    percentage: 0,
  });

  const [pagination, setPagination] = useState({
    totalPages: 0,
    totalRecords: 0,
  });

  const wardOptions = wardList?.map((ward) => ({
    value: ward.ward_id,
    label: ward.ward_name,
  }));

  const loadWardOptions = (inputValue, callback) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };

  const [financialYears, setFinancialYears] = useState([]);
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
        const currentYear = response.data.data.find((year) => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  // Function to check ward remaining count
  const checkWardRemainingCount = async (wardName: string) => {
    if (!selectedFinancialYear || !wardName) return;

    try {
      const response = await Api.getWarshikKarRemainingCount(
        wardName,
        selectedFinancialYear
      );
      if (response.status && response.data.statusCode === 200) {
        setWardRemainingCount(response.data.data);
      } else {
        setWardRemainingCount(null);
      }
    } catch (error) {
      console.error("Error checking ward remaining count:", error);
      setWardRemainingCount(null);
    }
  };

  const nineReport = async () => {
    if (!selectedFinancialYear) {
      toast({
        title: `${t("selectFinancialYear")}`,
        variant: "destructive",
      });
      return;
    }

    const searchParams: { [key: string]: string | undefined } = {};

    if (wardName) {
      searchParams.searchOn = "ward";
      searchParams.ward = wardName?.label;
    }

    if (propertyNumber) {
      searchParams.searchOn = "propertyNumber";
      searchParams.value = propertyNumber;
    } else if (oldPropertyNumber) {
      searchParams.searchOn = "old_propertyNumber";
      searchParams.value = oldPropertyNumber;
    } else if (ownerName) {
      searchParams.searchOn = "name";
      searchParams.value = ownerName;
    }

    const queryString = new URLSearchParams(
      searchParams as Record<string, string>
    ).toString();
    setLoading(true);
    try {
      const response = await ReportApi.getVarshiKarAkarniReport(
        queryString,
        selectedFinancialYear,
        page,
        limit
      );

      if (
        response.data.statusCode === 200 &&
        response.data.message === "Data Not found" &&
        response.data.data === 0
      ) {
        setTaxData([]);
        setShowPrintALl(false);
        setCheckWarshikKarGenerated(0);
        setWardStatistics(null);
        setOpen(true);
        toast({
          title: "No data found.",
          variant: "destructive",
        });
        return;
      }

      if (
        response.data.data &&
        response.data.data.checkData &&
        response.data.data.checkData.length === 0
      ) {
        setTaxData([]);
        setShowPrintALl(false);

        setCheckWarshikKarGenerated(
          response.data.data.checkWarshikKarGenerated || 0
        );
        setWardStatistics(response.data.data.wardStatistics || null);
        setOpen(true);
        toast({
          title: "No data found.",
          variant: "destructive",
        });
        return;
      }

      const data = response.data?.data;
      if (data.checkData.length > 1) {
        setShowPrintALl(true);
      } else {
        setShowPrintALl(false);
      }
      setTaxData(data.checkData || []);
      setTaxTypes(data.tax_types || {});
      setCheckWarshikKarGenerated(data.checkWarshikKarGenerated || 0);
      setWardStatistics(data.wardStatistics || null);
      setPagination({
        totalPages: data.totalPages,
        totalRecords: data.total,
      });
      setOpen(true);

      // If searching by ward, check remaining count

      if (wardName?.label && limit == 10 && page == 1) {
        await checkWardRemainingCount(wardName.label);
      } else {
        setWardRemainingCount(null);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast({
        title: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateGenerationProgress = (
    processedCount,
    totalCount,
    currentWard
  ) => {
    const percentage = (processedCount / totalCount) * 100;
    setGenerationProgress({
      totalProperties: totalCount,
      processedProperties: processedCount,
      currentWard: currentWard,
      percentage: percentage,
    });
  };

  const handleGenerateWarshikKar = async () => {
    if (!selectedFinancialYear) {
      toast({
        title: "कृपया आर्थिक वर्ष निवडा",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress({
      totalProperties: 0,
      processedProperties: 0,
      currentWard: "",
      percentage: 0,
    });

    try {
      // Prepare parameters based on search criteria
      const params = {
        financialYear: selectedFinancialYear,
        ward: wardName?.label || null,
        propertyNumber: propertyNumber || null,
        oldPropertyNumber: oldPropertyNumber || null,
        ownerName: ownerName || null,
      };

      const response = await Api.processWarshikKarAkarni(params);

      if (response.data.statusCode === 200) {
        const { totalProperties, processedProperties, currentWard } =
          response.data.data;

        updateGenerationProgress(
          processedProperties,
          totalProperties,
          currentWard
        );

        setTimeout(() => {
          toast({
            title: "वार्षिक कर यशस्वीरित्या तयार झाला",
            variant: "success",
          });
          setIsGenerating(false);
          nineReport();
        }, 1000);
      } else {
        throw new Error(response.data.message || "Failed to generate records");
      }
    } catch (error) {
      console.error("Error generating Warshik Kar:", error);
      toast({
        title: "वार्षिक कर तयार करण्यात अयशस्वी",
        description: error.message || "अनपेक्षित त्रुटी आली",
        variant: "destructive",
      });
      setIsGenerating(false);
    }
  };

  const columns = [
    {
      accessorKey: "srNo",
      header: `${t("SrNo")}`,
      cell: ({ row }) => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "propertyNumber",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.propertyNo")}
        </Button>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.original.propertyNumber}</div>
      ),
    },
    {
      accessorKey: "old_propertyNumber",
      header: t("property.oldPropertyNumber"),
      cell: ({ row }) => (
        <div className="capitalize min-w-[115px]">
          {row.original.old_propertyNumber}
        </div>
      ),
    },
    {
      accessorKey: "ownerName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.ownerName")}
        </Button>
      ),
      cell: ({ row }) => {
        const owners = row.original?.property_owner_details;
        const primaryOwners = owners?.filter(
          (owner) => owner?.owner_type?.owner_type
        );

        return (
          <div className="capitalize relative">
            {primaryOwners?.length > 0 ? (
              <>
                {primaryOwners.slice(0, 1).map((owner, index) => (
                  <p
                    key={owner?.property_owner_details_id}
                    className="text-nowrap"
                  >
                    {owner.name}
                    {index < primaryOwners.length - 1 && ", "}
                  </p>
                ))}
                {primaryOwners.length > 1 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-[#f0eded]"
                      >
                        +{primaryOwners.length - 1}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-fit px-3 py-1">
                      {primaryOwners.slice(1).map((owner) => (
                        <DropdownMenuItem key={owner.property_owner_details_id}>
                          {owner.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            ) : (
              "--"
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "wardName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("wardName")}
        </Button>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.original?.ward?.ward_name}</div>
      ),
    },
    {
      accessorKey: "mobileNumber",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("Mobile")}
        </Button>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.original.mobile_number || "--"}</div>
      ),
    },
    {
      accessorKey: "total_prev",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          मागणी
        </Button>
      ),
      cell: ({ row }) => {
        const warshikKar = row.original.warshikKar[0];
        return <div className="capitalize">{warshikKar.total_tax || 0}</div>;
      },
    },
    {
      accessorKey: "total",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          भरलेलाला कर
        </Button>
      ),
      cell: ({ row }) => {
        const totalPaidTax = row.original.TotalTax;
        return <div className="capitalize">{totalPaidTax || 0}</div>;
      },
    },
    {
      accessorKey: "all_property_tax_sum_prev",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          घर {t("मागिल")}
        </Button>
      ),
      cell: ({ row }) => {
        const warshikKar = row.original.warshikKar[0];
        return (
          <div className="capitalize">
            {warshikKar.all_property_tax_sum || 0}
          </div>
        );
      },
    },
    {
      accessorKey: "all_property_tax_sum_cur",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          घर {t("चालू")}
        </Button>
      ),
      cell: ({ row }) => {
        const warshikKar = row.original.warshikKar[0];
        return (
          <div className="capitalize">
            {warshikKar.all_property_tax_sum_current || 0}
          </div>
        );
      },
    },
    {
      accessorKey: "all_property_tax_sum",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          घर
        </Button>
      ),
      cell: ({ row }) => {
        const warshikKar = row.original.warshikKar[0];
        return (
          <div className="capitalize">
            {warshikKar.all_property_tax_sum_current || 0}
          </div>
        );
      },
    },
    ...Object.keys(taxTypes).flatMap((taxKey) => [
      {
        accessorKey: `${taxKey}_prev`,
        header: ({ column }) => (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {taxTypes[taxKey]}
            {t("मागिल")}
          </Button>
        ),
        cell: ({ row }) => {
          const warshikKar = row.original.warshikKar[0];
          return (
            <div className="capitalize">
              {warshikKar[`${taxKey}_previous`] || 0}
            </div>
          );
        },
      },
      {
        accessorKey: `${taxKey}_cur`,
        header: ({ column }) => (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {taxTypes[taxKey]}
            {t("चालू")}
          </Button>
        ),
        cell: ({ row }) => {
          const warshikKar = row.original.warshikKar[0];
          return (
            <div className="capitalize">
              {warshikKar[`${taxKey}_current`] || 0}
            </div>
          );
        },
      },
      {
        accessorKey: taxKey,
        header: ({ column }) => (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {taxTypes[taxKey]}
          </Button>
        ),
        cell: ({ row }) => {
          const warshikKar = row.original.warshikKar[0];
          return (
            <div className="capitalize">{warshikKar[`${taxKey}`] || 0}</div>
          );
        },
      },
    ]),
    {
      accessorKey: t("Actions"),
      enableHiding: false,
      cell: ({ row }) => {
        const rowId = row.original.propertyNumber;
        const isLoading = loadingRows.includes(rowId);

        return (
          <div className="text-nowrap">
            <Button
              className={`bg-LightBlue text-BlueText border border-Secondary hover:bg-LightBlue px-4 py-1 min-w-fit h-fit ${
                printloading ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={() => handlePrintTax(row?.original)}
              disabled={isLoading}
            >
              <Printer className={`w-5 ${isLoading ? "animate-bounce" : ""}`} />
            </Button>
          </div>
        );
      },
    },
  ];

  const handleOldNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOldPropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyNumber("");
    setOwnerName("");
    setWardName(null);
    setSearchOn("oldPropertyNumber");
  };

  const handlePrintAll = async () => {
    setPrintLoading(true);

    try {
      const params: any = {
        fy: selectedFinancialYear,
      };

      if (wardName) {
        params.ward = wardName.label;
      }
      if (ownerName) {
        params.name = ownerName;
      }

      const response = await ReportApi.printAllNamunaNineReport(params);

      if (response.status && response.data) {
        toast({
          title: "All tax reports generated successfully.",
          variant: "success",
        });
      } else {
        toast({
          title: "Failed to generate all tax reports. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error generating all tax reports. Please try again.",
        variant: "destructive",
      });
      console.error("Error printing all taxes:", error);
    } finally {
      setPrintLoading(false);
    }
  };

  const handlePropertyNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPropertyNumber(e.target.value.trim().toUpperCase());
    setOldPropertyNumber("");
    setOwnerName("");
    setWardName(null);
    setSearchOn("propertyNumber");
  };

  const handlePrintTax = async (item: any) => {
    const rowId = item.propertyNumber;
    setLoadingRows((prev) => [...prev, rowId]);
    const newTab = window.open("", "_blank");

    try {
      const params = {
        searchOn: "propertyNumber",
        value: item.propertyNumber,
        fy: selectedFinancialYear,
      };

      const response = await ReportApi.printNamunaNineReport(params);

      console.log("response--<s---", response);

      if (response.status && response.data) {
        console.log("response--<s", response.data);

        const blob = new Blob([response.data.data], {
          type: "application/pdf",
        });
        const url = URL.createObjectURL(blob);
        console.log("url", url);

        if (newTab) {
          newTab.location.href = url; // Set the PDF blob URL
          newTab.focus();
        }

        toast({
          title: "Tax report generated successfully.",
          variant: "success",
        });
        toast({
          title: "Tax printed successfully.",
          variant: "success",
        });
      } else {
        newTab?.close(); // Close if no data
        toast({
          title: "No Payments found",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      newTab?.close(); // Close on error
      toast({
        title: "Please try again.",
        variant: "destructive",
      });
      console.error("Error printing tax:", error);
    } finally {
      setLoadingRows((prev) => prev.filter((id) => id !== rowId));
    }
  };

  const handleWardChange = (selectedOption: any) => {
    setWardName(selectedOption);
    setPropertyNumber("");
    setOldPropertyNumber("");
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
  };

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins w-full ml-3">
          नमुना 9
        </h1>
        <WhiteContainer>
          <div className="grid md:grid-cols-5 gap-x-3 gap-2 items-end">
            <div className="grid-cols-subgrid">
              <Label>
                {t("financialYear")}
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select
                onValueChange={setSelectedFinancialYear}
                value={selectedFinancialYear}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("selectYear")} />
                </SelectTrigger>
                <SelectContent>
                  {financialYears.map((year) => (
                    <SelectItem
                      key={year.financial_year_range}
                      value={year.financial_year_range}
                    >
                      {year.financial_year_range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("propertyView.ward")}</Label>
              <AsyncSelect
                placeholder={t("propertyLocationDetailsForm.ward")}
                className="mt-1 h-[2.5rem]"
                loadOptions={loadWardOptions}
                defaultOptions={wardOptions}
                value={wardName}
                onChange={handleWardChange}
                onKeyDown={(e) => e.key === "Enter" && nineReport()}
                colourOptions={[]}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("property.propertyNumberColumn")}</Label>
              <Input
                name="propertyNumber"
                id="propertyNumber"
                value={propertyNumber}
                onChange={handlePropertyNumberChange}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("property.propertyNumberColumn")}
                onKeyDown={(e) => e.key === "Enter" && nineReport()}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("property.oldPropertyNumber")}</Label>
              <Input
                name="oldPropertyNumber"
                id="oldPropertyNumber"
                onChange={handleOldNumberChange}
                value={oldPropertyNumber}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("property.oldPropertyNumber")}
                onKeyDown={(e) => e.key === "Enter" && nineReport()}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("नाव")}</Label>
              <Input
                name="ownername"
                id="ownername"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setOwnerName(e.target.value.trim())
                }
                value={ownerName}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("नाव")}
                onKeyDown={(e) => e.key === "Enter" && nineReport()}
              />
            </div>
          </div>
          <div className="flex items-end justify-end w-full mt-2">
            <Button variant="submit" className="ml-2" onClick={nineReport}>
              {t("search")}
            </Button>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        )}
        {open && !loading && wardRemainingCount?.remainingProperties > 0 && (
          <div className="text-center py-4">
            {/* Show generate button based on ward remaining count or general check */}
            {(() => {
              // If we have ward remaining count and there are remaining properties
              if (
                wardRemainingCount &&
                wardRemainingCount?.remainingProperties > 0
              ) {
                return true;
              }
              // If we have ward statistics and there are remaining properties
              if (
                wardStatistics &&
                wardStatistics.remainingPropertiesForWarshikKar > 0
              ) {
                return true;
              }
              // If no ward data but checkWarshikKarGenerated is 0 (for non-ward searches)
              if (
                !wardRemainingCount &&
                !wardStatistics &&
                checkWarshikKarGenerated === 0
              ) {
                return true;
              }
              return false;
            })() ? (
              <>
                {!isGenerating ? (
                  <div className="space-y-4">
                    <Button
                      onClick={handleGenerateWarshikKar}
                      className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                      {wardName
                        ? `${wardName.label} वॉर्डसाठी वार्षिक कर तयार करा`
                        : propertyNumber
                          ? `गुणधर्म क्र. ${propertyNumber} साठी वार्षिक कर तयार करा`
                          : oldPropertyNumber
                            ? `जुना गुणधर्म क्र. ${oldPropertyNumber} साठी वार्षिक कर तयार करा`
                            : ownerName
                              ? `${ownerName} साठी वार्षिक कर तयार करा`
                              : "वार्षिक कर तयार करा"}
                    </Button>

                    {/* Show ward statistics if available */}
                    {(wardRemainingCount || wardStatistics) && (
                      <div className="text-sm text-gray-600 mt-3 p-3 bg-blue-50 rounded border">
                        <p className="font-semibold text-blue-800 mb-2">
                          वॉर्ड{" "}
                          {wardRemainingCount?.wardName ||
                            wardStatistics?.wardName}{" "}
                          माहिती:
                        </p>
                        <div className="grid grid-cols-3 gap-2 text-center">
                          <div>
                            <p className="font-medium text-gray-800">
                              {wardRemainingCount?.totalProperties ||
                                wardStatistics?.totalPropertiesWithMilkatKar}
                            </p>
                            <p className="text-xs">एकूण मालमत्ता</p>
                          </div>
                          <div>
                            <p className="font-medium text-green-600">
                              {wardRemainingCount?.completedProperties ||
                                wardStatistics?.propertiesWithWarshikKar}
                            </p>
                            <p className="text-xs">तयार झालेले</p>
                          </div>
                          <div>
                            <p className="font-medium text-orange-600">
                              {wardRemainingCount?.remainingProperties ||
                                wardStatistics?.remainingPropertiesForWarshikKar}
                            </p>
                            <p className="text-xs">बाकी</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center gap-2">
                      <Loader className="w-5 h-5" />
                      <span className="text-lg font-medium">
                        वार्षिक कर तयार करत आहे...
                      </span>
                    </div>

                    {generationProgress.totalProperties > 0 && (
                      <div className="max-w-md mx-auto space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>प्रगती:</span>
                          <span>
                            {generationProgress.processedProperties} /{" "}
                            {generationProgress.totalProperties}
                          </span>
                        </div>

                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                            style={{
                              width: `${generationProgress.percentage}%`,
                            }}
                          ></div>
                        </div>

                        <div className="text-center text-sm text-gray-600">
                          {generationProgress.percentage.toFixed(1)}% पूर्ण
                        </div>

                        {generationProgress.currentWard && (
                          <div className="text-center text-sm text-blue-600">
                            सध्या प्रक्रिया: {generationProgress.currentWard}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </>
            ) : (
              <p>{t("noResults")}</p>
            )}
          </div>
        )}

        {open && !loading && (
          <WhiteContainer>
            {showPrintAll && (
              <Button
                className="ml-4 mt-1"
                onClick={handlePrintAll}
                disabled={printloading}
              >
                <Printer
                  className={`w-4 mr-2  ${printloading ? "animate-bounce" : ""}`}
                />
                Print All
              </Button>
            )}

            <>
              <TanStackTable
                columns={columns}
                data={taxData}
                searchKey={undefined}
                searchColumn={"ownerName"}
                serverPagination={{
                  currentPage: page,
                  pageSize: limit,
                  totalPages: pagination.totalPages,
                  totalRecords: pagination.totalRecords,
                  onPageChange: handlePageChange,
                  onPageSizeChange: handlePageSizeChange,
                }}
              />
            </>
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default VarshikAkarniNineReport;
