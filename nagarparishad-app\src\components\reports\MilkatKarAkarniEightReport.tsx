import React, { useEffect, useState } from "react";
import WhiteContainer from "../custom/WhiteContainer";
import { Input } from "../ui/input";
import { t } from "i18next";
import { But<PERSON> } from "../ui/button";
import TanStackTable from "../globalcomponent/tanstacktable";
import { Printer } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import AsyncSelect from "@/components/ui/react-select";
import { toast } from "../ui/use-toast";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";
import { Loader } from "@/components/globalcomponent/Loader";
import ReportApi from "@/services/ReportServices";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Api from "@/services/ApiServices";
import PropertyApi from "@/services/PropertyServices";

const MilkatKarAkarniEightReport = () => {
  const navigate = useNavigate();
  const [printloading, setPrintLoading] = useState(false);
  const [loadingRows, setLoadingRows] = useState<string[]>([]);
  const [open, setopen] = useState(false);
  const wardList: any = useWardMasterController();
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isSearchDisabled, setIsSearchDisabled] = useState(true);
  const [ownerName, setOwnerName] = useState("");
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");
  const [financialYears, setFinancialYears] = useState([]);
  const [page, setPage] = useState(1);
    const [showPrintAll, setShowPrintALl] = useState(false);
  
  const [limit, setLimit] = useState(10);
  const [pagination, setPagination] = useState({
    totalPages: 0,
    totalRecords: 0,
  });
  const [showNoDataMessage, setShowNoDataMessage] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState({
    totalProperties: 0,
    processedProperties: 0,
    currentWard: "",
    percentage: 0,
  });
  const [wardRemainingCount, setWardRemainingCount] = useState(null);
  const [currentReassessmentRange, setCurrentReassessmentRange] =
    useState(null);

  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

  const wardOptions: any = wardList?.wardList?.map((ward: any) => ({
    value: ward.ward_id,
    label: ward.ward_name,
  }));

  const loadWardOptions = (
    inputValue: string,
    callback: (options: any[]) => void
  ) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };

  const [formData, setFormData] = useState({
    propertyNumber: "",
    streetName: "",
    ownerName: "",
    old_propertyNumber: "",
    wardName: "",
    totalBuildingTax: "",
    totalTax: "",
    someOtherField: "",
  });

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "srNo",
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.sr_no}</div>
      ),
    },
    {
      accessorKey: "propertyNumber",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.propertyNo")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.propertyNumber}</div>
      ),
    },
    {
      accessorKey: "old_propertyNumber",
      header: t("property.oldPropertyNumber"),
      cell: ({ row }) => (
        <div className="capitalize min-w-[150px]">
          {row.original.old_propertyNumber}
        </div>
      ),
    },
    {
      accessorKey: "streetName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {"रोड नाव"}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.street.street_name}</div>
      ),
    },
    {
      accessorKey: "ownerName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.ownerName")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => {
        const owners = row.original.property_owner_details;
        return (
          <div className="relative">
            {owners.length > 0 && owners[0]?.name}
            {owners.length > 1 && (
              <DropdownMenu>
                <p className="text-right">
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-BlueText text-white absolute right-0 bottom-0"
                    >
                      +{owners.length - 1}
                    </Button>
                  </DropdownMenuTrigger>
                </p>
                <DropdownMenuContent className="max-w-[300px] px-3 py-2">
                  {owners.slice(1).map((owner: any, index: number) => (
                    <DropdownMenuItem key={index}>
                      {owner.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: t("Actions"),
      enableHiding: false,
      cell: ({ row }: { row: any }) => {
        const rowId = row.original.propertyNumber;
        const isLoading = loadingRows.includes(rowId);

        return (
          <div className="text-nowrap">
            <Button
              className={`bg-LightBlue text-BlueText border border-Secondary hover:bg-LightBlue px-4 py-1 min-w-fit h-fit ${
                printloading ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={() => handlePrintTax(row?.original)}
              disabled={isLoading}
            >
              <Printer className={`w-5 ${isLoading ? "animate-bounce" : ""}`} />
            </Button>
          </div>
        );
      },
    },
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    const trimmedValue = value.trim().toUpperCase();
    setOwnerName("");

    setFormData((prevData) => {
      if (name === "propertyNumber" && trimmedValue !== "") {
        return {
          ...prevData,
          [name]: trimmedValue,
          old_propertyNumber: "",
          ownerName: "",
          wardName: null,
        };
      }
      if (name === "old_propertyNumber" && trimmedValue !== "") {
        return {
          ...prevData,
          [name]: trimmedValue,
          propertyNumber: "",
          ownerName: "",
          wardName: null,
        };
      }
      return {
        ...prevData,
        [name]: trimmedValue,
      };
    });
  };

  const handleWardChange = (selectedOption) => {
    setFormData((prevData) => ({
      ...prevData,
      wardName: selectedOption ? selectedOption.label : "",
      propertyNumber: "",
      ownerName: "",
      old_propertyNumber: "",
    }));
    setOwnerName("");
  };

  const handleOwnerNameChange = (e) => {
    const trimmedValue = e.target.value.trim().toUpperCase();
    setOwnerName(trimmedValue);
    setFormData((prevData) => ({
      ...prevData,
      propertyNumber: "",
      old_propertyNumber: "",
      wardName: null,
    }));
  };
  const handlePrintTax = async (item) => {
    const rowId = item.propertyNumber;
    setLoadingRows((prev) => [...prev, rowId]);

    // Open the tab immediately
    const newTab = window.open("", "_blank");

    try {
      const params = {
        searchOn: "propertyNumber",
        value: item.propertyNumber,
        fy: selectedFinancialYear,
      };

      const response = await ReportApi.printNamunaEightReport(params);
      console.log("response--<", response);

      if (response.status && response.data) {
        console.log("response--<s", response.data);

        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        console.log("url", url);

        if (newTab) {
          newTab.location.href = url; // Set the PDF blob URL
          newTab.focus();
        }

        toast({
          title: "Tax report generated successfully.",
          variant: "success",
        });
        toast({
          title: "Tax printed successfully.",
          variant: "success",
        });
      } else {
        newTab?.close(); // Close if no data
        toast({
          title: "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      newTab?.close(); // Close on error
      toast({
        title: "Please try again.",
        variant: "destructive",
      });
      console.error("Error printing tax:", error);
    } finally {
      setLoadingRows((prev) => prev.filter((id) => id !== rowId));
    }
  };

  const handlePrintAll = async () => {
    setPrintLoading(true);

    try {
      const params: any = {
        fy: selectedFinancialYear,
      };

      if (formData.wardName) {
        params.ward = formData.wardName;
      }
      if (ownerName) {
        params.name = ownerName;
      }

      const response = await ReportApi.printAllNamunaEightReport(params);

      if (response.status && response.data) {
     

        toast({
          title: "All tax reports generated successfully.",
          variant: "success",
        });
      } else {
    
        toast({
          title: "Failed to generate all tax reports. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
  
      toast({
        title: "Error generating all tax reports. Please try again.",
        variant: "destructive",
      });
      console.error("Error printing all taxes:", error);
    } finally {
      setPrintLoading(false);
    }
  };

  // Function to check ward remaining count for Milkat Kar
  const checkWardRemainingCount = async (wardName: string) => {
    if (!currentReassessmentRange || !wardName) return;

    try {
      const response = await Api.getMilkatKarRemainingCount(
        wardName,
        currentReassessmentRange.reassessment_range_id
      );
      if (response.status && response.data.statusCode === 200) {
        setWardRemainingCount(response.data.data);
      } else {
        setWardRemainingCount(null);
      }
    } catch (error) {
      console.error("Error checking ward remaining count:", error);
      setWardRemainingCount(null);
    }
  };

  const fetchReportData = async (queryParams, page, limit) => {
    if (queryParams.size <= 1) return;
    try {
      console.log("limit in fetching limit", limit, "page", page);
      const response = await ReportApi.getMilkatAkarniReport(
        queryParams,
        page,
        limit
      );

      if (response.status === 200 && response.data.data) {
        const {
          total,
          page,
          limit,
          totalPages,
          nextPageAvailable,
          prevPageAvailable,
          data,
        } = response.data.data;
         if(data.length >1){setShowPrintALl(true)}else{setShowPrintALl(false)}
        setFilteredData(data);
        setPagination({ totalPages, totalRecords: total });
        setopen(true);
        setShowNoDataMessage(false);

        // If searching by ward, check remaining count
        const wardParam = queryParams.get("ward");
        if (wardParam && limit==10 && page==1) {
          await checkWardRemainingCount(wardParam);
        } else {
          setWardRemainingCount(null);
        }
      } else {
        setShowPrintALl(false)
        setFilteredData([]);
        setopen(false);
        setShowNoDataMessage(true);
      }
    } catch (error) {
      console.error("Error fetching the data:", error);
      toast({
        title: "Failed to fetch the data. Please try again.",
        variant: "destructive",
      });
      setShowNoDataMessage(false);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    const queryParams = new URLSearchParams();

    try {
      queryParams.append("fy", selectedFinancialYear);

      if (formData.propertyNumber) {
        queryParams.append("searchOn", "propertyNumber");
        queryParams.append("value", formData.propertyNumber);
      }

      if (formData.old_propertyNumber) {
        queryParams.append("searchOn", "old_propertyNumber");
        queryParams.append("value", formData.old_propertyNumber);
      }

      if (formData.wardName) {
        queryParams.append("searchOn", "all");
        queryParams.append("ward", formData.wardName);
      }

      if (ownerName) {
        queryParams.append("searchOn", "name");
        queryParams.append("value", ownerName);
      }

      await fetchReportData(queryParams, page, limit);
    } catch (error) {
      console.error("Error in handleSubmit:", error);
    }
    setLoading(false);
  };

  const handlePageChange = (newPage) => {
    setPage((prev) => newPage);
  };

  const handlePageSizeChange = (newLimit) => {
    console.log("newlimit", newLimit);
    setLimit((prev) => newLimit);
    setPage(1); // Reset to the first page when changing page size
  };

  useEffect(() => {
    const { wardName, propertyNumber, old_propertyNumber } = formData;
    if (!wardName && !propertyNumber && !old_propertyNumber && !ownerName) {
      setIsSearchDisabled(true);
    } else {
      setIsSearchDisabled(false);
    }
  }, [formData, ownerName]);

  useEffect(() => {
    fetchFinancialYears();
    fetchCurrentReassessmentRange();
  }, []);

  const fetchCurrentReassessmentRange = async () => {
    try {
      const response = await PropertyApi.getReassessmentRanges((response) => {
        if (response.status && response.data.statusCode === 200) {
          const reassessmentRanges = response.data.data;
          const currentReassessment = reassessmentRanges.find(
            (r) => r.is_current
          );
          if (currentReassessment) {
            setCurrentReassessmentRange(currentReassessment);
          }
        }
      });
    } catch (error) {
      console.error("Error fetching current reassessment range:", error);
    }
  };

  useEffect(() => {
    handleSubmit();
  }, [page, limit]);

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
        const currentYear = response.data.data.find((year) => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  const handleGenerateMilkatKar = async () => {
    if (!selectedFinancialYear) {
      toast({
        title: "कृपया आर्थिक वर्ष निवडा",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress({
      totalProperties: 0,
      processedProperties: 0,
      currentWard: "",
      percentage: 0,
    });

    try {
      // First get reassessment ranges to find the current one
      const reassessmentResponse = await PropertyApi.getReassessmentRanges(
        (response) => {
          if (response.status && response.data.statusCode === 200) {
            const reassessmentRanges = response.data.data;
            const currentReassessment = reassessmentRanges.find(
              (r) => r.is_current
            );

            if (currentReassessment) {
              // Start the generation process
              startMilkatKarGeneration(
                currentReassessment.reassessment_range_id ||
                  currentReassessment.reassessment_id
              );
            } else {
              toast({
                title: "सध्याचे पुनर्मूल्यांकन वर्ष सापडले नाही",
                variant: "destructive",
              });
              setIsGenerating(false);
            }
          } else {
            toast({
              title: "पुनर्मूल्यांकन डेटा मिळवण्यात अयशस्वी",
              variant: "destructive",
            });
            setIsGenerating(false);
          }
        }
      );
    } catch (error) {
      console.error("Error starting generation:", error);
      toast({
        title: "मालमत्ता कर तयार करण्यात त्रुटी",
        variant: "destructive",
      });
      setIsGenerating(false);
    }
  };

  const simulateProgress = () => {
    // Simulate progress for demonstration
    const totalSteps = 100;
    let currentStep = 0;

    const progressInterval = setInterval(() => {
      currentStep += Math.random() * 5; // Random progress increment
      if (currentStep >= totalSteps) {
        currentStep = totalSteps;
        clearInterval(progressInterval);
      }

      setGenerationProgress({
        totalProperties: 500, // Simulated total
        processedProperties: Math.floor((currentStep / totalSteps) * 500),
        currentWard: `Ward ${Math.floor(Math.random() * 10) + 1}`,
        percentage: currentStep,
      });
    }, 200);

    return progressInterval;
  };

  const startMilkatKarGeneration = async (reassessmentYearId) => {
    try {
      // Start progress simulation
      const progressInterval = simulateProgress();

      // Call the processMilkatKar API with correct signature
      PropertyApi.processMilkatKar(reassessmentYearId, (response) => {
        // Clear progress simulation
        clearInterval(progressInterval);

        if (response.status && response.data.statusCode === 200) {
          // Set final progress
          setGenerationProgress({
            totalProperties: 500,
            processedProperties: 500,
            currentWard: "Completed",
            percentage: 100,
          });

          setTimeout(() => {
            toast({
              title: "मालमत्ता कर यशस्वीरित्या तयार झाला",
              variant: "success",
            });
            setIsGenerating(false);
            // Refresh the report data
            handleSubmit();
          }, 1000);
        } else {
          toast({
            title: "मालमत्ता कर तयार करण्यात अयशस्वी",
            description: response.data.message || "अनपेक्षित त्रुटी आली",
            variant: "destructive",
          });
          setIsGenerating(false);
        }
      });
    } catch (error) {
      console.error("Error in generation process:", error);
      toast({
        title: "मालमत्ता कर तयार करण्यात त्रुटी",
        variant: "destructive",
      });
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex h-fit ">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          नमुना 8
        </h1>
        <WhiteContainer>
          <div className="grid md:grid-cols-5 gap-x-3 gap-2 items-end">
            <div className="grid-cols-subgrid">
              <Label>
                {t("financialYear")}
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select
                onValueChange={setSelectedFinancialYear}
                value={selectedFinancialYear}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("selectYear")} />
                </SelectTrigger>
                <SelectContent>
                  {financialYears.map((year) => (
                    <SelectItem
                      key={year.financial_year_range}
                      value={year.financial_year_range}
                    >
                      {year.financial_year_range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid-cols-subgrid">
              <Label>{"प्रभाग"}</Label>
              <AsyncSelect
                placeholder={t("propertyLocationDetailsForm.ward")}
                className="mt-1 h-[2.5rem]"
                loadOptions={loadWardOptions}
                defaultOptions={wardOptions}
                value={
                  formData.wardName
                    ? { label: formData.wardName, value: formData.wardName }
                    : null
                }
                onChange={handleWardChange}
                onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("namunaEight.propertyNumber")}</Label>
              <Input
                name="propertyNumber"
                value={formData.propertyNumber}
                onChange={handleChange}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("property.propertyNumberColumn")}
                onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("property.oldPropertyNumber")}</Label>
              <Input
                name="old_propertyNumber"
                value={formData.old_propertyNumber}
                onChange={handleChange}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("property.oldPropertyNumber")}
                onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
              />
            </div>
            <div className="grid-cols-subgrid">
              <Label>{t("नाव")}</Label>
              <Input
                name="ownerName"
                value={ownerName}
                onChange={handleOwnerNameChange}
                className="mt-1 block w-full"
                type="text"
                placeholder={t("नाव")}
                onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
              />
            </div>
          </div>

          <div className=" flex justify-between items-center">
            <p className="text-xs italic font-semibold mb-0 text-[#3c3c3c]">
              {" "}
              {/* {t("selectAnyFieldNote")}{" "} */}
            </p>
            <div className="flex justify-end">
              <Button
                variant="submit"
                className="mt-2"
                onClick={handleSubmit}
                disabled={isSearchDisabled}
              >
                {t("search")}
              </Button>
            </div>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        )}
        {(showNoDataMessage ||
          (wardRemainingCount && wardRemainingCount?.remainingProperties || 0 > 0)) &&
          !loading && (
            <WhiteContainer>
              <div className="text-center py-8">
                {showNoDataMessage && (
                  <p className="text-lg text-gray-600 mb-4">
                    {t("noDataFound")}
                  </p>
                )}

                {/* Show generate button if there are remaining properties to generate */}
                {(() => {
                  // If we have ward remaining count and there are remaining properties
                  if (
                    wardRemainingCount &&
                    wardRemainingCount.remainingProperties > 0
                  ) {
                    return true;
                  }
                  // If no data found (for non-ward searches)
                  if (showNoDataMessage && !wardRemainingCount) {
                    return true;
                  }
                  return false;
                })() ? (
                  <>
                    {!isGenerating ? (
                      <div className="space-y-4">
                        <Button
                          onClick={handleGenerateMilkatKar}
                          className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600"
                        >
                          {formData.wardName
                            ? `${formData.wardName} वॉर्डसाठी मालमत्ता कर तयार करा`
                            : "मालमत्ता कर तयार करा"}
                        </Button>

                        {/* Show ward statistics if available */}
                       {wardRemainingCount && (
  <div className="w-full bg-[#edf5ff] rounded-md shadow border border-blue-200 mt-6 px-4 py-6">
    <p className="text-base font-semibold text-center text-blue-800 mb-6">
      वॉर्ड {wardRemainingCount.wardName} माहिती:
    </p>
    <div className="grid grid-cols-3 text-center">
      <div>
        <p className="text-xl font-bold text-gray-800">
          {wardRemainingCount.totalProperties}
        </p>
        <p className="text-sm text-gray-700 mt-1">एकूण मालमत्ता</p>
      </div>
      <div>
        <p className="text-xl font-bold text-green-600">
          {wardRemainingCount.completedProperties}
        </p>
        <p className="text-sm text-gray-700 mt-1">तयार झालेले</p>
      </div>
      <div>
        <p className="text-xl font-bold text-orange-600">
          {wardRemainingCount.remainingProperties}
        </p>
        <p className="text-sm text-gray-700 mt-1">बाकी</p>
      </div>
    </div>
  </div>
)}

                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-center gap-2">
                          <Loader className="w-5 h-5" />
                          <span className="text-lg font-medium">
                            मालमत्ता कर तयार करत आहे...
                          </span>
                        </div>

                        {generationProgress.totalProperties > 0 && (
                          <div className="max-w-md mx-auto space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>प्रगती:</span>
                              <span>
                                {generationProgress.processedProperties} /{" "}
                                {generationProgress.totalProperties}
                              </span>
                            </div>

                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div
                                className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                                style={{
                                  width: `${generationProgress.percentage}%`,
                                }}
                              ></div>
                            </div>

                            <div className="text-center text-sm text-gray-600">
                              {generationProgress.percentage.toFixed(1)}% पूर्ण
                            </div>

                            {generationProgress.currentWard && (
                              <div className="text-center text-sm text-blue-600">
                                सध्या प्रक्रिया:{" "}
                                {generationProgress.currentWard}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <p>{t("noResults")}</p>
                )}
              </div>
            </WhiteContainer>
          )}

        {open && !loading && (
          <WhiteContainer>

            <div className="flex justify-end">
              
             {showPrintAll && (
                     <Button
                       className="ml-4 mt-1"
                       onClick={handlePrintAll}
                       disabled={printloading }
                     >
                       <Printer
                         className={`w-4 mr-2  ${printloading ? "animate-bounce" : ""}`}
                       />
                       Print All
                     </Button>
                   )}

            </div>
            <TanStackTable
              columns={columns}
              data={filteredData.length ? filteredData : []}
              searchKey={undefined}
              searchColumn={"ownerName"}
              serverPagination={{
                currentPage: page,
                pageSize: limit,
                totalPages: pagination.totalPages,
                totalRecords: pagination.totalRecords,
                onPageChange: handlePageChange,
                onPageSizeChange: handlePageSizeChange,
              }}
            />
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default MilkatKarAkarniEightReport;
