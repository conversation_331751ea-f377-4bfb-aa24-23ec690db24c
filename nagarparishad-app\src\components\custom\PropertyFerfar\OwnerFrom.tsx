import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { t } from "i18next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";

import { Checkbox } from "@/components/ui/checkbox";
import Webcam from "react-webcam";
import { Textarea } from "@/components/ui/textarea";
import WebcamCapture from "@/components/globalcomponent/WebcamCapture";
import {
  CircleX,
  Delete,
  Edit,
  Save,
  Trash,
  UploadCloudIcon,
} from "lucide-react";
import DocumentUpload from "@/components/globalcomponent/DocumentUpload";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { useTranslation } from "react-i18next";
import DeletePopUpScreen from "../DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import axios from "axios";
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";
import PropertyApi from "@/services/PropertyServices";
import ImageServices from "@/services/ImageServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

const panCardRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

const schema = z.object({
  owner_type: z.string().nonempty(t("errorsRequiredField")),
  owner_name: z.string().nonempty(t("errorsRequiredField")),

  mobile_number: z
    .string()
    .regex(/^(?:[1-9])\d{9}$/, t("errorsMobileNumber"))
    .optional()
    .or(z.literal("")),
  email_id: z.string().email("अवैध ईमेल").optional().or(z.literal("")),

  aadhar_number: z
    .string()
    .optional()
    .refine((value) => value.length !== 12, {
      message: "आधार क्रमांक 12 अंकांचा असणे अनिवार्य आहे",
    }),

  pan_card: z
    .string()
    .regex(panCardRegex, "अवैध पॅन कार्ड क्रमांक")
    .optional()
    .or(z.literal("")),

  gender: z.string().nonempty(t("errorsRequiredField")),

  marital_status: z.string().optional(), // Make marital status optional
  partner_name: z.string().optional(),
  remark: z.string(),
  photo: z.any().optional(),
  documents: z.array(z.any()).optional(),
});

type FormData = z.infer<typeof schema>;

interface formProps {
  tableData: any;
  title: string;
  divRef: React.MutableRefObject<any>;
}

const OwnerForm: React.FC<formProps> = ({ tableData, title, divRef }) => {
  const { t } = useTranslation();
  const [assetDetails, setassetDetails] = useState("");
  const [isMarried, setIsMarried] = useState(false);
  const [data, setData] = useState<any[]>(
    tableData?.property_owner_details || []
  );
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [deletedItems, setDeletedItems] = useState<any[]>([]);
  const [remark, setRemark] = useState("");
  const { setrefreshRoleList } = useContext(GlobalContext);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // New state for editing
  const { updateProperty, setUpdateProperty } = useContext(GlobalContext);
  const [isDeleting, setIsDeleting] = useState(false);
  // New state for temporary owners that haven't been submitted yet
  const [tempOwners, setTempOwners] = useState<any[]>([]);
  const [isUpdateMode, setIsUpdateMode] = useState(false);
  const [currentOperation, setCurrentOperation] = useState<'none' | 'add' | 'edit' | 'delete'>('none');
  const { ownerTypeList } = useOwnerTypeController();
  const ownerTypeMap = Object.fromEntries(
    ownerTypeList.map((ownerType) => [
      ownerType.owner_type_id,
      ownerType.owner_type,
    ])
  );

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      owner_type: tableData?.owner_type?.owner_type || "",
      marital_status: tableData?.marital_status || "no",
      owner_name: tableData?.name || "",
      mobile_number: tableData?.mobile_number || "",
      email_id: tableData?.email_id || "",
      aadhar_number: formatAdharNo(tableData?.aadhar_number || ""),
      pan_card: tableData?.pan_card || "",
      gender: tableData?.gender || "",
      remark: tableData?.ferfarRemark || "",
      // photo: "",
      // documents: [],
    },
  });

  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanRead
  );
  const canUpdate = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanUpdate
  );
  const CanCreate = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanCreate
  );
  const CanDelete = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanDelete
  );

const handleUploadComplete = (fileDetails: any) => {
  const fileDetailsArray = Array.isArray(fileDetails) ? fileDetails : [fileDetails];
  setUploadedFiles((prevFiles) => [...prevFiles, ...fileDetailsArray]);
};

  const handleRemoveFile = (file: any) => {
    setUploadedFiles((prevFiles) => prevFiles.filter((i) => i !== file));
  };
  const webcamRef = useRef<Webcam>(null);
  const {
    control,
    watch,
    formState: { errors },
  } = form;

  const handleCapture = (photo: string) => {
    setCapturedPhotos((prev) => [...prev, photo]);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      setValue("documents", Array.from(files));
    }
  };

  const formTwoOnSubmit = (data) => {
    console.log("Form data submitted:sss", data);
  };

  const viewImage = async (imageSrc: string) => {
    try {
      await ImageServices.viewImage(imageSrc);
    } catch (error) {
      console.error("Error viewing image:", error);
    }
  };
  // const handleEdist = (row: any, index: number) => {

  const handleEdit = (row: any, index: number) => {
  
    setCurrentOperation('edit');
    setIsFormVisible(true); // Show the form for editing
    setIsUpdateMode(true); // Set update mode to true

    if (index < 0) {
      // Editing temp owner (index is negative)
      const tempIndex = Math.abs(index) - 1; // Get actual index in tempOwners array
      const tempRow = tempOwners[tempIndex];

      form.reset({
        owner_type: tempRow.owner_type_id || "",
        owner_name: tempRow.name || "",
        mobile_number: tempRow.mobile_number || "",
        email_id: tempRow.email_id || "",
        aadhar_number: formatAdharNo(tempRow.aadhar_number || ""),
        pan_card: tempRow.pan_card || "",
        gender: tempRow.gender || "",
        marital_status: tempRow.marital_status || "no",
        partner_name: tempRow.partner_name || "",
        remark: tempRow.remark || "",
      });
      setSelectedIndex(index); // Store negative index to indicate temp owner
      setIsMarried(tempRow.marital_status === "yes");
    } else {
      // Editing existing owner
      setSelectedIndex(index);
      const propertyOwnerId = row.property_owner_details_id;
      form.reset({
        owner_type: row.owner_type.owner_type_id || "",
        owner_name: row.name || "",
        mobile_number: row.mobile_number || "",
        email_id: row.email_id || "",
        aadhar_number: formatAdharNo(row.aadhar_number || ""),
        pan_card: row.pan_card || "",
        gender: row.gender || "",
        marital_status: row.marital_status || "no",
        partner_name: row.partner_name || "",
        remark: tableData.ferfarRemark || "",
        // photo: "",
        // documents: [],
      });
      setIsMarried(row.marital_status === "yes");
    }
  };

  const PropertyId = tableData?.property_id || "";

  // Add owner to form data without submitting to API
  const addToTempOwners = () => {
    const formData = form.getValues();

    // Validate form
    form.trigger().then((isValid) => {
      if (!isValid) {
        toast({
          title: "कृपया आवश्यक फील्ड भरा",
          variant: "destructive",
        });
        return;
      }

      // Create a new temporary owner object - without remark
      const newTempOwner = {
        owner_type_id: formData.owner_type,
        name: formData.owner_name,
        mobile_number: formData.mobile_number || "",
        email_id: formData.email_id || "",
        aadhar_number: formData.aadhar_number?.replace(/-/g, "") || "",
        gender: formData.gender,
        marital_status: formData.marital_status || "no",
        pan_card: formData.pan_card || "",
        partner_name: formData.partner_name || "",
        // Add this for rendering in the table
        owner_type: {
          owner_type_id: formData.owner_type,
          owner_type: ownerTypeMap[formData.owner_type],
        },
      };

      if (selectedIndex !== null && selectedIndex < 0) {
        // Editing an existing temp owner
        const tempIndex = Math.abs(selectedIndex) - 1;
        const updatedTempOwners = [...tempOwners];
        updatedTempOwners[tempIndex] = newTempOwner;
        setTempOwners(updatedTempOwners);

        toast({
          title: "मालमत्ता धारक अद्यतनित केला",
          variant: "success",
        });
      } else if (selectedIndex !== null && selectedIndex >= 0) {
        // We're in update mode and adding a new owner alongside the update
        // Add to temp owners for batch processing
        setTempOwners([...tempOwners, newTempOwner]);

        toast({
          title: "मालमत्ता धारक सूचीमध्ये जोडला",
          description: "सर्व बदल एकत्रित सबमिट केले जातील",
          variant: "success",
        });
      } else {
        // Adding a new temp owner (normal flow)
        setTempOwners([...tempOwners, newTempOwner]);

        toast({
          title: "मालमत्ता धारक सूचीमध्ये जोडला",
          variant: "success",
        });
      }

      // Reset form only if not in update mode for an existing owner
      // This allows the user to continue with the update after adding new owners
      if (!(selectedIndex !== null && selectedIndex >= 0)) {
        form.reset({
          owner_type: "",
          owner_name: "",
          mobile_number: "",
          email_id: "",
          aadhar_number: "",
          gender: "",
          pan_card: "",
          marital_status: "no",
          partner_name: "",
          // Keep the remark as it's common for all owners
          remark: formData.remark || "",
        });

        setSelectedIndex(null);
        setIsMarried(false);
      }
      
      // REMOVED: setIsFormVisible(false); - Keep the form open after adding to temp owners
    });
  };
  // Update the global remark that applies to all owners
  const handleRemarkChange = (event) => {
    setRemark(event.target.value);
  };

  // Submit all temp owners to API
  const submitAllOwners = async () => {
    if (tempOwners.length === 0) {
      toast({
        title: "कोणतेही मालक जोडलेले नाहीत",
        variant: "destructive",
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: "मालमत्ता धारक सबमिट करत आहे...",
        variant: "default",
      });

      // Check if we're in update mode with an existing owner
      if (selectedIndex !== null && selectedIndex >= 0) {
        // If we're updating an existing owner and adding new ones,
        // use the batch update function
        const propertyOwnerId = data[selectedIndex]?.property_owner_details_id;
        if (propertyOwnerId) {
          const formData = form.getValues();
          const updatedEntry = {
            owner_type_id: formData.owner_type,
            name: formData.owner_name,
            mobile_number: formData.mobile_number || "",
            email_id: formData.email_id || "",
            aadhar_number: formData.aadhar_number?.replace(/-/g, "") || "",
            gender: formData.gender,
            marital_status: formData.marital_status || "no",
            pan_card: formData.pan_card || "",
            partner_name: formData.partner_name || "",
            // remark is now handled separately
          };

          await updateOwnerWithNewOwners(propertyOwnerId, updatedEntry);

          // Reset form and close it
          form.reset();
          setSelectedIndex(null);
          setIsFormVisible(false);
          return;
        }
      }

      // If not in update mode, prepare owners data without remark
      const ownersData = tempOwners.map(owner => ({
        owner_type_id: owner.owner_type_id,
        name: owner.name,
        mobile_number: owner.mobile_number,
        email_id: owner.email_id,
        aadhar_number: owner.aadhar_number,
        gender: owner.gender,
        marital_status: owner.marital_status,
        pan_card: owner.pan_card,
        partner_name: owner.partner_name,
        // No remark here as it's handled separately
      }));

      // Prepare the request payload with owners array and separate fields for remark, photo, and documents
      const requestPayload = {
        owners: ownersData,
        remark: remark, // Global remark for all owners
        photos: capturedPhotos, // Photos for all owners
        documents: uploadedFiles, // Documents for all owners
      };

      console.log("Request payload being sent:", requestPayload); // Debugging log

      // Use batch API to add multiple owners at once
      const apiUrl = `${apiBaseUrl}/v1/property-owner/${PropertyId}/add-new-owner`;
      const response = await axios.post(apiUrl, requestPayload, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });

      console.log("API Response:", response.data); // Debugging log

      if (response.status === 200 || response.status === 201) {
        // Add the new owners to the data array with their IDs
        const newOwners = response.data.owners || [];
        setData((prevData) => [...prevData, ...newOwners]);

        // Clear temporary owners and uploaded files after successful submission
        setTempOwners([]);
        setCapturedPhotos([]);
        setUploadedFiles([]);

        toast({
          title: "सर्व मालमत्ता धारक यशस्वीरित्या जोडले गेले",
          variant: "success",
        });

        // Close the form after submission
        setIsFormVisible(false);
      }
    } catch (error) {
      console.error("Error submitting owners:", error.response ? error.response.data : error.message); // Debugging log
      toast({
        title: "मालमत्ता धारक जोडण्यात अयशस्वी",
        variant: "destructive",
      });
    }
  };



  // Function to handle editing an existing owner in database and optionally add new owners
  const editOwner = async (submittedData: FormData) => {
    
    const propertyOwnerId = data[selectedIndex]?.property_owner_details_id;

    if (!propertyOwnerId) {
      toast({
        title: t("Property owner ID not found"),
        variant: "destructive",
      });
      return;
    }

    const updatedEntry = {
      owner_type_id: submittedData.owner_type,
      name: submittedData.owner_name,
      mobile_number: submittedData.mobile_number,
      email_id: submittedData.email_id,
      aadhar_number: submittedData.aadhar_number.replace(/-/g, ""),
      gender: submittedData.gender,
      marital_status: submittedData.marital_status,
      pan_card: submittedData.pan_card,
      partner_name: submittedData.partner_name,
      remark: submittedData.remark,
    };

    try {
      // Prepare the data for the API call, including photos and documents
      const updateData = {
        ...updatedEntry,
        photos: capturedPhotos,
        documents: uploadedFiles,
      };

      // Check if there are any temporary owners to add along with the update
      if (tempOwners.length > 0) {
        // If there are temp owners, use the batch update function
        await updateOwnerWithNewOwners(propertyOwnerId, updatedEntry);
      } else {
        // If no temp owners, just update the existing owner
        const apiUrl = `${apiBaseUrl}/v1/property-owner/correction/${propertyOwnerId}`;

        const response = await axios.patch(apiUrl, updateData, {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
          },
        });

        if (response.status === 200) {
          setData((prevData) => {
            const newData = [...prevData];
            newData[selectedIndex] = {
              ...newData[selectedIndex],
              ...updatedEntry,
              property_owner_details_id: propertyOwnerId,
              owner_type: {
                owner_type_id: submittedData.owner_type,
                owner_type: ownerTypeMap[submittedData.owner_type],
              },
            };
            return newData;
          });

          toast({
            title: t("मालमत्ता धारक यशस्वीरित्या अद्यतनित केले"),
            variant: "success",
          });
        }
      }

      setSelectedIndex(null);
      setIsFormVisible(false); // Close the form after editing
      form.reset();
    } catch (error) {
      toast({ title: t("apiError"), variant: "destructive" });
    }
  };

  // Function to update an owner and add new owners in a single API call
  const updateOwnerWithNewOwners = async (propertyOwnerId: string, updatedEntry: any) => {
    try {
      // Show loading toast
      toast({
        title: "मालमत्ता धारक अद्यतनित करत आहे...",
        variant: "default",
      });

      // Prepare the data for the API call with remark, photos, and documents outside the owner objects
      const ownersData = {
        updatedOwner: {
          property_owner_details_id: propertyOwnerId,
          ...updatedEntry
        },
        newOwners: tempOwners.map(owner => ({
          owner_type_id: owner.owner_type_id,
          name: owner.name,
          mobile_number: owner.mobile_number,
          email_id: owner.email_id,
          aadhar_number: owner.aadhar_number,
          gender: owner.gender,
          marital_status: owner.marital_status,
          pan_card: owner.pan_card,
          partner_name: owner.partner_name,
          // No remark here as it's handled separately
        })),
        // Common fields for all owners
        remark: remark, // Global remark for all owners
        photos: capturedPhotos, // Photos for all owners
        documents: uploadedFiles, // Documents for all owners
      };

      console.log("Update with new owners payload:", ownersData); // Debugging log

      // Make the API call to update and add owners
      const apiUrl = `${apiBaseUrl}/v1/property-owner/${PropertyId}/update-with-new-owners`;
      const response = await axios.post(apiUrl, ownersData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });

      if (response.status === 200 || response.status === 201) {
        // Update the existing owner in the UI
        setData((prevData) => {
          const newData = [...prevData];
          newData[selectedIndex] = {
            ...newData[selectedIndex],
            ...updatedEntry,
            property_owner_details_id: propertyOwnerId,
            owner_type: {
              owner_type_id: updatedEntry.owner_type_id,
              owner_type: ownerTypeMap[updatedEntry.owner_type_id],
            },
          };

          // Add the new owners to the data array
          const newOwners = response.data.newOwners || [];
          return [...newData, ...newOwners];
        });

        // Clear temporary owners and uploaded files after successful submission
        setTempOwners([]);
        setCapturedPhotos([]);
        setUploadedFiles([]);

        toast({
          title: "सर्व मालमत्ता धारक यशस्वीरित्या अद्यतनित केले",
          variant: "success",
        });
      }
    } catch (error) {
      console.error("Error updating owners:", error);
      toast({
        title: "मालमत्ता धारक अद्यतनित करण्यात अयशस्वी",
        variant: "destructive",
      });
      throw error; // Re-throw to be caught by the calling function
    }
  };

  const onsubmit: SubmitHandler<FormData> = async (submittedData) => {
    console.log("Form submitted with data:", submittedData);

    // If we are editing an existing owner (not a temp one)
    if (selectedIndex !== null && selectedIndex >= 0) {
      // If there are temporary owners and we're updating an existing owner,
      // we'll handle both operations in a single API call
      await editOwner(submittedData);
    } else if (selectedIndex !== null && selectedIndex < 0) {
      // Editing a temporary owner
      addToTempOwners();
      setIsFormVisible(false); // Close the form after adding to temp owners
    } else {
      // Adding a new temporary owner
      addToTempOwners();
      // Keep the form open to allow adding more owners
    }
  };

  useEffect(() => {
    // Load data from localStorage when component mounts
    const savedData = localStorage.getItem("ownerData");
    if (savedData) {
      setData(JSON.parse(savedData));
    }

    // Load temp owners
    const savedTempOwners = localStorage.getItem("tempOwners");
    if (savedTempOwners) {
      setTempOwners(JSON.parse(savedTempOwners));
    }
  }, []);

  useEffect(() => {
    // Save data to localStorage whenever it changes
    localStorage.setItem("ownerData", JSON.stringify(data));
  }, [data]);

  useEffect(() => {
    // Save temp owners to localStorage
    localStorage.setItem("tempOwners", JSON.stringify(tempOwners));
  }, [tempOwners]);

  const handleOpenDelete = (index, isTemp = false) => {
    setCurrentOperation('delete');
    if (isTemp) {
      // Remove from temp owners
      setTempOwners(tempOwners.filter((_, i) => i !== index));
      toast({
        title: "मालमत्ता धारक हटविला",
        variant: "success",
      });
      // Don't close the form when deleting a temp owner
    } else {
      // Remove from real owners
      const item = data[index];
      setDeletedItems((prev) => [...prev, item]);
      setData((prev) => prev.filter((_, i) => i !== index));
      setIsFormVisible(false); // Close the form only when deleting a real owner
    }
  };

  const handleCancelDelete = () => {
    setCurrentOperation('none');
    setDeletedItems([]);
    setData((prev) => [...prev, ...deletedItems]);
  };

  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    try {
      const deletePromises = deletedItems.map(async (item) => {
        const propertyOwnerId = item.property_owner_details_id;

        if (propertyOwnerId) {
          const deleteResponse = await PropertyApi.deleteOwner(
            propertyOwnerId,
            remark
          );
          if (!deleteResponse.status) {
            throw new Error(deleteResponse.data);
          }
        }
      });

      await Promise.all(deletePromises);
      setDeletedItems([]);
      toast({ title: t("removeOwner"), variant: "success" });
    } catch (error) {
      console.log("error", error, deletedItems);
      toast({ title: t("failedToDeleteOwner"), variant: "destructive" });
    } finally {
      setIsDeleting(false);
      setCurrentOperation('none');
    }
  };

  useEffect(() => {
    if (tableData && tableData.property_owner_details) {
      setData(tableData.property_owner_details);
    }
  }, [tableData]);

  // Columns for saved owners
  const columns = [
    {
      accessorKey: "ownerType",
      header: "मालमत्ता धारक  प्रकार",
      cell: ({ row }: { row: any }) => {
        return (
          <div>{ownerTypeMap[row.original.owner_type?.owner_type_id]}</div>
        );
      },
    },
    {
      accessorKey: "ownerName",
      header: "मालमत्ता धारकाचे नाव",
      cell: ({ row }: { row: any }) => <div>{row.original.name}</div>,
    },
    {
      accessorKey: "mobileNumber",
      header: "मोबाईल नंबर",
      cell: ({ row }: { row: any }) => <div>{row.original.mobile_number}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => <div>{row.original.aadhar_number}</div>,
    },
    {
      accessorKey: "actions",
      header: "कृती",
      cell: ({ row }: { row: any }) => {
        const isChecked =
          normalizeWhitespace(title) ===
          normalizeWhitespace(
            "ज्यांच्या नावावर मिळकत कारवायचीय आहे त्या मिळकत धारकांची नावे"
          );
        return (
          <div>
            {isChecked ? (
              <Checkbox
                checked={row.index === selectedCheckboxIndex}
                onCheckedChange={() => handleCheckboxChange(row.index)}
              />
            ) : (
              <div className="flex space-x-2">
                {currentOperation !== 'delete' && (
                  <button
                    className="h-7 w-7 p-0 justify-center"
                    onClick={() => handleEdit(row.original, row.index)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {currentOperation !== 'add' && currentOperation !== 'edit' && (
                  <button
                    className="h-7 w-7 p-0 justify-center"
                    onClick={() => handleOpenDelete(row.index)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </div>
            )}
          </div>
        );
      },
    },
  ];

  // Columns for temporary owners
  const tempColumns = [
    {
      accessorKey: "ownerType",
      header: "मालमत्ता धारक प्रकार",
      cell: ({ row }: { row: any }) => {
        return <div>{ownerTypeMap[row.original.owner_type_id]}</div>;
      },
    },
    {
      accessorKey: "ownerName",
      header: "मालमत्ता धारकाचे नाव",
      cell: ({ row }: { row: any }) => <div>{row.original.name}</div>,
    },
    {
      accessorKey: "mobileNumber",
      header: "मोबाईल नंबर",
      cell: ({ row }: { row: any }) => <div>{row.original.mobile_number}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => <div>{row.original.aadhar_number}</div>,
    },
    {
      accessorKey: "actions",
      header: "कृती",
      cell: ({ row }: { row: any }) => (
        <div className="flex space-x-2">
          {/* Always show edit button for temporary owners */}
          <button
            className="h-7 w-7 p-0 justify-center"
            onClick={() => handleEdit(row.original, -(row.index + 1))}
          >
            <Edit className="text-blue-500" />
          </button>
          {/* Always show delete button for temporary owners */}
          <button
            className="h-7 w-7 p-0 justify-center"
            onClick={() => handleOpenDelete(row.index, true)}
          >
            <Trash className="text-red-500" />
          </button>
        </div>
      ),
    },
  ];

  const deletedcolumns = [
    {
      accessorKey: "ownerType",
      header: "मालमत्ता धारक  प्रकार",
      cell: ({ row }: { row: any }) => {
        return (
          <div>{ownerTypeMap[row.original.owner_type?.owner_type_id]}</div>
        );
      },
    },
    {
      accessorKey: "ownerName",
      header: "मालमत्ता धारकाचे नाव",
      cell: ({ row }: { row: any }) => <div>{row.original.name}</div>,
    },
    {
      accessorKey: "mobileNumber",
      header: "मोबाईल नंबर",
      cell: ({ row }: { row: any }) => <div>{row.original.mobile_number}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => <div>{row.original.aadhar_number}</div>,
    },
  ];

  const [selectedOwnerType, setSelectedOwnerType] = useState("All");
  const [selectedCheckboxIndex, setSelectedCheckboxIndex] = useState(0);
  const [isFormVisible, setIsFormVisible] = useState(false);

  const handleFilterChange = (value) => {
    setSelectedOwnerType(value);
  };

  const handleCheckboxChange = (index) => {
    setSelectedCheckboxIndex(index === selectedCheckboxIndex ? null : index);
  };

  const filteredData = useMemo(() => {
    return selectedOwnerType === "All"
      ? data
      : data.filter((row) => row.owner_type.owner_type === selectedOwnerType);
  }, [data, selectedOwnerType]);

  const filteredTempData = useMemo(() => {
    return selectedOwnerType === "All"
      ? tempOwners
      : tempOwners.filter(
          (row) => ownerTypeMap[row.owner_type_id] === selectedOwnerType
        );
  }, [tempOwners, selectedOwnerType, ownerTypeMap]);

  const handleToggleFormVisibility = () => {
    // If the form is currently visible and we're closing it (canceling)
    if (isFormVisible) {
      // Clear temporary owners when canceling
      setTempOwners([]);
      setSelectedIndex(null);
      setCapturedPhotos([]);
      setUploadedFiles([]);
      setRemark("");
    }

    setCurrentOperation(isFormVisible ? 'none' : 'add');
    form.reset({
      owner_type: "",
      owner_name: "",
      mobile_number: "",
      email_id: "",
      aadhar_number: "",
      gender: "",
      pan_card: "",
      marital_status: "no",
      partner_name: "",
      remark: "",
    });
    
    setIsFormVisible((prev) => !prev); // Toggle form visibility
    setIsUpdateMode(false); // Reset update mode
    setIsMarried(false);
  };

  const normalizeWhitespace = (str) => str?.replace(/\s+/g, " ").trim();

  // For form handling
  const { setValue } = form;

  return (
    <>
      <h2 className="text-xl w-fit font-semibold mb-4">{title}</h2>

      <div className="mb-4">
        <div className="flex justify-between items-center mb-4">
          {normalizeWhitespace(title) !==
            normalizeWhitespace(
              "ज्यांच्या नावावर मिळकत कारवायची आहे त्या मिळकत धारकांची नावे"
            ) && (
            <div className="flex items-center">
              <Select
                value={selectedOwnerType}
                onValueChange={handleFilterChange}
              >
                <SelectTrigger className="w-fit">
                  {selectedOwnerType !== "All" ? selectedOwnerType : "ALL"}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                    <SelectItem value="स्वत:">स्वतः</SelectItem>
                  <SelectItem value="भोगवटादार">भोगवटादार</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {!isFormVisible && selectedIndex === null && CanCreate && currentOperation !== 'delete' && (
            <Button onClick={handleToggleFormVisibility}>
              {t("malmattaFerfar.addPropertyOwner")}
            </Button>
          )}
        </div>

        <TanStackTable
          columns={columns}
          data={filteredData}
          showPagination={false}
        />

        {deletedItems.length > 0 && (
          <div>
            <h2 className="mt-4 spl-2 text-lg font-semibold">
              <span>* </span>हटविलेले मालक
            </h2>
            <TanStackTable
              columns={deletedcolumns}
              data={deletedItems}
              showPagination={false}
            />
            <div className="w-full py-2 pl-1">
              <p className="mb-0 mt-2 font-semibold ">शेरा</p>
              <Textarea
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
                placeholder="शेरा"
                className=" w-1/2 "
              />
              <div className="flex justify-end mt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelDelete}
                  className="w-fit px-1 bg-white text-black mr-2 border-black border"
                >
                  {" "}
                  {t("cancel")}
                </Button>
                <Button
                  disabled={isDeleting}
                  onClick={handleConfirmDelete}
                  className=""
                >
                  {" "}
                  {t("add")}
                </Button>
              </div>
            </div>
          </div>
        )}
        {isDeleteOpen && selectedItem && (
          <DeletePopUpScreen
            isOpen={isDeleteOpen}
            toggle={handleCancelDelete}
            itemName={selectedItem.roleName}
            onDelete={handleConfirmDelete}
          />
        )}
      </div>

      {isFormVisible &&
        normalizeWhitespace(title) !==
          normalizeWhitespace(
            "ज्यांच्या नावावर मिळकत करावयाची आहे त्या मिळकत धारकांची नावे"
          ) && (
          <>
            <hr />

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onsubmit)}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-y-3 gap-x-6 px-3 pt-4">
                  <div className="grid-cols-subgrid">
                    <FormField
                      control={form.control}
                      name="owner_type"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel>
                            {" "}
                            {t("assetDetailsForm.ownerType")}
                            <span className="ml-1 text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value); // Update form value
                              }}
                              value={field.value}
                              {...field}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue
                                  placeholder={t("assetDetailsForm.ownerType")}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  {ownerTypeList.map((ownerType) => (
                                    <SelectItem
                                      key={ownerType.owner_type_id}
                                      value={ownerType.owner_type_id}
                                    >
                                      {ownerType.owner_type}
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          {errors.owner_type && (
                            <FormMessage className="ml-1">
                              Select Owner Type
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid-cols-subgrid">
                    <FormField
                      control={form.control}
                      name="owner_name"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel>
                            {" "}
                            संपूर्ण नाव{" "}
                            <span className="ml-1 text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center mt-1 ">
                              <div className="w-[80px]">
                                <select
                                  className="block w-full h-10 px-2 py-1 border border-[#0000006b]  rounded-l-md text-[13px] "
                                  onChange={(e) => {
                                    // Handle select change
                                    // For example, log the selected value
                                  }}
                                >
                                  <option value="Mr">श्री</option>
                                  <option value="Mrs">श्रीमती</option>
                                </select>
                              </div>
                              <Input
                                type="text"
                                className="rounded-l-none"
                                placeholder={t("enterFullName")}
                                {...field}
                              />
                            </div>
                          </FormControl>
                          {errors.owner_name && (
                            <FormMessage className="ml-1">
                              Enter Name
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="mobile_number"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel>
                            {" "}
                            {t("assetDetailsForm.mobileNumber")}
                            {/* <span className="ml-1 text-red-500">*</span> */}
                          </FormLabel>
                          <FormControl>
                            <Input
                              className="mt-1 block w-full"
                              transliterate={false}
                              type="text"
                              placeholder={t("assetDetailsForm.mobileNumber")}
                              {...field}
                            />
                          </FormControl>
                          {errors.mobile_number && (
                            <FormMessage className="ml-1">
                              Enter Mobile Number
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="email_id"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel> {t("assetDetailsForm.email")}</FormLabel>
                          <FormControl>
                            <Input
                              className="mt-1 block w-full"
                              type="text"
                              transliterate={false}
                              placeholder={t("assetDetailsForm.email")}
                              {...field}
                            />
                          </FormControl>
                          {errors.email_id && (
                            <FormMessage className="ml-1">
                              Enter Email ID
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="aadhar_number"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel>
                            {t("assetDetailsForm.adharNumber")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              className="mt-1 block w-full"
                              transliterate={false}
                              type="text"
                              placeholder={t("assetDetailsForm.adharNumber")}
                              {...field}
                              onKeyDown={(e) => {
                                if (e.keyCode === 8) {
                                  let value = e.target.value?.replace(
                                    /\D/g,
                                    ""
                                  );
                                  if (
                                    value.length > 1 &&
                                    value.length % 4 === 1
                                  ) {
                                    value = value.substring(
                                      0,
                                      value.length - 1
                                    );
                                  }
                                  field.onChange(value);
                                }
                              }}
                              onChange={(e) => {
                                let value = e.target.value
                                  ?.replace(/\D/g, "")
                                  .substring(0, 12);
                                const hasDash = value.includes("-");
                                if (hasDash) {
                                  value = value
                                    ?.replace(/(.{4})/g, "$1-")
                                    .slice(0, 14);
                                } else {
                                  value = value
                                    ?.replace(/(.{4})/g, "$1-")
                                    .slice(0, 14);
                                }
                                field.onChange(value);
                              }}
                            />
                          </FormControl>
                          {errors.aadhar_number && (
                            <FormMessage className="ml-1">
                              Enter Aadhar Number
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="pan_card"
                      render={({ field }) => (
                        <FormItem className=" ">
                          <FormLabel>
                            {" "}
                            {t("assetDetailsForm.panNumber")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              className="mt-1 block w-full"
                              transliterate={false}
                              type="text"
                              placeholder={t("assetDetailsForm.panNumber")}
                              {...field}
                              onChange={(e) => {
                                const value = e.target.value.toUpperCase();
                                field.onChange(value);
                              }}
                            />
                          </FormControl>
                          {errors.pan_card && (
                            <FormMessage className="ml-1">
                              Enter PAN Number
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormLabel>
                            {" "}
                            {t("assetDetailsForm.gender")}
                            <span className="ml-1 text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => field.onChange(value)}
                              defaultValue={field.value}
                              {...field}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue
                                  placeholder={t("assetDetailsForm.gender")}
                                />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectItem value="MALE">Male</SelectItem>
                                  <SelectItem value="FEMALE">Female</SelectItem>
                                  <SelectItem value="OTHER">Other</SelectItem>
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          {errors.gender && (
                            <FormMessage className="ml-1">
                              Select Gender
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid-cols-subgrid">
                    <FormField
                      control={control}
                      name="marital_status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>विवाह स्थिती</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={(value) => {
                                const married = value === "yes";
                                field.onChange(value);
                                setIsMarried(married);
                                if (!married) {
                                  form.setValue("partner_name", "");
                                }
                              }}
                              value={field.value}
                            >
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="yes" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  विवाहित
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="no" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  अविवाहित
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          {errors.marital_status && (
                            <FormMessage className="ml-1">
                              {t("errorsRequiredField")}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>

                  {isMarried && (
                    <div className="grid-cols-subgrid">
                      <FormField
                        control={control}
                        name="partner_name"
                        render={({ field }) => (
                          <FormItem className=" ">
                            <FormLabel>पती/पत्नी चे नाव</FormLabel>
                            <FormControl>
                              <div className="flex items-center mt-1">
                                <div className="w-[80px]">
                                  <select
                                    className="block w-full h-10 px-2 py-1 border border-[#0000006b]  rounded-l-md text-[13px]"
                                    onChange={(e) => {
                                      // Handle select change
                                    }}
                                  >
                                    <option value="Mr">श्री</option>
                                    <option value="Mrs">श्रीमती</option>
                                  </select>
                                </div>
                                <Input
                                  type="text"
                                  className="rounded-l-none"
                                  placeholder={t("enterFullName")}
                                  {...field}
                                  value={field.value}
                                />
                              </div>
                            </FormControl>
                            {errors.partner_name && (
                              <FormMessage className="ml-1">
                                Enter Name
                              </FormMessage>
                            )}
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>
{isUpdateMode && selectedIndex !== null && selectedIndex >= 0 && (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5 px-3">
    <div className="grid-cols-subgrid">
      <FormField
        control={control}
        name="photo"
        render={({ field }) => (
          <FormItem>
            <FormLabel>फोटो अपलोड करा </FormLabel>
            <FormControl>
              <>
                <WebcamCapture onCapture={handleCapture} />
              </>
            </FormControl>
            {errors.photo && (
              <FormMessage className="ml-1">
                add photo
              </FormMessage>
            )}
          </FormItem>
        )}
      />
    </div>

    <div className="grid-cols-subgrid">
      {/* Document Upload Field */}
      <FormField
        control={control}
        name="documents"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <>
                <DocumentUpload
                  onUploadComplete={handleUploadComplete}
                  title={"Click to upload"}
                  btnText={t(
                    "propertyLocationDetailsForm.uploadFile"
                  )}
                  uploadIcon={
                    <UploadCloudIcon className="w-9 h-9 m-auto text-BlueText" />
                  }
                />
              </>
            </FormControl>
            {errors.documents && (
              <FormMessage className="ml-1">
                {errors.documents.message}
              </FormMessage>
            )}
          </FormItem>
        )}
      />
    </div>
    <div className="grid-cols-subgrid">
                      <FormField
                        control={control}
                        name="remark"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>शेरा</FormLabel>
                            <FormControl>
                              <Textarea
                                className="mt-1 block w-full"
                                placeholder="शेरा"
                                {...field}
                              />
                            </FormControl>
                            {errors.remark && (
                              <FormMessage className="ml-1">
                                {errors.remark.message}
                              </FormMessage>
                            )}
                          </FormItem>
                        )}
                      />
                    </div>
  </div>
)}
                <div className="mt-4 flex justify-end space-x-2 items-end">
                  {/* Cancel button - only show if there are no temporary owners yet */}
                  {tempOwners.length === 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      className="w-fit px-1 bg-white text-black border-black border"
                      onClick={handleToggleFormVisibility}
                    >
                      {t("cancel")}
                    </Button>
                  )}

                  {/* Add to list button for new owners or editing temp owners */}
                  {(selectedIndex === null || selectedIndex < 0) && CanCreate && (
                    <Button
                      type="button"
                      onClick={addToTempOwners}
                      className="flex items-center gap-1"
                    >
                      {selectedIndex !== null && selectedIndex < 0
                        ? "अद्यतनित करा"
                        : "सूचीमध्ये जोडा"}
                    </Button>
                  )}

                  {/* Update button for existing owners */}
                  {selectedIndex !== null && selectedIndex >= 0 && CanCreate && (
                    <Button type="submit" variant="submit">
                      {tempOwners.length > 0 ? "सर्व अद्यतनित करा" : t("update")}
                    </Button>
                  )}
                </div>
                {/* Display temporary owners in a separate table below the form */}
                {tempOwners.length > 0 && (
                  <div className="mt-8">
                    <TanStackTable
                      columns={tempColumns}
                      data={filteredTempData}
                      showPagination={false}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5 px-3">
                      <div className="grid-cols-subgrid">
                        <FormField
                          control={control}
                          name="photo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>फोटो अपलोड करा </FormLabel>
                              <FormControl>
                                <>
                                  <WebcamCapture onCapture={handleCapture} />
                                </>
                              </FormControl>
                              {errors.photo && (
                                <FormMessage className="ml-1">
                                  add photo
                                </FormMessage>
                              )}
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid-cols-subgrid">
                        {/* Document Upload Field */}
                        <FormField
                          control={control}
                          name="documents"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <>
                                  <DocumentUpload
                                    onUploadComplete={handleUploadComplete}
                                    title={"Click to upload"}
                                    btnText={t(
                                      "propertyLocationDetailsForm.uploadFile"
                                    )}
                                    uploadIcon={
                                      <UploadCloudIcon className="w-9 h-9 m-auto text-BlueText" />
                                    }
                                  />
                                </>
                              </FormControl>
                              {errors.documents && (
                                <FormMessage className="ml-1">
                                  {errors.documents.message}
                                </FormMessage>
                              )}
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="grid-cols-subgrid">
                        <FormField
                          control={control}
                          name="remark"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>शेरा</FormLabel>
                            <FormControl>
                              <Textarea
                                className="mt-1 block w-full"
                                placeholder="शेरा"
                                {...field}
                              />
                            </FormControl>
                            {errors.remark && (
                              <FormMessage className="ml-1">
                                {errors.remark.message}
                              </FormMessage>
                            )}
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold">
                      {" "}
                      <span className="text-sm text-gray-500"></span>
                      </h3>

                      {/* Submit all temporary owners */}
                      {CanCreate && tempOwners.length > 0 && (
                        <div className="flex space-x-2">
                           <Button
                        type="button"
                        variant="outline"
                        className="w-fit bg-white text-black border-black border"
                        onClick={handleToggleFormVisibility}
                        >
                        {t("cancel")}
                        </Button>
                          <Button
                            onClick={submitAllOwners}
                            className="flex items-center gap-1"
                          >
                            {t("malmattaFerfar.addPropertyOwner")}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </form>
            </Form>
          </>
        )}
    </>
  );
};

export default OwnerForm;
