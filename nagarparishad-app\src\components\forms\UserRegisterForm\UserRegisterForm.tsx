import Api from "@/services/ApiServices";
import { toast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { z, object } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useUserRegisterController } from "@/controller/user-register/UserRegisterController";
import { useTranslation } from "react-i18next";
import { useRoleController } from "@/controller/roles/RolesController";
import { GlobalContext } from "@/context/GlobalContext";
import { Button } from "@/components/ui/button";
import { UserUpdateObj } from "@/model/user-register";
import { Loader2 } from "lucide-react";

interface UserMasterInterface {
  btnTitle: string;
  editData?: UserUpdateObj;
}

const UserRegisterForm = ({ btnTitle, editData }: UserMasterInterface) => {
  const { t } = useTranslation();

  const { roleList } = useRoleController();
  const [isUpdate, setIsUpdate] = useState(false);
  const dynamicValues = {
    name: t("user.userTitle"),
  };
  const { setRefreshUserList, refreshUserList } = useContext(GlobalContext);
  const [loader, setLoader] = useState(false);

  const { updateUser, createUser } = useUserRegisterController();

  const passwordValidation =isUpdate ? z.string().optional() :
    z.string()
    .min(6, t("errorsPasswordStrengthError"))
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/,
      t("errorsPasswordStrengthError"),
    );

  const mobileNumberValidation = z
    .string()
    .regex(/^[6-9]\d{9}$/, t("errorsMobileNumber"));

  const roleValidation = z
    .string()
    .nonempty(t("errorsRequiredField"))
    .refine((val) => !isNaN(Number(val)), t("errorsRequiredField"));

  const schema = z.object({
    firstname: z.string().nonempty(t("errorsRequiredField")),
    lastName: z.string().nonempty(t("errorsRequiredField")),
    email: z.string().email(t("errorsInvalidEmailFormat")),
    password: passwordValidation,
    mobileNumber: mobileNumberValidation,
    address: z.string().nonempty(t("errorsRequiredField")),
    role: roleValidation,
    isActive: z.boolean(),
    // profilePic: z.string()
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      firstname: editData?.firstname || "",
      lastName: editData?.lastname || "",
      email: editData?.email || "",
      password: "",
      mobileNumber: editData?.phone_number || "",
      address: "",
      role: editData?.role || "",
      isActive: true,
    },
  });

  const {
    formState: { errors },
    handleSubmit,
    setValue,
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    const userData = {
      firstname: data?.firstname,
      lastname: data?.lastName,
      email: data?.email,
      mobileNumber: data?.mobileNumber,
      address: data?.address,
      role_id: parseInt(data?.role),
     
      isActive: data?.isActive,
    };
    if (editData?.userId !== undefined && editData?.userId !== null) {
      setLoader(true);
      updateUser(
        { userId: editData.userId, userRegisterData: userData },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({
              firstname: "",
              lastName: "",
              email: "",
              password: "",
              mobileNumber: "",
              address: "",
              role: "",
              isActive: true,
            });
            setLoader(false);
            setIsUpdate(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      const userData = {
        firstname: data?.firstname,
        lastname: data?.lastName,
        email: data?.email,
        mobileNumber: data?.mobileNumber,
        address: data?.address,
        role: parseInt(data?.role),
        password: data?.password,
        isActive: data?.isActive,
      };
      createUser(userData, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({
            firstname: "",
            lastName: "",
            email: "",
            password: "",
            mobileNumber: "",
            address: "",
            role: "",
            isActive: true,
          });
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData && editData !== undefined) {
      setIsUpdate(true);
      reset({
        firstname: editData.firstname || "",
        lastName: editData.lastname || "",
        email: editData.email || "",
        password: "",
        mobileNumber: editData.mobileNumber || "",
        address: editData.address || "",
        role: editData?.role?.role_id.toString() || "",
        isActive: true,
      });
    }
  }, [editData, reset]);

  return (
    <div>
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="sm:px-4">
            <div className="">
              <div className="form-flex">
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="firstname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.firstName")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Input
                          {...field}
                          className="mt-1"
                          placeholder={t("user.firstName")}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.lastName")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Input
                          {...field}
                          className="mt-1"
                          placeholder={t("user.lastName")}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.email")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Input
                          {...field}
                          className="mt-1"
                          transliterate={false}

                          placeholder={t("user.email")}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="form-flex">
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="mobileNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.phoneNumber")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Input
                          {...field}
                          type="number"
                          className="mt-1"
                          placeholder={t("user.phoneNumber")}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.address")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Input
                          {...field}
                          // type="number"
                          className="mt-1"
                          placeholder={t("user.address")}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.password")}</FormLabel>
                        {!isUpdate && (<span className="ml-1 text-red-500">*</span>)}
                        <Input
                          {...field}
                          className="mt-1"
                          transliterate={false}
                          placeholder={t("user.password")}
                          disabled={isUpdate}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <div className="form-flex">
                <div className="form-element">
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("user.role")}</FormLabel>
                        <span className="ml-1 text-red-500">*</span>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value.toString()}
                        >
                          <SelectTrigger>
                            {roleList && roleList.length > 0
                              ? roleList.find(
                                (item) =>
                                  item?.role_id?.toString() ===
                                  field.value.toString(),
                              )?.roleName || "भूमिका निवडा"
                              : "भूमिका निवडा"}{" "}
                          </SelectTrigger>
                          <SelectContent>
                            {roleList?.map((role: string, index: any) => (
                              <SelectItem
                                key={index}
                                value={role?.role_id?.toString()}
                              >
                                {role.roleName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="form-element"></div>
                <div className="form-element"></div>

              </div>
            </div>
            <div className="w-full h-full flex justify-end mt-2 gap-2">
  {loader ? (
    <Button disabled>
      <Loader2 className="mr-2 h-4 w-5 animate-spin " />
      {t("pleaseWait")}
    </Button>
  ) : (
    <>
      <Button
        type="button"
        variant="outline"
        onClick={() => {
          form.reset({
            firstname: "",
            lastName: "",
            email: "",
            password: "",
            mobileNumber: "",
            address: "",
            role: "",
            isActive: true,
          });
          setIsUpdate(false); // Exit update mode
        }}
      >
        {t("cancel")}
      </Button>
      <Button variant="submit">
        {isUpdate === true ? t("update") : t("newUser")}
      </Button>
    </>
  )}
</div>

          </div>
        </form>
      </Form>
    </div>
  );
};

export default UserRegisterForm;
