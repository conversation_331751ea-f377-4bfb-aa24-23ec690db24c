import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import AuthController from "@/controller/AuthController"; // Import AuthController
import { ApiResponse } from "@/model/auth/authServices";
import { toast } from "@/components/ui/use-toast";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Eye, EyeOff, Loader2 } from "lucide-react"; // Import Eye and EyeOff icons
import logo from "@/assets/img/homepage/logo.png";
import BreadCrumb from "../../components/custom/BreadCrumb";
import { useUpdatePermissions } from "@/context/PermissionContext";
import { useNotifications } from "@/context/NotificationContext";
import Navbar from "@/components/custom/Navbar";
import Header from "@/components/custom/Header";

// regex of password

const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // Indicates if OTP is fully entered
  const {connectToSSE}=useNotifications();
  const updatePermissions = useUpdatePermissions();

  useEffect(() => {
    const isAuthenticated = !!localStorage.getItem("UserData");
    if (isAuthenticated) {
      navigate("/dashboard", { replace: true });
    }
  }, [navigate]);
  const passwordSchema = z.string();

  const emailSchema = z
    .string()
    .refine((email) => email.trim() !== "", {
      message: t("validations.email.emailRequiredError"),
    })
    .refine((email) => /\S+@\S+\.\S+/.test(email), {
      message: t("validations.email.emailInvalidError"),
    });

  const schema = z.object({
    email: emailSchema,
    password: passwordSchema,
    otp: z.string().optional(),
  });

  const [showAdditionalField, setShowAdditionalField] = useState(false);
  const [isOtpComplete, setIsOtpComplete] = useState(false);
  const [otpValue, setOtpValue] = useState("");

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
      password: "",
      otp: "",
    },
  });
  const {
    formState: { errors },
  } = form;

  const [loader, setLoader] = useState(false);
  const [resendOtploader, setResendOtpLoader] = useState(false);

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>
  ) => {
    setLoader(true);
    if (!showAdditionalField) {
      const Data1 = {
        email: data?.email,
        password: data?.password,
      };
      setLoader(true);

      AuthController.signIn(Data1, (response: ApiResponse) => {
        console.log("responseee-->", response);
        if (response.status && response.data.statusCode === 201) {
          setShowAdditionalField(true);

          // Assume the OTP is returned in the response data as `response.otp`
          const otpFromMessage = response.data.message.split(" ").pop();

          if (otpFromMessage) {
            // Auto-fill OTP
            setOtpValue(otpFromMessage);
            setIsOtpComplete(true); // Mark the OTP as complete
            form.setValue("otp", otpFromMessage); // Set OTP in form state
          }

          toast({
            title: otpFromMessage,
            variant: "success",
          });
          setLoader(false);
        } else {
          toast({
            title: t("api.loginfailed"),
            variant: "destructive",
          });
          setLoader(false);
        }
      });
    } else {
      // Validate OTP
      const Data = {
        email: data?.email,
        otp: data?.otp,
      };
      setLoader(true);

      AuthController.validateOtp(Data, (response) => {
        if (response.statusCode && response.statusCode === 201) {
          setLoader(false);
          console.log("responseee login", response.data);
          connectToSSE(response?.data?.accessToken);

          updatePermissions(response.data.user?.Permissions);
          localStorage.setItem(
            "permissions",
            JSON.stringify(response.data.user?.Permissions)
          );
          navigate(`/dashboard`);
        } else {
          toast({
            title: "Invalid Otp",
            variant: "destructive",
          });
          setLoader(false);
        }
      });
    }
  };

  // Handle Resend OTP
  const handleResendOTP = () => {
    const { email, password } = form.getValues();
    const Data1 = {
      email: email,
      password: password,
    };
    form.resetField("otp"); // Reset the OTP field
    setIsOtpComplete(false); // Set OTP completion state to false
    setOtpValue(""); // Reset OTP value
    setResendOtpLoader(true);
    AuthController.signIn(Data1, (response: ApiResponse) => {
      if (response.status && response.data.statusCode === 201) {
        setShowAdditionalField(true);
        const otpFromMessage = response.data.message.split(" ").pop();

        if (otpFromMessage) {
          // Auto-fill OTP
          setOtpValue(otpFromMessage);
          setIsOtpComplete(true); // Mark the OTP as complete
          form.setValue("otp", otpFromMessage); // Set OTP in form state
        }
        toast({
          title: response.data.message,
          variant: "success",
        });
        setResendOtpLoader(false);
      } else {
        toast({
          title: response.message,
          variant: "destructive",
        });
        setResendOtpLoader(false);
      }
    });
  };

  const [isPasswordVisible, setIsPasswordVisible] = React.useState(false);

  const handleTogglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handlenavigate = () => {
    navigate("/");
  };

  return (

 

      <div className="  bg-blue-100 flex items-center flex-1 justify-center py-12 px-4">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden md:flex sm:w-3/4 w-full max-w-5xl">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="sm:p-12 p-8 w-full md:w-full"
            >
              <h2 className="text-2xl font-semibold mb-6">
                {t("loginHeading")}
              </h2>
              {/* <p className="text-zinc-600 mb-4">{t("loginSubHeading")}</p> */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("loginEmailLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={t("loginEmailPlaceholder")}
                        className={`${errors.email ? "border-red-500" : ""} border-GraphiteSec`}
                        {...field}
                      />
                    </FormControl>
                    {errors.email && (
                      <FormMessage className="ml-1"></FormMessage>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="py-1">
                    <FormLabel>{t("loginPasswordLabel")}</FormLabel>
                    <FormControl>
                      <div className="relative">
                        {" "}
                        <input
                          placeholder={t("loginPasswordPlaceholder")}
                          className="border-GraphiteSec flex h-10 w-full rounded-md border-[1.5px] border-[#0000006b] bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[#94A3B7] focus-visible:ring-1 focus-visible:ring-GraphiteSec focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 "
                          {...field}
                          type={isPasswordVisible ? "text" : "password"}
                        />
                        <div>
                          {isPasswordVisible ? (
                            <Eye
                              size={20} // Customize size as needed
                              onClick={handleTogglePasswordVisibility}
                              style={{
                                position: "absolute",
                                top: "50%",
                                right: "10px",
                                transform: "translateY(-50%)",
                                cursor: "pointer",
                              }}
                            />
                          ) : (
                            <EyeOff
                              size={20}
                              onClick={handleTogglePasswordVisibility}
                              style={{
                                position: "absolute",
                                top: "50%",
                                right: "10px",
                                transform: "translateY(-50%)",
                                cursor: "pointer",
                              }}
                            />
                          )}
                        </div>
                      </div>
                    </FormControl>
                    {errors.password && (
                      <FormMessage className="ml-1"></FormMessage>
                    )}
                  </FormItem>
                )}
              />
              {showAdditionalField && (
                <FormField
                  control={form.control}
                  name="otp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("loginOTP")}</FormLabel>

                      <FormControl>
                        <div className=" py-1 ">
                          <InputOTP
                            autoFocus
                            maxLength={6}
                            value={otpValue}
                            onChange={(value) => {
                              setOtpValue(value);
                              if (value.length === 6) {
                                setIsOtpComplete(true);
                              } else {
                                setIsOtpComplete(false);
                              }
                            }}
                            onComplete={(otp) => {
                              field.onChange(otp);
                              form.handleSubmit(onSubmit)();
                            }}
                          >
                            <InputOTPGroup className="gap-6  w-full pl-1">
                              <InputOTPSlot index={0} className="border-2  " />
                              <InputOTPSlot index={1} className="border-2" />
                              <InputOTPSlot index={2} className="border-2" />
                              {/* </InputOTPGroup>
                        <InputOTPSeparator />
                        <InputOTPGroup> */}
                              <InputOTPSlot index={3} className="border-2" />
                              <InputOTPSlot index={4} className="border-2" />
                              <InputOTPSlot index={5} className="border-2" />
                            </InputOTPGroup>
                          </InputOTP>
                        </div>
                      </FormControl>
                      {errors.otp && (
                        <FormMessage className="ml-1"></FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              )}
              <div className="my-4">
                {loader ? (
                  <Button disabled className="w-full">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin " />
                    {t("pleaseWait")}
                  </Button>
                ) : (
                  <Button
                    disabled={showAdditionalField && !isOtpComplete}
                    type="submit"
                    variant="submit"
                    className="w-full"
                  >
                    {t("loginBtn")}
                  </Button>
                )}
              </div>
              <div className="flex justify-between items-center">
                {showAdditionalField && (
                  <div
                    onClick={handleResendOTP}
                    className="cursor-pointer text-blue-600"
                  >
                    Resend OTP
                  </div>
                )}
                <div
                  className="ml-auto cursor-pointer text-blue-600"
                  onClick={() => navigate("/forgot-password")}
                >
                  {t("loginForgotPasswordLink")}
                </div>
              </div>
            </form>
          </Form>
          <div className="w-full md:w-full bg-blue-50 p-8 flex flex-col items-center justify-center">
            <div className="flex items-center justify-center mb-6 w-40 h-40 cursor-pointer">
              <img
                src={logo}
                alt="Logo"
                className="w-full h-full"
                onClick={() => handlenavigate()}
              />
            </div>
            <h3 className="text-3xl !font-bold mb-3 text-center">
              {/* {t("loginClassicalContentText")} */}
              {t("HeaderSectionLogoname")}
            </h3>
            <p className="text-zinc-600 text-sm text-center">
              {t("loginClassicalContentText")}
            </p>
          </div>
        </div>
      </div>
  
  );
};

export default LoginPage;
