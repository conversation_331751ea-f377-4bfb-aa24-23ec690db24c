import axios from "axios";
import {
  GET_DETAILS_FOR_NAMUNA_EIGHT,
  GET_DETAILS_FOR_NAMUNA_NINE,
} from "@/constant/utils/reportUtils";
const baseURL = import.meta.env.VITE_APP_BASE_URL;
const VITE_BASE_URL = baseURL;

const REACT_APP_GET_DETAILS_FOR_NAMUNA_EIGHT =
  VITE_BASE_URL + GET_DETAILS_FOR_NAMUNA_EIGHT;
const REACT_APP_GET_DETAILS_FOR_NAMUNA_NINE =
  VITE_BASE_URL + GET_DETAILS_FOR_NAMUNA_NINE;

class ReportApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getMilkatKarAkarni = async (
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.get(
        `${REACT_APP_GET_DETAILS_FOR_NAMUNA_EIGHT}`,
        {
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
        }
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };
  static getNamunaNine = async (
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.get(
        `${REACT_APP_GET_DETAILS_FOR_NAMUNA_NINE}`,
        {
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
        }
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static getVarshiKarAkarniReport = async (
    queryString: string,
    selectedFinancialYear: string,
    page: number,
    limit: number
  ) => {
    try {
      const url = `${VITE_BASE_URL}/v1/annual-kar-akarani?${queryString}&fy=${selectedFinancialYear}&page=${page}&limit=${limit}`;

      const response = await axios.get(url.toString(), {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${ReportApi.getStoredToken()}`,
        },
      });
      return response;
    } catch (err: any) {
      console.error(err);
    }
  };

  static getMilkatAkarniReport = async (
    queryParams: any,
    page: number,
    limit: number
  ) => {
    try {
      const url = `${VITE_BASE_URL}/v1/milkatKar?page=${page}&limit=${limit}`;

      const response = await axios.get(url.toString(), {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${ReportApi.getStoredToken()}`,
        },
        params: queryParams,
      });
      return response;
    } catch (err: any) {
      console.error(err);
    }
  };

  static printNamunaEightReport = async (params: any) => {
    try {
      const url = `${VITE_BASE_URL}/v1/register/print_namuna_eight`;

      // Create notification before starting the request
  

      const response = await axios.post(
        url.toString(),
        {}, // Empty body since data is in query params
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/pdf", // Correct MIME type for PDF
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
          params: params, // Include params as query parameters
          responseType: "arraybuffer", // Ensure the response is treated as an array buffer
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );

      if (response && response.data) {
        console.log("response--<--", response);

        return { status: true, data: response.data };
      } else {
        return { status: false, data: null };
      }
    } catch (err: any) {
      console.error("Error fetching report:", err);
      return { status: false, data: null };
    }
  };

    static printAllNamunaEightReport = async (params: any) => {
    try {
      const url = `${VITE_BASE_URL}/v1/register/print_namuna_eight`;

      const response = await axios.post(
        url.toString(),
        {}, // Empty body since data is in query params
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/pdf", // Correct MIME type for PDF
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
          params: params, // Include params as query parameters
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );

      if (response && response.data) {
        console.log("response--<--", response);

        return { status: true, data: response.data };
      } else {
        return { status: false, data: null };
      }
    } catch (err: any) {
      console.error("Error fetching report:", err);
      return { status: false, data: null };
    }
  };
  static printNamunaNineReport = async (params: any) => {
    try {
      const url = `${VITE_BASE_URL}/v1/register/print_namuna_nine`;


      const response = await axios.post(
        url.toString(),
        {}, // Empty body since data is in query params
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
          params: params, // Include params as query parameters
          responseType: "arraybuffer", // Ensure the response is treated as an array buffer
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );
      if (response) {
        return { status: true, data: response };
      } else {
        return { status: false, data: response };
      }
    } catch (err: any) {
      return { status: false, data: [] };
    }
  };
  static printAllNamunaNineReport = async (params: any) => {
    try {
      const url = `${VITE_BASE_URL}/v1/register/print_namuna_nine`;


      const response = await axios.post(
        url.toString(),
        {}, // Empty. The data is sent as query parameters.
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/pdf",
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
          params: params, // Include params as query parameters
          responseType: "arraybuffer", // Ensure the response is treated as an array buffer
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );
      if (response) {
        return { status: true, data: response.data };
      } else {
        return { status: false, data: response };
      }
    } catch (err: any) {
      return { status: false, data: [] };
    }
  };
  static printBill = async (param: any) => {
    try {
      const url = `${VITE_BASE_URL}/v1/billing/get-bill_by_PropertyNumber`;

      const response = await axios.post(
        url.toString(),
        {}, // Empty body since data is in query params
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/pdf", // Correct MIME type for PDF
            Authorization: `Bearer ${ReportApi.getStoredToken()}`,
          },
          params: param, // Include params as query parameters
          responseType: "arraybuffer", // Ensure the response is treated as an array buffer
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
        }
      );

      if (response && response.data) {
        console.log("response--<--", response);

        return { status: true, data: response.data };
      } else {
        return { status: false, data: null };
      }
    } catch (err: any) {
      console.error("Error fetching report:", err);
      return { status: false, data: null };
    }
  };

  static downloadMilkatKarReport = async (financialYear: string, onDownloadProgress: (progressEvent: any) => void) => {
    const token = ReportApi.getStoredToken();
    return axios.get(
      `${VITE_BASE_URL}/v1/reports/milkat-kar-akarani/export-excel`,
      {
        params: { financialYear },
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Authorization: `Bearer ${token}`,
        },
        onDownloadProgress: onDownloadProgress,
      }
    );
  };

  static downloadVarshikKarReport = async (financialYear: string, onDownloadProgress: (progressEvent: any) => void) => {
    const token = ReportApi.getStoredToken();
    return axios.get(
      `${VITE_BASE_URL}/v1/reports/warshik-kar-akarani/export-excel`,
      {
        params: { financialYear },
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Authorization: `Bearer ${token}`,
        },
        onDownloadProgress: onDownloadProgress,
      }
    );
  };

  static downloadPaidUserReport = async (financialYear: string, onDownloadProgress: (progressEvent: any) => void) => {
    const token = ReportApi.getStoredToken();
    return axios.get(
      `${VITE_BASE_URL}/v1/reports/paid-users/export-excel`, // Placeholder URL
      {
        params: { financialYear },
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Authorization: `Bearer ${token}`,
        },
        onDownloadProgress: onDownloadProgress,
      }
    );
  };

  static downloadNotPaidUserReport = async (financialYear: string, onDownloadProgress: (progressEvent: any) => void) => {
    const token = ReportApi.getStoredToken();
    return axios.get(
      `${VITE_BASE_URL}/v1/reports/not-paid-users/export-excel`, // Placeholder URL
      {
        params: { financialYear },
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Authorization: `Bearer ${token}`,
        },
        onDownloadProgress: onDownloadProgress,
      }
    );
  };

  static downloadPropertyReport = async (financialYear: string, onDownloadProgress: (progressEvent: any) => void) => {
    const token = ReportApi.getStoredToken();
    return axios.get(
      `${VITE_BASE_URL}/v1/reports/property/export-excel`, // Placeholder URL
      {
        params: { financialYear },
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          Authorization: `Bearer ${token}`,
        },
        onDownloadProgress: onDownloadProgress,
      }
    );
  };
}

export default ReportApi;
