import { map } from 'rxjs/operators';
import { Json } from 'aws-sdk/clients/robomaker';
import { Length } from 'class-validator';
import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  MilkatKareRepository,
  PropertyTypeClassMasterRepository,
  PropertyTypeMasterRepository,
  PreviousOwnerRepository,
  PropertyMasterRepository,
  ReassessmentRangeRepository,
} from 'libs/database/repositories';
import path from 'path';
import fs from 'fs';
import ejs from 'ejs';
import axios from 'axios';
import FormData from 'form-data';
import { BillingService } from 'src/billing/billing.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { Response } from 'express';
import { TAX_TYPES } from '@helper/helpers/tax-types.helper';
import { scale } from 'pdf-lib';

@Injectable()
export class RegisterService {
  constructor(
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly propertyTypeClassRepository: PropertyTypeClassMasterRepository,
    private readonly milkatKareRepository: MilkatKareRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,

    private readonly previousOwnerRepository: PreviousOwnerRepository,
    private readonly billingService: BillingService,
    private readonly reAssesmentRepo: ReassessmentRangeRepository,
    private readonly notificationsService: NotificationsService,
  ) {}

  async findAll() {
    try {
      const getAllData =
        await this.propertyTypeMasterRepository.findAllLocation();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  // async printNamunaEight(params, res: Response) {
  //   const { searchOn, value, fy, ...options } = params;

  //   try {
  //     const getData: any = await this.milkatKareRepository.getAssesmentData(
  //       value,
  //       searchOn,
  //       fy,
  //     );
  //     const floorOrder = ['तळ मजला', 'पहिला मजला', 'दुसरा मजला', 'तिसरा मजला'];

  //     if (getData.length === 0) {
  //       return res.status(404).json({ message: 'No data found' });
  //     }

  //     const taxTypes = {
  //       tax_type_8: 'पडसर कर',
  //       tax_type_1: 'वृक्ष उपकार',
  //       tax_type_2: 'शिक्षण उपकर',
  //       tax_type_3: 'रो. हमी उपकर',
  //       tax_type_10: 'अग्नीशमन फी',
  //       tax_type_4: 'घनकचरा सेवा कर',
  //       tax_type_5: 'शास्ती फी',
  //       // tax_type_6: 'दिवाबत्ती कर',
  //       // tax_type_7: 'आरोग्य कर',
  //       // tax_type_9: 'दंड',
  //     };

  //     const marathiDataArray = [];
  //     const sendingDataToPdf = [];
  //     let currentData = new Date();
  //     let day = String(currentData.getDate()).padStart(2, '0');
  //     let month = String(currentData.getMonth() + 1).padStart(2, '0');
  //     let year = currentData.getFullYear();
  //     let formattedDate = `${day}-${month}-${year}`;

  //     for (const data of getData.slice(0, 5)) {
  //       const propertyId = data.property.property_id;
  //       const previousOwner =
  //         await this.previousOwnerRepository.findByProperty(propertyId);
  //       data.previousOwner = previousOwner;

  //       data.milkatKarTax.sort((a, b) => {
  //         const floorA = a.property_usage_details?.floorType?.floor_name || '';
  //         const floorB = b.property_usage_details?.floorType?.floor_name || '';

  //         const indexA = floorOrder.indexOf(floorA);
  //         const indexB = floorOrder.indexOf(floorB);

  //         if (indexA === -1 && indexB === -1) return 0;
  //         if (indexA === -1) return 1;
  //         if (indexB === -1) return -1;

  //         return indexA - indexB;
  //       });

  //       data.property.property_owner_details.sort((a, b) => {
  //         const dateA = new Date(a.createdAt);
  //         const dateB = new Date(b.createdAt);
  //         return dateA.getTime() - dateB.getTime();
  //       });

  //       const marathiData = await this.billingService.convertJson(data);
  //       marathiDataArray.push(marathiData);
  //       sendingDataToPdf.push({
  //         fileData: marathiData,
  //         tax_types: taxTypes,
  //         currentDate: formattedDate,
  //       });
  //     }

  //     //    return res
  //     // .status(200)
  //     // .json({
  //     //   fileData: marathiDataArray,
  //     //   tax_types: taxTypes,
  //     //   currentDate: formattedDate,
  //     // });

  //     const templatePath = path.join(
  //       __dirname,
  //       'templates',
  //       'NamunaEightReport.ejs',
  //     );
  //     const template = fs.readFileSync(templatePath, 'utf-8');

  //     const htmlOutputArray = ejs.render(template, {
  //       fileData: marathiDataArray,
  //       tax_types: taxTypes,
  //       currentDate: formattedDate,
  //     });
  //     //  return res.send(sendingDataToPdf.slice(0,10));

  //     const generatedHtmlDir = path.join(
  //       __dirname,
  //       'templates',
  //       'generatedhtml',
  //     );
  //     if (!fs.existsSync(generatedHtmlDir)) {
  //       fs.mkdirSync(generatedHtmlDir, { recursive: true });
  //     }

  //     const formData = new FormData();
  //     const filePath = path.join(generatedHtmlDir, `index.html`);
  //     fs.writeFileSync(filePath, htmlOutputArray);
  //     formData.append('file', fs.createReadStream(filePath), `index.html`);
  //     //  return res.send(htmlOutputArray);

  //     const apiUrl = process.env.REPORT_EIGHT_URL;
  //     console.log('API Response:', apiUrl);
  //     const response = await axios.post(apiUrl, formData, {
  //       headers: {
  //         ...formData.getHeaders(),
  //       },
  //       responseType: 'arraybuffer', // Ensure the response is treated as a binary stream
  //     });

  //     // Clean up temporary file
  //     fs.unlinkSync(filePath);
  //     console.log("resposneee->",response);

  //     // Set headers for PDF download
  //     res.setHeader('Content-Type', 'application/pdf');
  //     res.setHeader(
  //       'Content-Disposition',
  //       'inline; filename="NamunaEightReport.pdf"',
  //     );
  //     res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  //     res.setHeader('Pragma', 'no-cache');
  //     res.setHeader('Expires', '0');
  //     res.status(200).send(response.data);
  //     // Stream the PDF content to the client
  //   } catch (error) {
  //     console.error('Error:', error);
  //     res
  //       .status(500)
  //       .json({ message: 'Internal Server Error', error: error.message });
  //   }
  // }

  async printNamunaEight(params, res: Response, userId?: string) {
    const { searchOn, value, fy } = params;
    const currentReassesmentRange =
      await this.reAssesmentRepo.getReassessmentRangeByYear(fy);
    if (!currentReassesmentRange) {
      return {
        message: 'Reassessment range not found for the given financial year',
        data: {
          property_count: 0,
          property_id: '',
        },
      };
    }
    try {
 

      const getData: any = await this.milkatKareRepository.getAssesmentData(
        value,
        searchOn,
        fy,
        currentReassesmentRange?.reassessment_range_id,
      );
      const floorOrder = ['तळ मजला', 'पहिला मजला', 'दुसरा मजला', 'तिसरा मजला'];

      if (getData.length === 0) {
        return res.status(404).json({ message: 'No data found' });
      }

      // res.json(getData);
console.log("getDatagetData",JSON.stringify(getData));
      const propertyId = getData[0].property.property_id;
      const previousOwner =
        await this.previousOwnerRepository.findByProperty(propertyId);
      getData[0].previousOwner = previousOwner;

      const taxTypes = {
        tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
        tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
        tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
        tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर

        tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
        tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
        tax_type_5: TAX_TYPES.tax_type_5,
      };
      const marathiDataArray = [];
      const sendingDataToPdf = [];
      const currentData = new Date();
      const day = String(currentData.getDate()).padStart(2, '0');
      const month = String(currentData.getMonth() + 1).padStart(2, '0');
      const year = currentData.getFullYear();
      const formattedDate = `${day}-${month}-${year}`;

      for (const data of getData) {
        const propertyId = data.property.property_id;
        const previousOwner =
          await this.previousOwnerRepository.findByProperty(propertyId);
        data.previousOwner = previousOwner;

        data.milkatKarTax.sort((a, b) => {
          const floorA = a.property_usage_details?.floorType?.floor_name || '';
          const floorB = b.property_usage_details?.floorType?.floor_name || '';
          const indexA = floorOrder.indexOf(floorA);
          const indexB = floorOrder.indexOf(floorB);
          if (indexA === -1 && indexB === -1) return 0;
          if (indexA === -1) return 1;
          if (indexB === -1) return -1;
          return indexA - indexB;
        });

       if (data.milkatKarTax && Array.isArray(data.milkatKarTax)) {
    data.milkatKarTax.forEach((karTax) => {
      if (karTax.tax !== undefined && karTax.property_type_discount !== undefined) {
        karTax.tax = karTax.tax - karTax.property_type_discount;
        data.total_tax = data.total_tax - karTax.property_type_discount;
      } else {
        console.error('Missing tax or property_type_discount field in karTax:', karTax);
      }
    });
  }

        data.property.property_owner_details.sort((a, b) => {
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        });

        const marathiData = await this.billingService.convertJson(data);
        marathiDataArray.push(marathiData);
        sendingDataToPdf.push({
          fileData: [marathiData],
          tax_types: taxTypes,
          currentDate: formattedDate,
        });
      }

      const formData = new FormData();

      if (getData.length < 10) {
        // Render HTML file and convert to PDF
        const templatePath = path.join(
          __dirname,
          'templates',
          'NamunaEightReport.ejs',
        );
        const template = fs.readFileSync(templatePath, 'utf-8');
 console.log("marathiDataArray--->",JSON.stringify(marathiDataArray));
        const htmlOutputArray = ejs.render(template, {
          fileData: marathiDataArray,
          tax_types: taxTypes,
          currentDate: formattedDate,
        });
      console.log(
  "marathiDataArray --->",
  JSON.stringify(
    {
      fileData: marathiDataArray,
      tax_types: taxTypes,
      currentDate: formattedDate,
    },
    null, // Replacer (not needed)
    2     // Indentation for readability
  )
);



        // return res.status(200).send(htmlOutputArray);


        const generatedHtmlDir = path.join(
          __dirname,
          'templates',
          'generatedhtml',
        );
        if (!fs.existsSync(generatedHtmlDir)) {
          fs.mkdirSync(generatedHtmlDir, { recursive: true });
        }

        const filePath = path.join(generatedHtmlDir, `index.html`);
        fs.writeFileSync(filePath, htmlOutputArray);

        formData.append(
          'html_file',
          fs.createReadStream(filePath),
          'index.html',
        );

        // Add the additional parameters
        formData.append('paperWidth	', '22'); // A4 width in mm
        formData.append('paperHeight', '5'); // A4 height in mm (or increase if needed)
        formData.append('scale', '0.5'); // Scale down content to fit
        formData.append('marginTop', '0.5');
        formData.append('marginBottom', '0.5');

        // Optional: to prevent page breaks
        formData.append('preferCssPageSize', 'true');
        formData.append('printBackground', 'true');

        const apiUrl = process.env.CONVERT_SINGLEHTML_TO_PDF;

        const response = await axios.post(apiUrl, formData, {
          headers: {
            ...formData.getHeaders(),
          },
          responseType: 'arraybuffer',
        });

        fs.unlinkSync(filePath); // cleanup
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
          'Content-Disposition',
          'inline; filename="NamunaEightReport.pdf"',
        );
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        return res.status(200).send(response.data);
      } else {
        // Generate notification ID for tracking
        const notificationId = `namuna_eight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create offline notification for tracking
                  console.log("Creatong notigaion id hereee",userId)

        if (userId) {

          console.log("Creatong notigaion id hereee",userId)
          await this.notificationsService.createOfflineNotification(
            userId,
            notificationId,
            'Namuna Eight Report Generation',
            'Your ZIP file is being generated. You will be notified when it\'s ready for download.',
            'progress',
            {
              reportType: 'namuna_eight',
              totalRecords: getData.length,
              processedRecords: 0
            }
          );

          // Send initial real-time notification if user is online
          this.notificationsService.sendNotification(userId, {
            type: 'notification_update',
            notificationId,
            data: {
              status: 'processing',
              progress: 0,
              message: 'Starting ZIP file generation for Namuna Eight reports...',
              metadata: {
                reportType: 'namuna_eight',
                totalRecords: getData.length,
                processedRecords: 0
              }
            }
          });
        }
console.log("111111111111111--->")
        const sendingData = {
          template_name: 'NamunaEightReport',
          data: sendingDataToPdf,
          output_filename: 'property_reports.pdf',
          orientation: 'landscape',
          page_width: 11.0,
          page_height: 8.5,
          margin_top: 0.5,
          margin_bottom: 0.5,
          margin_left: 0.5,
          margin_right: 0.5,

         
webhook_url : `${process.env.BASE_URL}/api/v1/notifications/webhook`,
          webhook_id: notificationId,
        };

        try {
          // Send progress update
          if (userId) {
            this.notificationsService.sendProgressUpdate(userId, notificationId, 25, 0, getData.length);
          }
          
console.log("1111111111111222--->")

          // Send request to PDF microservice with webhook URL

                  const apiUrl = process.env.CONVERT_MULTIPLEEJS_TO_PDF;
            // const apiUrl ='http://***************:8000/convert/ejs-batch-data' 

          const response = await axios.post(
           apiUrl,
            sendingData,
            {
              headers: {
                'Content-Type': 'application/json',
              },
timeout: 240000 // 2 minutes

            },

          );
console.log("111111111111133--->",response)

          // Send progress update
          if (userId) {
            this.notificationsService.sendProgressUpdate(userId, notificationId, 50, getData.length / 2, getData.length);
          }

          console.log('Request sent to PDF microservice:', response.data);

          // Return success response immediately
          res.status(200).json({
            success: true,
            message: 'Report generation started. You will be notified when the ZIP file is ready for download.',
            notificationId: notificationId,
            totalRecords: getData.length
          });

        } catch (error) {
          console.error('Error sending request to PDF microservice:', error);

          // Send error notification
          if (userId) {
            this.notificationsService.sendErrorNotification(
              userId,
              notificationId,
              'Failed to start ZIP file generation. Please try again.'
            );
          }

          res.status(500).json({
            success: false,
            message: 'Failed to start report generation',
            error: error.message
          });
        }
      }
    } catch (error) {
      console.error('Error:-->', error);
      res
        .status(500)
        .json({ message: 'Internal Server Error', error: error.message });
    }
  }

  //   async printNamunaEight(params, res: Response) {
  //     const { searchOn, value, fy } = params;
  //     const currentReassesmentRange =
  //     await this.reAssesmentRepo.getReassessmentRangeByYear(fy);
  //     if (!currentReassesmentRange) {
  //       return {
  //         message: 'Reassessment range not found for the given financial year',
  //         data: {
  //           property_count: 0,
  //           property_id: '',
  //         },
  //       };
  //     }
  //     try {
  //       if (searchOn === 'ward' || searchOn === 'name') {
  //         return res.json({
  //           message: 'Hi there',
  //           data: [],
  //         });
  //       }

  //       const getData: any = await this.milkatKareRepository.getAssesmentData(
  //         value,
  //         searchOn,
  //         fy,
  //         currentReassesmentRange?.reassessment_range_id
  //       );
  //       const floorOrder = ["तळ मजला", "पहिला मजला", "दुसरा मजला", "तिसरा मजला"];

  //       if (getData.length === 0) {
  //         return res.status(404).json({ message: 'No data found' });
  //       }

  //       // res.json(getData);

  //       const propertyId = getData[0].property.property_id;
  //       const previousOwner =
  //         await this.previousOwnerRepository.findByProperty(propertyId);
  //       getData[0].previousOwner = previousOwner;

  //       const taxTypes = {
  //         tax_type_8: 'पडसर कर',
  //         tax_type_1: 'वृक्ष उपकार',
  //         tax_type_2: 'शिक्षण उपकर',
  //         tax_type_3: 'रो. हमी उपकर',
  //         tax_type_10: 'अग्नीशमन फी',

  //         tax_type_4: 'घनकचरा सेवा कर',
  //         tax_type_5: 'शास्ती फी',
  //         // tax_type_6: 'दिवाबत्ती कर',
  //         // tax_type_7: 'आरोग्य कर',
  //         // tax_type_9: 'दंड',
  //       };

  //       getData[0].milkatKarTax.sort((a, b) => {
  //         const floorA = a.property_usage_details?.floorType?.floor_name || "";
  //         const floorB = b.property_usage_details?.floorType?.floor_name || "" ;

  //         const indexA = floorOrder.indexOf(floorA);
  //         const indexB = floorOrder.indexOf(floorB);

  //         if (indexA === -1 && indexB === -1) return 0;
  //         if (indexA === -1) return 1;
  //         if (indexB === -1) return -1;

  //         return indexA - indexB;
  //       });

  //       getData[0].property.property_owner_details.sort((a, b) => {
  //         const dateA = new Date(a.createdAt);
  //         const dateB = new Date(b.createdAt);
  //         return dateA.getTime() - dateB.getTime();
  //       });

  //       const marathiData = await this.billingService.convertJson(getData[0]);

  //       const templatePath = path.join(
  //         __dirname,
  //         'templates',
  //         'NamunaEightReport.ejs',
  //       );
  //       const template = fs.readFileSync(templatePath, 'utf-8');
  //             let currentData = new Date();
  //       let day = String(currentData.getDate()).padStart(2, '0');
  //       let month = String(currentData.getMonth() + 1).padStart(2, '0');
  //       let year = currentData.getFullYear();

  //       let formattedDate = `${day}-${month}-${year}`;

  //     // return res.status(200).json({
  //     //   fileData: [marathiData],
  //     //   tax_types: taxTypes,
  //     //   currentDate: formattedDate,
  //     // });

  //       const htmlOutput = ejs.render(template, {
  //         fileData: [marathiData],
  //         tax_types: taxTypes,
  //         currentDate: formattedDate,
  //       });

  //       // res
  //       // .status(200)
  //       // .json({ message: 'File sent to API', apiResponse: marathiData });
  // // res.send(htmlOutput);
  //       const generatedHtmlDir = path.join(
  //         __dirname,
  //         'templates',
  //         'generatedhtml',
  //       );
  //       if (!fs.existsSync(generatedHtmlDir)) {
  //         fs.mkdirSync(generatedHtmlDir, { recursive: true });
  //       }

  //       const filePath = path.join(generatedHtmlDir, 'index.html');
  //       fs.writeFileSync(filePath, htmlOutput);

  //       const formData = new FormData();
  //       formData.append('file', fs.createReadStream(filePath), 'index.html');

  //       const apiUrl = process.env.REPORT_EIGHT_URL;
  //       const response = await axios.post(apiUrl, formData, {
  //         headers: {
  //           ...formData.getHeaders(),
  //         },
  //       });

  //       res
  //         .status(200)
  //         .json({ message: 'File sent to API', apiResponse: htmlOutput });
  //     } catch (error) {
  //       console.error('Error:', error);
  //       res
  //         .status(500)
  //         .json({ message: 'Internal Server Error', error: error.message });
  //     }
  //   }

  async calculateFormDataSize(formData) {
    return new Promise((resolve, reject) => {
      formData.getLength((err, length) => {
        if (err) {
          console.error('Error calculating FormData size:', err);
          reject(err);
        } else {
          resolve(length);
        }
      });
    });
  }

  // async printNamunaNine(params, res: Response) {
  //   const { searchOn, value, fy, ...options } = params;

  //   try {
  //     if (searchOn === 'ward' || searchOn === 'name') {
  //       return res.json({
  //         message: 'Hi there',
  //         data: [],
  //       });
  //     }

  //     const getData: any =
  //       await this.propertyMasterRepository.getRegisterNineData(
  //         value,
  //         searchOn,
  //         fy,
  //         options,
  //       );
  //     if (getData.total=== 0) {
  //       return res.status(404).json({ message: 'No data found' });
  //     }
  //     //  return res.status(200).json(getData);

  //     const taxTypes = {
  //       tax_type_8: 'पडसर कर',
  //       tax_type_1: 'वृक्ष उपकार',
  //       tax_type_2: 'शिक्षण उपकर',
  //       tax_type_3: 'रो. हमी उपकर',
  //       tax_type_10: 'अग्नीशमन फी',

  //       tax_type_4: 'घनकचरा सेवा कर',
  //       tax_type_5: 'शास्ती फी',
  //       // tax_type_6: 'दिवाबत्ती कर',
  //       // tax_type_7: 'आरोग्य कर',
  //       // tax_type_9: 'दंड',
  //     };
  //     getData.data[0].demandReportData.forEach(report => {
  //       const propertyTypeDiscount = parseFloat(report?.property_type_discount) || 0;

  //       report.all_property_tax_sum_curr_remaining = (
  //           parseFloat(report.all_property_tax_sum_curr_remaining) +
  //           propertyTypeDiscount
  //       ).toString();
  //       report.all_property_tax_sum_remaining = (
  //         parseFloat(report.all_property_tax_sum_remaining) +
  //         propertyTypeDiscount
  //     ).toString();

  //   });

  //     const marathiData = await this.billingService.convertJson(
  //       getData.data[0],
  //     );
  //     //  return res.status(200).json(marathiData);

  //     const templatePath = path.join(__dirname, 'templates', 'namunanine.ejs');
  //     const template = fs.readFileSync(templatePath, 'utf-8');
  //     // return res.status(200).json({fileData:marathiData, tax_types: taxTypes});
  //     if (!marathiData?.payments) {
  //       return res.status(200).json({ message: 'No Payment', data: [] });
  //     }
  //     const htmlOutput = ejs.render(template, {
  //       fileData: marathiData,
  //       tax_types: taxTypes,
  //     });

  //     // res.send(marathiData);
  //     // return res.status(200).json({fileData:marathiData, tax_types: taxTypes});

  //     // res.send(htmlOutput);
  //     // return res.status(200).send(htmlOutput);
  //     const generatedHtmlDir = path.join(
  //       __dirname,
  //       'templates',
  //       'generatedhtml',
  //     );
  //     if (!fs.existsSync(generatedHtmlDir)) {
  //       fs.mkdirSync(generatedHtmlDir, { recursive: true });
  //     }

  //     const filePath = path.join(generatedHtmlDir, 'index.html');
  //     fs.writeFileSync(filePath, htmlOutput);

  //     const formData = new FormData();
  //     formData.append('file', fs.createReadStream(filePath), 'index.html');

  //     const apiUrl = process.env.REPORT_NINE_URL;
  //     const response = await axios.post(apiUrl, formData, {
  //       headers: {
  //         ...formData.getHeaders(),
  //       },
  //     });
  //     // res.send(htmlOutput);

  //     res
  //       .status(200)
  //       .json({ message: 'File sent to API', apiResponse: htmlOutput });
  //   } catch (error) {
  //     console.error('Error:', error);
  //     res
  //       .status(500)
  //       .json({ message: 'Internal Server Error', error: error.message });
  //   }
  // }

//   async printNamunaNine(params, res: Response, userId?: string) {
//     console.log("paramsparams",params)
//     const { searchOn, value, fy, ...options } = params;

//     try {
//       // if (searchOn === 'ward' || searchOn === 'name') {
//       //   return res.json({
//       //     message: 'Hi there',
//       //     data: [],
//       //   });
//       // }

//       const getData: any =
//         await this.propertyMasterRepository.getRegisterNineData(
//           value,
//           searchOn,
//           fy,
//           options,
//         );

//       if (getData.total === 0) {
//         return res.status(404).json({ message: 'No data found' });
//       }

//       const taxTypes = {
//         tax_type_8: 'पडसर कर',
//         tax_type_1: 'वृक्ष उपकार',
//         tax_type_2: 'शिक्षण उपकर',
//         tax_type_3: 'रो. हमी उपकर',
//         tax_type_10: 'अग्नीशमन फी',
//         tax_type_4: 'घनकचरा सेवा कर',
//         tax_type_5: 'शास्ती फी',
//       };

//       getData.data.forEach((dataItem) => {
//         dataItem.demandReportData.forEach((report) => {
//           const propertyTypeDiscount =
//             parseFloat(report?.property_type_discount) || 0;
//           report.all_property_tax_sum_curr_remaining = (
//             parseFloat(report.all_property_tax_sum_curr_remaining) +
//             propertyTypeDiscount
//           ).toString();
//           report.all_property_tax_sum_remaining = (
//             parseFloat(report.all_property_tax_sum_remaining) +
//             propertyTypeDiscount
//           ).toString();
          
//             const totalRemaining = parseFloat(report.total_amount_remaining) || 0;
//     const totalPaid = parseFloat(report.total_amount_paid) || 0;
//     report.remaining_amount = (totalRemaining - totalPaid).toString();
//         });
//       });

//       const marathiDataArray = [];
//       const sendingDataToPdf = [];
//       const currentData = new Date();
//       const day = String(currentData.getDate()).padStart(2, '0');
//       const month = String(currentData.getMonth() + 1).padStart(2, '0');
//       const year = currentData.getFullYear();
//       const formattedDate = `${day}-${month}-${year}`;
//       console.log('get data length', getData.data);

//       if(getData.data.Length <=1 && getData.data[0].payments?.length <=0){
//         return res.status(404).json({ message: 'No Payments found' });

//       }

//       for (const data of getData.data) {
//         const marathiData = await this.billingService.convertJson(data);
//         marathiDataArray.push(marathiData);
//         sendingDataToPdf.push({
//           fileData: marathiData,
//           tax_types: taxTypes,
//           currentDate: formattedDate,
//         });
//       }
//             // return res.status(200).json(sendingDataToPdf);

//       if (getData.data.length < 20) {
//         const templatePath = path.join(
//           __dirname,
//           'templates',
//           'namunanine.ejs',
//         );
//         console.log("herrrr===1111",JSON.stringify(marathiDataArray[0]));
//         const template = fs.readFileSync(templatePath, 'utf-8');
//         const htmlOutput = ejs.render(template, {
//           fileData: marathiDataArray[0],
//           tax_types: taxTypes,
//           currentDate: formattedDate,
//         });

//         const generatedHtmlDir = path.join(
//           __dirname,
//           'templates',
//           'generatedhtml',
//         );
//         if (!fs.existsSync(generatedHtmlDir)) {
//           fs.mkdirSync(generatedHtmlDir, { recursive: true });
//         }

//         const filePath = path.join(generatedHtmlDir, 'index.html');
//                 fs.writeFileSync(filePath, htmlOutput);


//         const formData = new FormData();

//         formData.append(
//           'html_file',
//           fs.createReadStream(filePath),
//           'index.html',
//         );

//         // Add the additional parameters
//         formData.append('paperWidth	', '22'); // A4 width in mm
//         formData.append('paperHeight', '5'); // A4 height in mm (or increase if needed)
//         formData.append('scale', '0.5'); // Scale down content to fit
//         formData.append('marginTop', '0.5');
//         formData.append('marginBottom', '0.5');

//         // Optional: to prevent page breaks
//         formData.append('preferCssPageSize', 'true');
//         formData.append('printBackground', 'true');

//         const apiUrl = process.env.CONVERT_SINGLEHTML_TO_PDF;
// console.log("herrrr===1111")
//         const response = await axios.post(apiUrl, formData, {
//           headers: {
//             ...formData.getHeaders(),
//           },
//           responseType: 'arraybuffer',
//         });

//         fs.unlinkSync(filePath); // cleanup
//         res.setHeader('Content-Type', 'application/pdf');
//         res.setHeader(
//           'Content-Disposition',
//           'inline; filename="NamunaNineReport.pdf"',
//         );
//         res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//         res.setHeader('Pragma', 'no-cache');
//         res.setHeader('Expires', '0');
//         return res.status(200).send(response.data);

//         // "======"
//         // fs.writeFileSync(filePath, htmlOutput);

//         // const formData = new FormData();
//         // formData.append('file', fs.createReadStream(filePath), 'index.html');

//         // const apiUrl = process.env.REPORT_NINE_URL;
//         // const response = await axios.post(apiUrl, formData, {
//         //     headers: {
//         //         ...formData.getHeaders(),
//         //     },
//         // });

//         // fs.unlinkSync(filePath);
//         // res.status(200).json({ message: 'File sent to API', apiResponse: response.data });
//       } else {
//         // Generate notification ID for tracking
//         const notificationId = `namuna_nine_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

//         // Create offline notification for tracking
//         if (userId) {
//           await this.notificationsService.createOfflineNotification(
//             userId,
//             notificationId,
//             'Namuna Nine Report Generation',
//             'Your ZIP file is being generated. You will be notified when it\'s ready for download.',
//             'progress',
//             {
//               reportType: 'namuna_nine',
//               totalRecords: getData.data.length,
//               processedRecords: 0
//             }
//           );

//           // Send initial real-time notification if user is online
//           this.notificationsService.sendNotification(userId, {
//             type: 'notification_update',
//             notificationId,
//             data: {
//               status: 'processing',
//               progress: 0,
//               message: 'Starting ZIP file generation for Namuna Nine reports...',
//               metadata: {
//                 reportType: 'namuna_nine',
//                 totalRecords: getData.data.length,
//                 processedRecords: 0
//               }
//             }
//           });
//         }

//         console.log("sendingDataToPdfsendingDataToPdf",sendingDataToPdf)
//         const sendingData = {
//           template_name: 'namunanine',
//           data: sendingDataToPdf,
//           output_filename: 'property_reports.pdf',
//           orientation: 'landscape',
//           page_width: 11.0,
//           page_height: 8.5,
//           margin_top: 0.5,
//           margin_bottom: 0.5,
//           margin_left: 0.5,
//           margin_right: 0.5,
//           webhook_url: `${process.env.BASE_URL}/api/v1/notifications/webhook`,
//           notification_id: notificationId,
//         };
// console.log("herrrr===222")

//         try {
//           // Send progress update
//           if (userId) {
//             this.notificationsService.sendProgressUpdate(userId, notificationId, 25, 0, getData.data.length);
//           }

//           // Send request to PDF microservice with webhook URL
//           const response = await axios.post(
//             'http://34.31.120.147:4001/convert/ejs-batch-data',
//             sendingData,
//             {
//               headers: {
//                 'Content-Type': 'application/json',
//               },
//             },
//           );

//           // Send progress update
//           if (userId) {
//             this.notificationsService.sendProgressUpdate(userId, notificationId, 50, getData.data.length / 2, getData.data.length);
//           }

//           console.log('Request sent to PDF microservice:', response.data);

//           // Return success response immediately
//           res.status(200).json({
//             success: true,
//             message: 'Report generation started. You will be notified when the ZIP file is ready for download.',
//             notificationId: notificationId,
//             totalRecords: getData.data.length
//           });

//         } catch (error) {
//           console.error('Error sending request to PDF microservice:', error);

//           // Send error notification
//           if (userId) {
//             this.notificationsService.sendErrorNotification(
//               userId,
//               notificationId,
//               'Failed to start ZIP file generation. Please try again.'
//             );
//           }

//           res.status(500).json({
//             success: false,
//             message: 'Failed to start report generation',
//             error: error.message
//           });
//         }
//       }
//     } catch (error) {
//       console.error('Error:', error);
//       res
//         .status(500)
//         .json({ message: 'Internal Server Error', error: error.message });
//     }
//   }

// with notification
  async printNamunaNine(params, res: Response, userId?: string) {
    const { searchOn, value, fy, ...options } = params;

    try {
      // if (searchOn === 'ward' || searchOn === 'name') {
      //   return res.json({
      //     message: 'Hi there',
      //     data: [],
      //   });
      // }

      const getData: any =
        await this.propertyMasterRepository.getRegisterNineData(
          value,
          searchOn,
          fy,
          options,
        );

      if (getData.total === 0) {
        return res.status(404).json({ message: 'No data found' });
      }

      // Generate notification ID for tracking
            const taxTypes = {
        tax_type_8: 'पडसर कर',
        tax_type_1: 'वृक्ष उपकार',
        tax_type_2: 'शिक्षण उपकर',
        tax_type_3: 'रो. हमी उपकर',
        tax_type_10: 'अग्नीशमन फी',
        tax_type_4: 'घनकचरा सेवा कर',
        tax_type_5: 'शास्ती फी',
      };

      getData.data.forEach((dataItem) => {
        dataItem.demandReportData.forEach((report) => {
          const propertyTypeDiscount =
            parseFloat(report?.property_type_discount) || 0;
          report.all_property_tax_sum_curr_remaining = (
            parseFloat(report.all_property_tax_sum_curr_remaining) +
            propertyTypeDiscount
          ).toString();
          report.all_property_tax_sum_remaining = (
            parseFloat(report.all_property_tax_sum_remaining) +
            propertyTypeDiscount
          ).toString();

                report.remaining_amount = (
            parseFloat(report.total_amount_remaining) -
            parseFloat(report.total_amount_paid)-parseFloat(report.other_discount)
          ).toString();
          //conveting in english
                    report.property_type_discount = (
            parseFloat(report.property_type_discount || 0) +
            parseFloat(report.other_discount || 0)
          ).toString();


        });
      });

      const marathiDataArray = [];
      const sendingDataToPdf = [];
      const currentData = new Date();
      const day = String(currentData.getDate()).padStart(2, '0');
      const month = String(currentData.getMonth() + 1).padStart(2, '0');
      const year = currentData.getFullYear();
      const formattedDate = `${day}-${month}-${year}`;
      console.log('get data length', getData.data[0]);

      // if(!getData.data.payments || getData.data.payments?.length <=0){
      //   return res.status(404).json({ message: 'No Payments found' });


      // }
      console.log("getData.data.payments",getData.data[0].payments)

      for (const data of getData.data) {
        if(data.payments?.length <=0){
          continue;
           }
        const marathiData = await this.billingService.convertJson(data);
        marathiDataArray.push(marathiData);
        sendingDataToPdf.push({
          fileData: marathiData,
          tax_types: taxTypes,
          currentDate: formattedDate,
        });
      }
            // return res.status(200).json(sendingDataToPdf);

      if (getData.data.length < 20) {
        const templatePath = path.join(
          __dirname,
          'templates',
          'namunanine.ejs',
        );
        const template = fs.readFileSync(templatePath, 'utf-8');
        const htmlOutput = ejs.render(template, {
          fileData: marathiDataArray[0],
          tax_types: taxTypes,
          currentDate: formattedDate,
        });

        const generatedHtmlDir = path.join(
          __dirname,
          'templates',
          'generatedhtml',
        );
        if (!fs.existsSync(generatedHtmlDir)) {
          fs.mkdirSync(generatedHtmlDir, { recursive: true });
        }

        const filePath = path.join(generatedHtmlDir, 'index.html');
                fs.writeFileSync(filePath, htmlOutput);


        const formData = new FormData();

        formData.append(
          'html_file',
          fs.createReadStream(filePath),
          'index.html',
        );

        // Add the additional parameters
        formData.append('paperWidth	', '24'); // A4 width in mm
        formData.append('paperHeight', '5'); // A4 height in mm (or increase if needed)
        formData.append('scale', '0.6'); // Scale down content to fit
        formData.append('marginTop', '0.5');
        formData.append('marginBottom', '0.5');

        // Optional: to prevent page breaks
        formData.append('preferCssPageSize', 'true');
        formData.append('printBackground', 'true');

        const apiUrl = process.env.CONVERT_SINGLEHTML_TO_PDF;
console.log("herrrr===1111")
        const response = await axios.post(apiUrl, formData, {
          headers: {
            ...formData.getHeaders(),
          },
          responseType: 'arraybuffer',
        });

        fs.unlinkSync(filePath); // cleanup
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
          'Content-Disposition',
          'inline; filename="NamunaNineReport.pdf"',
        );
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        return res.status(200).send(response.data);

        // "======"
        // fs.writeFileSync(filePath, htmlOutput);

        // const formData = new FormData();
        // formData.append('file', fs.createReadStream(filePath), 'index.html');

        // const apiUrl = process.env.REPORT_NINE_URL;
        // const response = await axios.post(apiUrl, formData, {
        //     headers: {
        //         ...formData.getHeaders(),
        //     },
        // });

        // fs.unlinkSync(filePath);
        // res.status(200).json({ message: 'File sent to API', apiResponse: response.data });
      } else {

const notificationId = `namuna_nine_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // Create offline notification for tracking
      console.log("userId---->",userId)
      if (userId) {
              console.log("userId----> 2",userId)

        await this.notificationsService.createOfflineNotification(
          userId,
          notificationId,
          'Namuna Nine Report Generation',
          'Your ZIP file is being generated. You will be notified when it\'s ready for download.',
          'progress',
          {
            reportType: 'namuna_nine',
            totalRecords: getData.data.length,
            processedRecords: 0
          }
        );

        // Send initial real-time notification if user is online
        this.notificationsService.sendNotification(userId, {
          type: 'notification_update',
          notificationId,
          data: {
            status: 'processing',
            progress: 0,
            message: 'Starting ZIP file generation for Namuna Nine reports...',
            metadata: {
              reportType: 'namuna_nine',
              totalRecords: getData.data.length,
              processedRecords: 0
            }
          }
        });
      }


    
        const sendingData = {
          template_name: 'namunanine',
          data: sendingDataToPdf,
          output_filename: 'property_reports.pdf',
          orientation: 'Portrait',
          page_width: 20.0,
          page_height: 8.5,
          scale: 0.5,
          margin_top: 0.5,
          margin_bottom: 0.5,
          margin_left: 0.5,
          margin_right: 0.5,
          webhook_url: `${process.env.BASE_URL}/api/v1/notifications/webhook`,
          notification_id: notificationId,
        };
console.log("herrrr===222")

        try {
          // Send progress update
          if (userId) {
            this.notificationsService.sendProgressUpdate(userId, notificationId, 25, 0, getData.data.length);
          }

                            const apiUrl = process.env.CONVERT_MULTIPLEEJS_TO_PDF;


          const response = await axios.post(
            apiUrl,
            sendingData,
            {
              responseType: 'arraybuffer',
            },
          );

          // Send progress update
          if (userId) {
            this.notificationsService.sendProgressUpdate(userId, notificationId, 50, getData.data.length / 2, getData.data.length);
          }

          const zipFolderPath = path.join(__dirname, 'zipFolder');
          if (!fs.existsSync(zipFolderPath)) {
            fs.mkdirSync(zipFolderPath);
          }
                    console.log("zipFolderPath",zipFolderPath)

          const outputPath = path.join(zipFolderPath, 'output.zip');
          fs.writeFileSync(outputPath, response.data);

                              console.log("outputPath",outputPath)

         res.download(outputPath, 'property_reports.zip', (err) => {
          if (err) {
              console.error("Error downloading file:", err);
              if (userId) {
                this.notificationsService.sendErrorNotification(
                  userId,
                  notificationId,
                  'Failed to download ZIP file'
                );
              }
          } else {
              console.log("File downloaded successfully");
              if (userId) {
                this.notificationsService.sendCompletionNotification(
                  userId,
                  notificationId,
                  `/notifications/download/${notificationId}`,
                  'property_reports.zip'
                );
              }
              fs.unlinkSync(outputPath);
          }
      });

        } catch (error) {
          console.error('Error downloading the ZIP file:', error);
          res
            .status(500)
            .json({ message: 'Internal Server Error', error: error.message });
        }
      }
    } catch (error) {
      console.error('Error:', error);
      res
        .status(500)
        .json({ message: 'Internal Server Error', error: error.message });
    }
  }

  async getAssessmentReport(params: any) {
    try {
      const { searchOn, value, fy, page = 1, limit = 10 } = params;

      // Get reassessment range for the financial year
      const currentReassesmentRange =
        await this.reAssesmentRepo.getReassessmentRangeByYear(fy);
      if (!currentReassesmentRange) {
        throw new NotFoundException(
          'Reassessment range not found for the given financial year',
        );
      }

      // Get assessment data with pagination
      const getData = await this.milkatKareRepository.getAssesmentData(
        value,
        searchOn,
        fy,
        currentReassesmentRange.reassessment_range_id,
      );

      if (!getData || getData.length === 0) {
        throw new NotFoundException('Assessment data not found');
      }

      return {
        message: 'Assessment report data fetched successfully',
        data: getData,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: getData.length,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async printAssessmentReport(params: any, res: Response) {
    try {
      const { searchOn, value, fy } = params;

      // Get reassessment range for the financial year
      const currentReassesmentRange =
        await this.reAssesmentRepo.getReassessmentRangeByYear(fy);
      if (!currentReassesmentRange) {
        return res
          .status(404)
          .json({
            message:
              'Reassessment range not found for the given financial year',
          });
      }

      // Get assessment data for printing
      const getData = await this.milkatKareRepository.getAssesmentData(
        value,
        searchOn,
        fy,
        currentReassesmentRange.reassessment_range_id,
      );

      if (!getData || getData.length === 0) {
        return res.status(404).json({ message: 'No assessment data found' });
      }

      // Convert data to Marathi format
      const marathiData = await this.billingService.convertJson(getData[0]);

      // Generate HTML template for assessment report
      const templatePath = path.join(
        __dirname,
        'templates',
        'AssessmentReport.ejs',
      );

      let template;
      try {
        template = fs.readFileSync(templatePath, 'utf-8');
      } catch (templateError) {
        // If template doesn't exist, use a basic template
        template = `
          <html>
            <head>
              <title>Assessment Report</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .property-info { margin-bottom: 20px; }
                .assessment-details { margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>Assessment Report</h1>
                <p>Property Assessment Details</p>
              </div>
              <div class="property-info">
                <h3>Property Information</h3>
                <p><strong>Property Number:</strong> <%= data.property?.propertyNumber || 'N/A' %></p>
                <p><strong>Owner Name:</strong> <%= data.property?.property_owner_details?.[0]?.ownerName || 'N/A' %></p>
                <p><strong>Address:</strong> <%= data.property?.address || 'N/A' %></p>
              </div>
              <div class="assessment-details">
                <h3>Assessment Details</h3>
                <table>
                  <tr>
                    <th>Assessment Year</th>
                    <td><%= data.year || 'N/A' %></td>
                  </tr>
                  <tr>
                    <th>Total Tax</th>
                    <td><%= data.total_tax || 'N/A' %></td>
                  </tr>
                  <tr>
                    <th>Status</th>
                    <td><%= data.status || 'N/A' %></td>
                  </tr>
                </table>
              </div>
            </body>
          </html>
        `;
      }

      const htmlOutput = ejs.render(template, { data: marathiData });

      // Create directory for generated HTML if it doesn't exist
      const generatedHtmlDir = path.join(
        __dirname,
        'templates',
        'generatedhtml',
      );
      if (!fs.existsSync(generatedHtmlDir)) {
        fs.mkdirSync(generatedHtmlDir, { recursive: true });
      }

      const filePath = path.join(generatedHtmlDir, 'assessment-report.html');
      fs.writeFileSync(filePath, htmlOutput);

      const formData = new FormData();
      formData.append(
        'file',
        fs.createReadStream(filePath),
        'assessment-report.html',
      );

      // Use existing report URL or create a new one for assessment reports
      const apiUrl =
        process.env.REPORT_ASSESSMENT_URL || process.env.REPORT_EIGHT_URL;

      if (apiUrl) {
        const response = await axios.post(apiUrl, formData, {
          headers: {
            ...formData.getHeaders(),
          },
        });

        // Clean up temporary file
        fs.unlinkSync(filePath);

        res
          .status(200)
          .json({
            message: 'Assessment report generated successfully',
            apiResponse: htmlOutput,
          });
      } else {
        // If no API URL is configured, return the HTML directly
        res
          .status(200)
          .json({
            message: 'Assessment report generated successfully',
            htmlContent: htmlOutput,
          });
      }
    } catch (error) {
      console.error('Error generating assessment report:', error);
      res
        .status(500)
        .json({ message: 'Internal Server Error', error: error.message });
    }
  }
}
