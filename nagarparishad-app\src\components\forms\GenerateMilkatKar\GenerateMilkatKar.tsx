import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { MASTER } from "@/constant/config/api.config";
import { ColumnDef } from "@tanstack/react-table";
// import { t } from "i18next";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import DialogDemo from "@/components/globalcomponent/GlobalDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/use-toast";
import { z } from "zod";
import { Skeleton } from "@/components/ui/skeleton";
import { useMilkatKarController } from "@/controller/tax/MilkatKarController";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import PropertyApi from "@/services/PropertyServices";

const FormSchema = z.object({
  reassessmentYearId: z.string({
    required_error: "A reassessment year is required.",
  }),
});

const GenerateMilkatKar = () => {
  const componentRef = useRef(null);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [selectedReassessmentYear, setSelectedReassessmentYear] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [newDialogOpen, setNewDialogOpen] = useState(false);
  const [wardWiseGenerating, setWardWiseGenerating] = useState<{[key: string]: boolean}>({});
  const [currentReassessmentYear, setCurrentReassessmentYear] = useState("");

  const {
    reassessmentRanges,
    reassessmentRangesLoading,
    milkatKarData,
    milkatKarDataLoading,
    processMilkatKar,
    refetchMilkatKarData,
  } = useMilkatKarController();

  const { wardData, wardLoading } = useWardMasterController();
  const [wardWiseData, setWardWiseData] = useState([]);
  const [wardWiseLoading, setWardWiseLoading] = useState(false);

  // Debug logs
  useEffect(() => {
    console.log("Ward data:", wardData);
    console.log("Ward loading:", wardLoading);
    console.log("Reassessment ranges:", reassessmentRanges);
  }, [wardData, wardLoading, reassessmentRanges]);

  // Set current reassessment year (latest one)
  useEffect(() => {
    if (Array.isArray(reassessmentRanges) && reassessmentRanges.length > 0) {
      const latestYear = reassessmentRanges[0]; // Assuming first one is latest
      setCurrentReassessmentYear(latestYear.reassessment_range_id || latestYear.reassessment_id);
      console.log("Current reassessment year set:", latestYear);
    }
  }, [reassessmentRanges]);

  // Fetch ward-wise data for current reassessment year
  const fetchWardWiseData = async () => {
    console.log("fetchWardWiseData called", { currentReassessmentYear });

    if (!currentReassessmentYear) {
      console.log("No current reassessment year available");
      return;
    }

    setWardWiseLoading(true);
    try {
      // Call the real API to get ward-wise status
      PropertyApi.getWardWiseMilkatKarStatus(currentReassessmentYear, (response) => {
        if (response.status && response.data.statusCode === 200) {
          console.log("Ward wise data from API:", response.data.data);
          setWardWiseData(response.data.data);
        } else {
          console.error("Failed to fetch ward-wise data:", response.data);
          // Fallback to mock data if API fails
          createFallbackData();
        }
        setWardWiseLoading(false);
      });
    } catch (error) {
      console.error("Error fetching ward-wise data:", error);
      // Fallback to mock data if API fails
      createFallbackData();
      setWardWiseLoading(false);
    }
  };

  // Create fallback mock data
  const createFallbackData = () => {
    const mockWardData = [
      { ward_id: "1", ward_name: "प्रभाग १", total_properties: 150, generated_count: 75, remaining_count: 75 },
      { ward_id: "2", ward_name: "प्रभाग २", total_properties: 200, generated_count: 120, remaining_count: 80 },
      { ward_id: "3", ward_name: "प्रभाग ३", total_properties: 180, generated_count: 180, remaining_count: 0 },
      { ward_id: "4", ward_name: "प्रभाग ४", total_properties: 120, generated_count: 60, remaining_count: 60 },
    ];
    setWardWiseData(mockWardData);
    console.log("Using fallback mock data:", mockWardData);
  };

  // Fetch ward-wise data when current reassessment year changes
  useEffect(() => {
    fetchWardWiseData();
  }, [currentReassessmentYear]);

  // Calculate summary totals from ward-wise data
  const calculateSummaryTotals = () => {
    if (!Array.isArray(wardWiseData) || wardWiseData.length === 0) {
      return {
        totalProperties: 0,
        totalGenerated: 0,
        totalRemaining: 0,
        overallProgress: 0
      };
    }

    const totals = wardWiseData.reduce((acc, ward) => {
      acc.totalProperties += ward.total_properties || 0;
      acc.totalGenerated += ward.generated_count || 0;
      acc.totalRemaining += ward.remaining_count || 0;
      return acc;
    }, { totalProperties: 0, totalGenerated: 0, totalRemaining: 0 });

    const overallProgress = totals.totalProperties > 0
      ? (totals.totalGenerated * 100) / totals.totalProperties
      : 0;

    return {
      ...totals,
      overallProgress: Math.round(overallProgress)
    };
  };

  const summaryTotals = calculateSummaryTotals();

  const generateMilkatKar = async () => {
    if (!selectedReassessmentYear) {
      toast({
        title: "Please select a reassessment year",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      processMilkatKar(
        { reassessmentYearId: selectedReassessmentYear },
        {
          onSuccess: () => {
            toast({
              title: "Milkat Kar generated successfully",
              variant: "success",
            });
            refetchMilkatKarData();
            setIsGenerating(false);
            handleCloseNew();
          },
          onError: (error: any) => {
            console.error("Error generating Milkat Kar:", error);
            toast({
              title: "Failed to generate Milkat Kar",
              description: error.message || "An unexpected error occurred",
              variant: "destructive",
            });
            setIsGenerating(false);
            handleCloseNew();
          },
        }
      );
    } catch (error) {
      console.error("Error generating Milkat Kar:", error);
      toast({
        title: "Failed to generate Milkat Kar",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
      setIsGenerating(false);
      handleCloseNew();
    }
  };

  const generateMilkatKarByWard = async (wardName: string) => {
    if (!currentReassessmentYear) {
      toast({
        title: "No current reassessment year found",
        variant: "destructive",
      });
      return;
    }

    setWardWiseGenerating(prev => ({ ...prev, [wardName]: true }));

    try {
      // Call the processMilkatKarByWard API with ward_number parameter
      PropertyApi.processMilkatKarByWard(wardName, currentReassessmentYear, (response) => {
        if (response.status && response.data.statusCode === 200) {
          toast({
            title: `Milkat Kar generated successfully for Ward ${wardName}`,
            variant: "success",
          });
          refetchMilkatKarData();
          fetchWardWiseData(); // Refresh ward-wise data
          setWardWiseGenerating(prev => ({ ...prev, [wardName]: false }));
        } else {
          toast({
            title: `Failed to generate Milkat Kar for Ward ${wardName}`,
            description: response.data.message || "An unexpected error occurred",
            variant: "destructive",
          });
          setWardWiseGenerating(prev => ({ ...prev, [wardName]: false }));
        }
      });
    } catch (error) {
      console.error(`Error generating Milkat Kar for ward ${wardName}:`, error);
      toast({
        title: `Failed to generate Milkat Kar for Ward ${wardName}`,
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
      setWardWiseGenerating(prev => ({ ...prev, [wardName]: false }));
    }
  };

  function handleOpenNew() {
    setNewDialogOpen(true);
  }

  function handleCloseNew() {
    setNewDialogOpen(false);
  }

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });



  // Ward-wise columns
  const wardWiseColumns: ColumnDef<any>[] = [
    {
      accessorKey: "sr_no",
      header: "अ.क्र.",
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "ward_name",
      header: () => {
        return (
          <Button
            className="px-0 justify-start text-base text-left  font-semibold"
            variant="ghost"
          >
            प्रभागाचे नाव
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="capitalize   font-medium">
          {row.original?.ward_name || ""}
        </div>
      ),
    },
    {
      accessorKey: "total_properties",
      header: () => {
        return (
          <Button
            className="px-0  text-base font-semibold"
            variant="ghost"
          >
            एकूण मालमत्ता
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="">{row.original?.total_properties || 0}</div>
      ),
    },
    {
      accessorKey: "generated_count",
      header: () => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            तयार केलेले
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className=" text-green-600 font-medium">
          {row.original?.generated_count || 0}
        </div>
      ),
    },
    {
      accessorKey: "remaining_count",
      header: () => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            उर्वरित
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className=" text-orange-600 font-medium">
          {row.original?.remaining_count || 0}
        </div>
      ),
    },
    {
  accessorKey: "progress",
  header: () => {
    return (
      <Button
        className="px-0 justify-start text-base font-semibold"
        variant="ghost"
      >
        प्रगती
      </Button>
    );
  },
  cell: ({ row }: { row: any }) => {
    const total = row?.original?.total_properties || 1;
    const generated = row?.original?.generated_count || 0;

    const percentage = total > 0 ? (generated * 100) / total : 0;

    // 👇 Force 100% only when exactly completed
    const isComplete = generated === total;
    const displayPercent = isComplete ? 100 : Math.floor(percentage);

    return (
      <div className="flex items-center gap-2">
        <div className="w-full bg-zinc-200 rounded-full h-2.5">
          <div
            className="bg-[#238c41] h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="text-sm font-medium min-w-[40px]">
          {displayPercent}%
        </span>
      </div>
    );
  },
},

  ];

  return (
    <div className="flex h-fit">
      <div
        className="w-full mx-auto px-4 sm:px-6 lg:px-5 py-6"
        ref={componentRef && componentRef}
      >
        <h1 className="text-2xl font-semibold font-Poppins w-full ml-3">
         

          मालमत्तांकरिता मिळकत कर संख्येचा अहवाल
        </h1>
        <div>
          <WhiteContainer>
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-blue-600 mb-1">एकूण मालमत्ता</div>
                  <div className="text-2xl font-bold text-blue-900">{summaryTotals.totalProperties}</div>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-green-600 mb-1">तयार केलेले</div>
                  <div className="text-2xl font-bold text-green-900">{summaryTotals.totalGenerated}</div>
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-orange-600 mb-1">उर्वरित</div>
                  <div className="text-2xl font-bold text-orange-900">{summaryTotals.totalRemaining}</div>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-purple-600 mb-1">एकूण प्रगती</div>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl font-bold text-purple-900">  {summaryTotals.overallProgress.toFixed(2)}%</div>
                    <div className="flex-1 bg-purple-200 rounded-full h-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${summaryTotals.overallProgress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Ward-wise Details */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">
                    प्रभागनिहाय मालमत्तांकरिता करनिर्मितीची स्थिती
                  </h3>
                  <div className="text-sm text-gray-600">
                    सध्याचे पुनर्मूल्यांकन वर्ष: {
                      Array.isArray(reassessmentRanges) && reassessmentRanges.find((r: any) =>
                        (r.reassessment_range_id || r.reassessment_id) === currentReassessmentYear
                      )?.reassessment_range ||
                      (Array.isArray(reassessmentRanges) && reassessmentRanges.find((r: any) =>
                        (r.reassessment_range_id || r.reassessment_id) === currentReassessmentYear
                      )?.start_range + " ते " +
                      reassessmentRanges.find((r: any) =>
                        (r.reassessment_range_id || r.reassessment_id) === currentReassessmentYear
                      )?.end_range) || "निवडलेले नाही"
                    }
                  </div>
                </div>

                <TanStackTable
                  columns={wardWiseColumns}
                  data={Array.isArray(wardWiseData) ? wardWiseData : []}
                  masterType={MASTER.ZONE}
                  loader={wardWiseLoading ? true : false}
                />
              </div>
            </div>
          </WhiteContainer>
        </div>
      </div>


    </div>
  );
};

export default GenerateMilkatKar;
/*  */