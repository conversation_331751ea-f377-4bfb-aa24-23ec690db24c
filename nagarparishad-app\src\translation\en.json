{"username": "Username", "submit": "Submit", "formDescription": "Description", "Welcome to React": "Welcome to React", "greeting": "Greetings!", "navbar": {"downloadApp": "Download Mobile App", "mainTopics": "Main Topics", "fontSize": {"decrease": "A-", "normal": "A", "increase": "A+"}}, "search": "Search", "reset": "Reset", "searchBy": "Search By", "advancesearch": "Advanced Search", "searchOwnerName": "Property Owner", "back": "Back", "BillDetails": "<PERSON>", "errorsRequiredField": "This field is required.", "errorsInvalidInput": "Invalid input.", "errorsNetworkError": "Network error. Please try again later.", "errorsInternalServerError": "Internal server error. Please try again later.", "errorsUnauthorized": "Unauthorized access.", "errorsInvalidCredentials": "Invalid credentials.", "errorsEmailRequiredError": "Email is required.", "errorsPasswordRequiredError": "Password is required.", "errorsOtpRequiredError": "OTP is required.", "errorsInvalidEmailFormat": "Invalid email format.", "errorsPasswordStrengthError": "Password must be 8 characters long, with uppercase, lowercase, numbers, and special characters.", "errorsMobileNumber": "Phone number must be 10 digits long", "financialYearExists": "This financial year already exists", "HeaderSectionLogoname": "Shirol Municipal Council", "HeaderSectionSearch": "Search", "HeaderSectionNamelist": "Name List", "HeaderSectionLogin": "<PERSON><PERSON>", "removeOwner": "Property owner removed successfully", "addOwner": "Owner added successfully", "homepagePayOnline": "Pay Tax Online", "homepageNewCitizenServices": "New Citizen Services", "homepageAdditionalCollectorAccess": "Additional Collector Access", "homepageAddProperty": "Add Property", "homepageSearchProperty": "Search Property", "footerOrganizationName": "Shirol Municipal Council", "footerOrganizationDescription": "Empowering transparent governance and reducing household expenses.", "footerOrganizationAddress": "<PERSON>rol, Maharashtra 416103", "footerLinks": "Quick Links", "footerListItem1": "Government of Maharashtra", "footerListItem2": "Our Government", "footerListItem3": "View 7/12", "footerListItem4": "Kolhapur Police", "footerListItem5": "Kolhapur District Council", "footerListItem6": "Government Land Records", "footerContactInfo": "Contact Information", "footerPhoneNumber": "📞 Phone Number", "footerTechnicalSupport": "Technical Support", "footerEmail": "📧 Email", "footerCopyRight": "Copyright", "footerAllRightsReserved": "All Rights Reserved.", "footerContactNumber": "+91 9876543210", "footerLandLineNumber": "12345-67890", "footerEmailId": "<EMAIL>", "footerAddress": "<PERSON><PERSON><PERSON><PERSON>, MH SH 137, <PERSON><PERSON>", "footerOpening": "Monday - Saturday", "footerWebPolicies": "Website Policies", "footerSiteMap": "Site Map", "footerContactUs": "Contact Us", "footerDownloadApp": "Download Mobile App", "footerDesignBy": "Designed and Developed by", "paytax": "Pay Tax", "loginHeading": "<PERSON><PERSON>", "loginSubHeading": "Enter your credentials", "loginEmailLabel": "Email ID", "loginEmailPlaceholder": "Email ID", "loginPasswordLabel": "Password", "loginPasswordPlaceholder": "Password", "loginOTP": "OTP", "loginSubmit": "Submit", "loginOTPButton": "OTP", "loginForgotPasswordLink": "Forgot Password?", "loginEmailRequiredError": "Email is required", "loginPasswordRequiredError": "Password is required", "loginClassicalContentHeading": "Classical Content", "loginClassicalContentText": "Please note: This system is for authorized use only. Unauthorized access attempts may lead to legal action.", "loginBtn": "<PERSON><PERSON>", "pleaseWait": "Please Wait", "onlineFormSearchBy": "Search By", "onlineFormSelectOption": "Select Option", "onlineFormEnterValue": "Enter Value", "onlineFormSeeDetails": "See Details", "onlineFormAvailableProperties": "Available Properties", "onlineFormFullName": "Full Name", "onlineFormCardNumber": "Card Number", "onlineFormcvv": "CVV", "onlineFormExpiryDate": "Expiry Date", "onlineFormGetOTP": "Get OTP", "onlineFormVerifyPassword": "Verify Password", "onlineFormSendMoney": "Send Money", "onlineFormHouseType": "House Type", "onlineFormAddress": "Address", "advancedsearch": "Advanced Search", "onlineFormPhoneNumber": "Phone Number", "onlineFormTable": {"index": "Index"}, "propertysearchholder": "Search by Property Number/Phone Number", "propertyLocationFormWizardStepOne": "Step 1", "propertyLocationFormWizardStepTwo": "Step 2", "propertyLocationFormWizardStepThree": "Step 3", "propertyLocationFormWizardStepFour": "Step 4", "propertyLocationFormWizardStepOneTitle": "Property Location Details", "propertyLocationFormWizardStepTwoTitle": "Property Owner Information", "propertyLocationFormWizardStepThreeTitle": "Property Assessment Details", "propertyLocationFormWizardStepFourTitle": "Plot Details", "propertyLocationFormHeading": "Search Property", "propertyLocationFormSubHeading": "Property Location Information", "propertyregisteartion": "Property Registration Form", "propertyListing": "Available Properties", "SrNo": "Sr. No.", "Actions": "Actions", "PropertyOwner": "Property Owner", "Mobile": "Mobile Number", "enterFullName": "Enter your name", "openWebcam": "Open Webcam", "uploadedFiles": "Uploaded Files", "takePhoto": "Take Photo", "propertys": {"AddBtn": "Register Property", "update": "Update Property", "updateSuccessFully": "Property updated successfully"}, "propertyText": "Property", "wardName": "Ward Name", "paidTax": "Paid Tax", "remainderTax": "Remaining Tax", "enterPropertyNumber": "Enter Property Number or Old Property Number", "financialYear": "Financial Year", "noDataFound": "No data available", "selectFinancialYear": "Please select Financial Year", "selectYear": "Select Financial Year", "billGenerate": "Bill generated successfully", "viewBill": "View Bill", "zone": {"zoneTitle": "Zone Master", "searchPlaceholder": "Search Zone", "searchZone": "Search Zone", "zoneName": "Zone Name", "wardName": "Ward Name", "edit": "Edit", "delete": "Delete", "AddBtn": "Add Zone", "addZone": "Zone", "selectWard": "Select Ward", "formTitle": "Zone Master", "zoneLabel": "Zone", "wardLabel": "Ward", "updateBtn": "Update Zone", "selectZone": "Select Zone"}, "ward": {"wardTitle": "Ward Master", "searchPlaceholder": "Search Ward", "searchWard": "Search Ward", "wardName": "Ward Name", "edit": "Edit", "delete": "Delete", "AddBtn": "Add Ward", "selectWard": "Select Ward", "formTitle": "Ward Master", "wardLabel": "Ward", "updateBtn": "Update Ward"}, "location": {"locationTitle": "Location Master", "searchPlaceholder": "Search Location", "searchLocation": "Search Location", "locationName": "Location Name", "AddBtn": "Add Location", "edit": "Edit", "delete": "Delete", "locationLabel": "Location", "formTitle": "Location Master", "updateBtn": "Update Location"}, "street": {"streetTitle": "Street Master", "searchPlaceholder": "Search Street", "searchLocation": "Search Street", "streetName": "Street Name", "AddBtn": "Add Street", "edit": "Edit", "delete": "Delete", "streetLabel": "Street", "formTitle": "Street Master", "updateBtn": "Update Street"}, "propertytype": {"propertyTypeTitle": "Property Type Master", "searchPlaceholder": "Search Property Type", "searchLocation": "Search Property Type", "propertytypeName": "Property Type Name", "AddBtn": "Add Property Type", "edit": "Edit", "delete": "Delete", "propertytypeLabel": "Property Type", "formTitle": "Property Type Master", "updateBtn": "Update Property Type"}, "propertyclass": {"propertyTypeTitle": "Property Type Master", "searchPlaceholder": "Search Property Type", "searchLocation": "Search Property Type", "propertytypeName": "Property Type Name", "AddBtn": "Add Property Type", "edit": "Edit", "delete": "Delete", "propertytypeLabel": "Property Type", "formTitle": "Property Type Master", "updateBtn": "Update Property Type"}, "propertyfloor": {"propertyFloorTitle": "Property Floor Master", "searchPlaceholder": "Search Floor", "searchLocation": "Search Floor", "propertyfloorName": "Property Floor Name", "AddBtn": "Add Property Floor", "edit": "Edit", "delete": "Delete", "propertytypeLabel": "Property Type", "formTitle": "Property Type Master", "updateBtn": "Update Property Type", "floorLabel": "Floor"}, "propertysubtype": {"propertySubTypeTitle": "Property Sub-Type Master", "searchPlaceholder": "Search Property Sub-Type", "searchLocation": "Search Property Sub-Type", "propertysubtypeName": "Property Sub-Type Name", "AddBtn": "Add Property Sub-Type", "edit": "Edit", "delete": "Delete", "propertysubtypeLabel": "Property Sub-Type", "formTitle": "Property Sub-Type Master", "updateBtn": "Update Property Sub-Type"}, "areaorlocality": {"areaTitle": "Area Master", "searchPlaceholder": "Search Area", "searchLocation": "Search Area", "areaName": "Area Name", "AddBtn": "Add Area", "edit": "Edit", "delete": "Delete", "areaLabel": "Area", "formTitle": "Area Master", "updateBtn": "Update Area"}, "electionboundry": {"electionBoundaryTitle": "Election Boundary Master", "searchPlaceholder": "Search Election Boundary", "searchLocation": "Search Election Boundary", "AddBtn": "Add Election Boundary", "formTitle": "Election Boundary Master", "updateBtn": "Update Election Boundary", "electionBoundaryLabel": "Election Boundary", "boundaryLabel": "Election Boundary"}, "usage": {"usageTitle": "Usage Master", "searchPlaceholder": "Search Usage", "searchLocation": "Search Usage", "AddBtn": "Add Usage", "formTitle": "Usage Master", "updateBtn": "Update Usage", "UsageLabel": "Usage"}, "usage_sub": {"usageSubTitle": "Usage Sub Master", "searchPlaceholder": "Search", "searchLocation": "Search", "AddBtn": "Add", "formTitle": "Usage Sub Master", "updateBtn": "Update", "UsageLabel": "Usage Sub"}, "construction": {"constructionTitle": "Construction Master", "searchPlaceholder": "Search Construction", "searchLocation": "Search Construction Type", "constructionName": "Construction Type Name", "AddBtn": "Add Construction Type", "edit": "Edit", "delete": "Delete", "constructionLabel": "Construction Class", "formTitle": "Construction Master", "updateBtn": "Update Construction Type", "classNameLabel": "Construction Class", "classNamePlaceholder": "Enter Construction Class", "classNameMarathiLabel": "Construction Class in Marathi", "classNameMarathiPlaceholder": "Enter Construction Class in Marathi", "valueLabel": "Value", "valuePlaceholder": "Enter Value"}, "administrativeboundary": {"administrativeBoundaryTitle": "Administrative Boundary Master", "searchPlaceholder": "Search Administrative Boundary", "searchLocation": "Search Administrative Boundary Type", "boundaryName": "Administrative Boundary Type Name", "AddBtn": "Add Administrative Boundary", "edit": "Edit", "delete": "Delete", "boundaryLabel": "Administrative Boundary Type", "formTitle": "Administrative Boundary Master", "updateBtn": "Update Administrative", "classNameLabel": "Administrative Boundary Class Name", "classNamePlaceholder": "Enter Administrative Boundary Class Name", "classNameMarathiLabel": "Administrative Boundary Class Name in Marathi", "classNameMarathiPlaceholder": "Enter Administrative Boundary Class Name in Marathi", "valueLabel": "Value", "valuePlaceholder": "Enter Value", "administrativeboundaryColumn": "Administrative Boundary"}, "contactUs": {"addressTitle": "Address", "address": "<PERSON><PERSON><PERSON><PERSON>, MH SH 137, <PERSON><PERSON>", "emailTitle": "Email ID", "emailId": "<EMAIL>", "openingTitle": "Office Hours", "opening": "Monday - Saturday", "leaveCommentTitle": "Contact Us", "yourName": "Name", "yourNamePlaceholder": "Enter your name", "yourEmail": "Email", "yourEmailPlaceholder": "Enter your email", "yourSubject": "Subject", "yourSubjectPlaceholder": "Enter your subject", "yourMessage": "Message", "yourMessagePlaceholder": "Enter your message", "submitMessage": "Submit Message"}, "master": {"masterZone": "Zone", "masterLocation": "Location", "masterWard": "Ward", "masterStreet": "Street", "masterArea": "Area", "masterPropertyType": "Property Type", "masterPropertySubtype": "Property Subtype", "masterElectionBoundary": "Election Boundary", "masterUsage": "Usage", "masterUsageSub": "Usage Sub", "masterConstruction": "Construction", "masterAdministrativeBoundary": "Administrative Boundary", "masterReadyReckonerRate": "Market Value", "masterPropertyTaxRate": "Property Tax Rate", "propertyTaxRate": "Property Tax Rate", "floor": "Floor", "propertyclass": "Property Type Section", "collector": "Collector"}, "titles": {"dashboard": "Dashboard", "propertyRegistration": "Property Registration", "master": "Master", "transaction": "Transaction", "report": "Report", "logout": "Logout", "determinationDepartment": "Determination Department", "userRegister": "Register User", "roleRegistration": "Register Role", "taxGeneration": "Tax Generation", "namuna8": "Property Tax Assessment (8)", "namuna9": "Annual Tax Demand (9)", "namuna10": "Sample 10", "paymentBill": "Payment Bill", "paymentBillHistory": "<PERSON>", "settings": "Settings", "paymentReport": "Payment Report"}, "Bill": "Bill", "user": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phoneNumber": "Phone Number", "role": "Role", "search": "Search", "address": "Address", "password": "Password", "userTitle": "User Information"}, "propertyLocationDetailsForm": {"propertyLocationTitle": "Property Details", "propertyNumber": "Property Number", "buildingNumber": "Building Number", "citySurveyNumber": "City Survey Number", "propertyStatus": "Property Status", "detailsLocationProperty": "Property Location", "plotNumber": "Plot Number", "blockNumber": "Block Number", "houseNumber": "House Number", "location": "Location", "houseApartmentName": "House or Apartment Name", "streetRoad": "Street", "landmark": "Landmark", "country": "Country", "city": "City", "latitude": "Latitude", "longitude": "Longitude", "ward": "Ward", "zone": "Zone", "administrativeBoundary": "Administrative Boundary", "electionBoundary": "Election Boundary", "documentDetails": "Document Details", "uploadFile": "Upload File", "sequenceNumber": "Sequence Number", "propertyDescription": "Property Description", "DetailsOfLocationOfProperty": "Property Location", "area": "Area", "fileUploadTitle": "Property Documents", "propertyOldNumber": "Old Property Number", "gatNumber": "Gat No."}, "assetDetailsForm": {"assetDetailsTitle": "Asset Details", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "organization": "Organization", "mobileNumber": "Mobile Number", "email": "Email ID", "adharNumber": "<PERSON><PERSON><PERSON> Number", "panNumber": "PAN Number", "gender": "Gender", "ownerType": "Property Owner Type", "fullName": "Full Name", "marrageStatus": "Marital Status"}, "propertyAssessmentDetailsForm": {"propertyAssessmentDetailsTitle": "Property Assessment Details", "plotArea": "Plot Area", "propertyType": "Property Type", "propertySubType": "Property Subtype", "propertyUsageType": "Property Usage Type", "propertyUsageSubType": "Solid Waste Compaction", "billType": "<PERSON>", "deliveryType": "Delivery Type", "propertyCurrentAssessment": "Property Current Assessment", "firstAssessment": "First Assessment", "propertyConstructionStartDate": "Property Construction Start Date", "propertyConstructionEndDate": "Property Construction End Date"}, "plotDetailsForm": {"plotDetailsTitle": "Plot Details", "flatStatus": "Flat Status", "floorNumber": "Floor", "flatNumber": "Flat Number", "occupancyType": "Occupancy Type", "constructionClass": "Construction Class", "usageType": "Usage Type", "usageSubType": "Solid Waste Compaction", "taxStartDate": "Tax Start Date", "roomDetailsInfo": "Details Information", "roomDetails": "Details", "manualAreaFlag": "Manual Area Coordinates", "constructionArea": "Construction Area", "carpetArea": "Carpet Area", "exemptedArea": "Exempted Area", "assessableArea": "Assessable Area", "plotArea": "Plot Area", "landCost": "Land Cost", "standardRate": "Standard Rate", "annualRent": "Annual Rent", "capitalValue": "Capital Value", "remark": "Remark", "PropertyPhotographs": "Property Photographs"}, "property": {"AddBtn": "Add Property", "UpdateBtn": "Update Property", "nextBtn": "Next", "previousBtn": "Previous", "propertyNumberColumn": "Property Number", "propertyOwnerColumn": "Property Owner", "propertyTypeColumn": "Property Type", "propertyMobileColumn": "Mobile Number", "bhogwatadar": "Bhogwatadar", "oldPropertyNumber": "Old Property Number", "usageDetails": "Usage Type", "propertytype": "Property Type", "surveyAndGatNumber": "Survey Number and Gat No.", "bhogwatadarName": "Bhogwatadar Name"}, "propertyOwner": "Property Owner", "wardcolumn": "Ward", "zonecolumn": "Zone", "welcomeText": "Welcome, {{name}}", "goodMorning": "Good Morning!", "goodAfternoon": "Good Afternoon!", "goodnight": "Good Night!", "toDate": "To Date", "fromDate": "From Date", "totalProperty": "Total Property", "propertySummary": "Property Summary", "taxCollected": "Tax Collected", "taxisdue": "Tax Due", "Fullfilledvsreality": "Fulfilled vs Reality", "Typeofproperty": "Types of Property", "Recenttransactionscollectedbycollectors": "Recent Transactions Collected by Collectors", "propertyDetails": "Property Details", "ward_name": "Ward", "noResults": "No records found", "availableAssets": "Available Assets", "searchward": "Search Ward", "searchZone": "Search Zone", "searchLocation": "Search Location", "searchArea": "Search Area", "searchStreet": "Search Street", "searchPropertyType": "Search Property Type", "searchPropertySubType": "Search Property Subtype", "searchElectionBoundry": "Search Election Boundary", "searchUsage": "Search Usage", "searchUsageSubtype": "Search Solid Waste Compaction", "searchAdministrativeBoundry": "Search Administrative Boundary", "searchConstructionClass": "Search Construction Class", "searchPropertyOwner": "Search Property Owner", "payTaxListTitle": "Property Owners Who Paid Tax on Time", "dueTaxListTitle": "Property Owners Who Did Not Pay Tax on Time", "viewMore": "View More Details", "searchReadyRecoknerRate": "Search Market Value", "searchFirstName": "Search Name", "deleteSuccess": "Deleted Successfully", "propertyAssessment": {"incomeDescription": "Income Description", "constructionCompletionCertificate": "Construction Completion Certificate", "privateToilet": "Private Toilet", "toiletType": "Toilet Type", "totleNo": "Total Number", "lightFixture": "Light Fixture", "faucetConnection": "Faucet Connection", "rooftopSolarPowerConnection": "Rooftop Solar Power Project", "rainWaterHarvestingProject": "Rainwater Harvesting Project", "drainageSystem": "Is there a drainage system?", "constructionAreaOfGroundFloor": "Construction Area of Ground Floor", "restGroundFloorfreeSpace": "Remaining Free Space on Ground Floor", "yes": "Yes", "no": "No"}, "api": {"login": "Login successful", "loginfailed": "<PERSON><PERSON> failed. Please check your options and try again", "invalidotp": "Invalid OTP", "fetch": "Data fetched", "error": "Please check your options and try again", "update": "Data updated", "create": "Data created", "delete": "Data deleted successfully", "formupdate": "{{name}} updated successfully", "formcreate": "{{name}} created successfully", "formdelete": "{{name}} deleted successfully", "financialYearGenerated": "Financial year generated", "generating": "Generating...", "generate": "Generate"}, "dashboard": {"usagewiseProperty": "Property by Usage Type", "wardwiseProperty": "Property by Ward"}, "validations": {"email": {"emailRequiredError": "Email is required", "emailInvalidError": "Invalid email"}, "password": {"passwordLengthError": "Password must be at least 5 characters long", "passwordLowercaseError": "Password must contain at least one lowercase letter", "passwordUppercaseError": "Password must contain at least one uppercase letter", "passwordNumberError": "Password must contain at least one number", "passwordSpecialCharError": "Password must contain at least one special character"}, "otp": "Invalid OTP"}, "addRole": "Add Role", "searchrole": "Search Role", "alreadyexist": "Already exists", "roleName": "Role Name", "permissionAccess": "Permission Access", "action": "Action", "createRole": "Add Role", "enterRoleName": "Enter Role Name", "roleDeletedSuccessfully": "Role deleted successfully", "failedToDeleteRole": "Failed to delete role", "invalidRoleName": "Invalid role name or role already exists", "selectRole": "Select Role", "administrative": "Administrative", "nonAdministrative": "Non-Administrative", "createRoleTitle": "Create Role:", "roleNamePlaceholder": "Role Name", "permissionsSavedSuccess": "Permissions saved successfully", "permissionsSavedFail": "Failed to save permissions", "saveRole": "Save Role", "permission": "Permission", "view": "View", "add": "Save", "create": "Create", "update": "Update", "delete": "Delete", "save": "Save", "cancel": "Cancel", "noResultsFound": "No records found!", "readyreckonerrate": {"fromDate": "From Date", "toDate": "To Date", "pickaDate": "Pick a Date", "surveyNumber": "Survey Number", "readyreckoner": "Market Value Rate", "valueRate": "Value Rate", "readyreckonerrateTitle": "Market Value Master", "searchPlaceholder": "Search Market Value", "searchLocation": "Search Market Value Type", "AddBtn": "Add Market Value", "edit": "Edit", "delete": "Delete", "formTitle": "Market Value Information", "updateBtn": "Update Market Value", "valueLabel": "Value", "valuePlaceholder": "Enter Value"}, "propertytaxrate": {"fromDate": "From Date", "toDate": "To Date", "pickaDate": "Pick a Date", "surveyNumber": "Survey Number", "propertyTaxRate": "Property Tax Rate", "valueRate": "Value Rate", "propertyTaxRateTitle": "Property Tax Rate Master", "searchPlaceholder": "Search Property Tax", "searchLocation": "Search Property Tax Type", "AddBtn": "Add Property Tax Rate", "edit": "Edit", "delete": "Delete", "formTitle": "Property Tax Rate Information", "updateBtn": "Update Property Tax Rate", "valueLabel": "Value", "valuePlaceholder": "Enter Value", "taxMin": "Property Tax (Minimum)", "taxMax": "Property Tax (Maximum)", "municipalTax": "Tax determined by the Municipal Council"}, "deletescreen": {"title": "Are you sure you want to permanently delete this record?", "description": "", "yes": "Yes", "no": "No"}, "table": {"edit": "Edit", "delete": "Delete", "view": "View Property", "printReport": "Print Report", "back": "Back"}, "paymentform": {"title": "Know Your Dues", "taxpayerNumberLabel": "Taxpayer Number", "billNo": "<PERSON>", "taxpayerFullNameLabel": "Taxpayer Full Name", "taxDetailsHeader": "Tax Details (₹)", "amountHeader": "Amount (₹)", "propertyTax": "Property Tax (House Tax)", "streetLightTax": "Street Light Tax", "healthTax": "Health Tax", "drainageTax": "Drainage Tax", "treeTax": "Tree Tax", "educationTax": "Education Tax", "employmentGuaranteeTax": "Employment Guarantee Tax", "fireTax": "Fire Tax", "waterTax": "Water Tax", "solidWasteTax": "Solid Waste Tax", "penaltyTax": "Penalty Tax", "penalty": "Penalty", "interest": "Interest", "advertisementTax": "Advertisement Tax", "warrantFee": "<PERSON><PERSON>", "other": "Other", "totalPayment": "Total Payment", "printButton": "Print", "messagePlaceholder": "Send a message to the customer", "sendMessageButton": "Send Message", "choosePaymentMethod": "Choose Payment Method", "card": "Card", "userinfo": "", "cardNumber": "Card Number", "cardholderName": "Cardholder Name", "expiryDate": "Expiry Date", "cvv": "CVV", "upi": "UPI", "enterUpiId": "Enter UPI ID", "paypal": "PayPal", "enterPaypalEmail": "Enter PayPal Email", "netbanking": "Net Banking", "bankName": "Bank Name", "accountHolderName": "Account Holder Name", "accountNumber": "Account Number", "ifscCode": "IFSC Code", "proceedToPayButton": "Proceed to Pay", "razorpay": "Razorpay", "razorpayDescription": "You will be redirected to Razorpay to complete your payment."}, "DashboardTable": {"name": "Name", "targetReached": "Target Reached", "taxCollected": "Tax Collected", "amount": "Amount", "pendingDays": "Pending Days"}, "taxDemand": {"taxDemand": "Tax Generation", "selectRange": "Select Year", "year": "Year", "progress": "Progress", "propertyCount": "Property Count", "view": "View Property", "publish": "Publish", "unPublish": "Unpublish", "generateBill": "Generate Bill", "warshiKCarCount": "Annual Tax Count", "billsGenerated": "Bills Generated", "recordGeneratedForProperty": "Please go to Annual Tax Demand (9) and generate the annual tax demand for this property"}, "publish": "Are you sure you want to publish this?", "unpublish": "Are you sure you want to unpublish this?", "selectAnyFieldNote": "Note: Select any one field.", "allFieldAreRequire": "Note: All fields are required", "selectAnyTwoFieldNote": "Note: Select any two fields.", "recordGenerated": "Record generated successfully", "tryAgainLater": "Please try again later", "contactAdministrator": "Please contact the administrator.", "Generating": "Generating", "namunaEight": {"milkatKarAkarni": "Property Tax Assessment (8)", "namunaEightCheckboxOneValue": "Imposing less than 30% tax on new tax assessment compared to old tax assessment", "namunaEightCheckboxTwoValue": "50% tax", "namunaEightCheckboxThreeValue": "If old tax is to be taken", "milkatDharkachiMahiti": "Property Owner Information", "IncomeNumber": "Property Number", "oldIncomeNumber": "Old Property Number", "incomeType": "Property Type", "surveyNumber": "Survey Number", "streetName": "Street Name", "waterType": "Water Type", "incomeHolderName": "Property Owner Name", "occupancyHolderName": "Occupancy Holder Name", "tableHeading": "Income Description", "tableIncomeNumber": "Income No.", "tableIncomeType": "Income Type", "tableLengthHeading": "Length", "tableWidthHeading": "<PERSON><PERSON><PERSON>", "tableAreaHeading": "Area", "tableTaxRateHeading": "Tax Rate", "tableDepreciationRateHeading": "Depreciation Rate", "tableWeightingHeading": "Weighting", "tableTotalTaxHeading": "Total Tax (₹)", "tableIncomeOtherInfoHeading": "Income Other Information", "buildingTax": "Building Tax", "lightTax": "Light Tax", "healthTax": "Health Tax", "newTaxationTotal": "Total as per New Tax Assessment", "commonWatershed": "Common Water Tax", "capitalValue": "Capital Value", "reducedAmount30%": "Amount reduced by 30% (-)", "solidWasteCharges": "Solid Waste Charges", "penaltyCharges": "Penalty Charges", "totalTax": "Total Tax", "newTaxRate": "New Tax Rate", "incomeNote": "Note on Income", "occupancyHolder": "Occupancy Holder", "propertyNumber": "Property Number", "propertyDescription": "Property Description", "taxesOnBuildings": "Taxes on Buildings (₹)", "pastDue": "Past Due", "currentTax": "Current Tax", "total": "Total", "totalAmount": "Total Amount", "penaltyAmount": "Penalty Amount", "taxDemand": "Tax Demand"}, "userGuide": {"title": "Application User Guide", "description": "Learn how to use the Shirol Municipal Council application", "tabs": {"general": "General", "property": "Property Management", "tax": "Tax Payment", "admin": "Administration"}, "general": {"title": "Getting Started", "item1": "Use the search function to find properties by property number or owner's name", "item2": "Navigate through the application using the sidebar menu", "item3": "Adjust the font size using the A+/A- buttons in the navbar", "item4": "Contact support through the Contact Us page"}, "property": {"title": "Property Management", "item1": "Register new properties using the Property Registration form", "item2": "Search for existing properties using property number or owner details", "item3": "View and update property details including ownership information", "item4": "Process property transfer through the Property Transfer section"}, "tax": {"title": "Tax Management", "item1": "Generate property taxes using the Tax Generation feature", "item2": "View tax calculation details in the Sample 8 form", "item3": "Process annual tax demands through the Sample 9 form", "item4": "Pay taxes online through the Payment section", "item5": "View payment history and receipts"}, "admin": {"title": "Administration", "item1": "Manage users through the User Registration section", "item2": "Create and manage roles with specific permissions", "item3": "Configure master data such as zones, wards, and usage types", "item4": "View dashboard reports and statistics"}, "sidebar": {"title": "Sidebar Menu User Guide", "description": "Information on how to use the various options in the sidebar menu", "step1": "Dashboard: View general information and reports.", "step2": "Determination Department: Create and print determinations.", "step3": "Property Registration: Register new properties.", "step4": "Property Tax Assessment: Create property tax assessments.", "step5": "Annual Tax Demand: Process annual tax demands.", "step6": "New Registration: Register new users.", "step7": "Property Transfer: Process property transfers.", "step8": "Property Forms: Fill out property-related forms.", "step9": "Tax Payment: Pay taxes online.", "step10": "Settings: Adjust application settings."}}, "namunaNine": {"milkatKarAkarni": "Annual Tax Demand (9)"}, "total": "Total", "propertyView": {"basicInformation": "Basic Information", "SrNo": "Sr. No.", "ward": "Ward", "zone": "Zone", "street": "Street", "plotArea": "Plot Area", "GisNumber": "GIS Number", "ghanKachara": "Solid Waste", "plotConstructionArea": "Plot Construction Area", "plotEmptyArea": "Plot Empty Area", "ownerDetails": "Property Owner Information", "propertyDetails": "Property Details", "taxDetails": "Tax Details", "documents": "Documents", "citySurveyNumber": "City Survey Number", "houseName": "House Name", "latitude": "Latitude", "longitude": "Longitude", "namunaEight": "<PERSON><PERSON> (8)", "namunaNine": "<PERSON><PERSON> (9)", "waste_tax": "Solid Waste", "ownerDetail": "Owner <PERSON><PERSON>"}, "ownerDetails": {"SrNo": "Sr. No.", "name": "Name", "type": "Type", "mobileNumber": "Mobile Number", "emailId": "Email ID", "adharNumber": "<PERSON><PERSON><PERSON> Number"}, "propertyDetail": {"SrNo": "Sr. No.", "propertyType": "Property Type", "constructionDate": "Construction Date", "usageType": "Usage Type", "usageSubType": "Solid Waste Compaction", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "areaSqFt": "Area (Sq. Ft.)", "areaSqMt": "Area (Sq. Mt.)", "propertyStatus": "Property Status"}, "taxDetails": {"date": "Date", "taxYear": "Tax Year", "amount": "Amount", "status": "Status"}, "documents": {"SrNo": "Sr. No.", "documentName": "Name", "documentType": "Type", "action": "Action"}, "taxView": {"back": "Back", "billNo": "<PERSON>", "propertyNo": "Property Number", "ownerName": "Property Owner", "zone": "Zone", "street": "Street", "totalAmount": "Total (₹)"}, "malmattaFerfar": {"SrNo": "Sr. No.", "propertyNumber": "Property Number", "propertyOldNumber": "Old Property Number", "propertyOwner": "Property Owner", "occupancyHolder": "Occupancy Holder", "mobileNumber": "Mobile Number", "wardName": "Ward", "zone": "Zone", "usageType": "Usage Type", "propertyType": "Property Type", "addPropertyOwner": "Add Property Owner"}, "Value": "Rate", "Status": "Status", "setting": {"constructionRateTitle": "Ready Reckoner Construction Rate", "RR-Rate": "Ready Reckoner Rate", "TaxRate": "Tax Rate", "WeightingRate": "Weighting Rate", "solidWasteRate": "Solid Waste Rate", "depreciationRate": "Depreciation Rate", "bookMaster": "Book Master", "value": "Rate", "status": "Status", "financialYear": "Financial Year", "startDate": "Start Date", "endDate": "End Date", "startYear": "Start Year", "endYear": "End Year", "bookNumber": "Book Number", "reAssessment": "Reassessment Year", "reassessmentRange": "Reassessment Range", "selectReassessment": "Select Reassessment Range", "fromAge": "From Age", "toAge": "To Age", "fromAgePlaceholder": "Enter From Age", "toAgePlaceholder": "Enter To Age"}, "bookNumber": "Enter Book Number", "editCollector": "Edit Collector", "updateCollector": "Update", "reportModule": {"selectFinancialYear": "Please select Financial Year.", "processing": "Processing...", "download": "Download", "financial": "Financial", "downloadReportInExcel": "Download Report in Excel", "financialYear": "Financial Year", "selectYear": "Select Year", "milkatkarReportTitle": "Property Tax Report", "milkatkarReportDescription": "Detailed report of property tax (Property Tax) for the selected financial year.", "varshikkarReportTitle": "Annual Tax Report", "varshikkarReportDescription": "Comprehensive annual report for the selected financial year.", "paidUserReportTitle": "Tax Paid Users Report", "paidUserReportDescription": "Report of users who paid tax for the selected financial year.", "notPaidUserReportTitle": "Tax Not Paid Users Report", "notPaidUserReportDescription": "Report of users who did not pay tax for the selected financial year.", "propertyReportTitle": "Property Report", "propertyReportDescription": "Detailed report of properties for the selected financial year.", "milkatkarReportDownloaded": "Property Tax Report downloaded successfully.", "milkatkarReportFailed": "Failed to download Property Tax Report. Please try again.", "varshikkarReportDownloaded": "Annual Tax Report downloaded successfully.", "varshikkarReportFailed": "Failed to download Annual Tax Report. Please try again.", "paidUserReportDownloaded": "Tax Paid Users Report downloaded successfully.", "paidUserReportFailed": "Failed to download Tax Paid Users Report. Please try again.", "notPaidUserReportDownloaded": "Tax Not Paid Users Report downloaded successfully.", "notPaidUserReportFailed": "Failed to download Tax Not Paid Users Report. Please try again.", "propertyReportDownloaded": "Property Report downloaded successfully.", "propertyReportFailed": "Failed to download Property Report. Please try again."}, "newUser": "Save new user", "propertyModification": "Property modification", "selectStartYear": "Select Start Year", "selectEndYear": "Select End Year"}