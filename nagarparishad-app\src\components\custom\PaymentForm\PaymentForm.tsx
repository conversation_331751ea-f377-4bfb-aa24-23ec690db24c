import React, { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import WhiteContainer from "../WhiteContainer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormField,
} from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import google from "@/assets/img/payment/google.png";
import visa from "@/assets/img/payment/visa.png";
import paypal from "@/assets/img/payment/pay-pa.png";
import netbanking from "@/assets/img/payment/netbanking.svg";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import BreadCrumb from "../BreadCrumb";
import axios from "axios";
import razorpayLogo from "@/assets/img/payment/netbanking.png"; // Using netbanking.png as a placeholder for Razorpay logo
import { ArrowLeft, ChevronLeft } from "lucide-react";

const cardSchema = z.object({
  cardNumber: z
    .string()
    .refine((val) => /^\d{4} \d{4} \d{4} \d{4}$/.test(val), {
      message: "Invalid card number format",
    }),
  cardholderName: z.string().optional(),
  expiryDate: z.string().refine((val) => /^\d{2}\/\d{2}$/.test(val), {
    message: "Invalid expiry date format (MM/YY)",
  }),
  cvv: z.string().refine((val) => /^\d{3}$/.test(val), {
    message: "Invalid CVV format (3 digits)",
  }),
});

const upiSchema = z.object({
  upiId: z.string(),
});

const paypalSchema = z.object({
  paypalEmail: z.string().email(),
});

const netbankingSchema = z.object({
  bankName: z.string(),
  accountHolderName: z.string(),
  accountNumber: z.string().refine((val) => /^\d{9,18}$/.test(val), {
    message: "Invalid account number format (9-18 digits)",
  }),
  ifscCode: z.string().refine((val) => /^[A-Z]{4}\d{7}$/.test(val), {
    message: "Invalid IFSC code format (4 letters followed by 7 digits)",
  }),
});

const getPaymentSchema = (method: any) => {
  switch (method) {
    case "card":
      return cardSchema;
    case "upi":
      return upiSchema;
    case "paypal":
      return paypalSchema;
    case "netbanking":
      return netbankingSchema;
    case "razorpay":
      return z.object({}); // No specific fields for Razorpay as it uses its own modal
    default:
      return z.object({});
  }
};
getPaymentSchema;

// Function to load Razorpay script
const loadRazorpayScript = () => {
  return new Promise((resolve) => {
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });
};

const PaymentForm = () => {
  const { t } = useTranslation();
            const baseURL = import.meta.env.VITE_APP_BASE_URL;

  const location = useLocation();
  const navigate = useNavigate();

  const userData = location.state?.rowData || [];
  const taxDetails = location.state?.taxDetails || [];
  console.log("taxDetails", taxDetails);
  const taxTypes = location.state?.taxDetails?.tax_types || {};
  console.log("taxTypes", taxTypes);
  // console.log("taxDetails", taxDetails)
  // Extract only the keys that start with "tax_type" to create dynamic rows
  const calculateTotal = (taxDetails) => {
    if (!taxDetails || !taxDetails[0]) return 0;
    return taxDetails[0].total_tax_current || 0;
  };

  // Function to format tax amount
  const formatAmount = (amount) => {
    return amount ? Number(amount).toFixed(2) : "0.00";
  };
  const form = useForm({
    resolver: zodResolver(z.object({})), // Empty schema as no form fields for payment method
    defaultValues: {
      cardNumber: "",
      cardholderName: "",
      expiryDate: "",
      cvv: "",
      upiId: "",
      paypalEmail: "",
      bankName: "",
      accountHolderName: "",
      accountNumber: "",
      ifscCode: "",
    },
  });

  const { handleSubmit, setValue, trigger, reset } = form;

  useEffect(() => {
    // No reset needed as there are no payment method specific fields
  }, []);

  const handleRazorpayPayment = async () => {
    const totalAmount = taxDetails?.warshikKar?.[0]?.total_tax || 0;
    const propertyId = userData.property_id;
    const propertyNumber = userData.propertyNumber;
    const financialYear = taxDetails?.financial_year;
    const transactionPercentage = 2; // Example: 2% transaction fee

    if (totalAmount === 0) {
      toast({
        title: "Payment Error",
        description: "Cannot proceed with 0 amount.",
        variant: "destructive",
      });
      return;
    }

    try {
      const url = `${baseURL}/v1/razorpay/order`;

      const orderResponse = await axios.post(url, {
        amount: totalAmount,
        currency: "INR",
        propertyId: propertyId,
        propertyNumber: propertyNumber,
        financialYear: financialYear,
        transactionPercentage: transactionPercentage,
      });

      const { id: order_id, amount, currency } = orderResponse.data;

      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        toast({
          title: "Payment Error",
          description: "Razorpay SDK failed to load. Please try again.",
          variant: "destructive",
        });
        return;
      }

      const options = {
        key: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: amount,
        currency: currency,
        name: "Shirol Nagarparishad",
        description: "Property Tax Payment",
        order_id: order_id,
        handler: async function (response: any) {
          try {
            const verifyResponse = await axios.post(
              `${baseURL}/v1/razorpay/verify`,
              {
                paymentId: response.razorpay_payment_id,
                orderId: response.razorpay_order_id,
                signature: response.razorpay_signature,
                propertyId: propertyId,
                propertyNumber: propertyNumber,
                financialYear: financialYear,
                taxDetails: taxDetails,
              }
            );

            if (verifyResponse.status === 200) {
              toast({
                title: "Payment Successful",
                description: "Your payment has been successfully processed.",
              });
              // Optionally, navigate to a success page or update UI
            } else {
              toast({
                title: "Payment Failed",
                description: "Payment verification failed.",
                variant: "destructive",
              });
            }
          } catch (error) {
            console.error("Error verifying payment:", error);
            toast({
              title: "Payment Error",
              description: "An error occurred during payment verification.",
              variant: "destructive",
            });
          }
        },
        prefill: {
          name: userData.name || "",
          email: "",
          contact: "",
        },
        notes: {
          address: userData.propertyNumber || "",
        },
        theme: {
          color: "#3399FF",
        },
      };

      const rzp1 = new (window as any).Razorpay(options);
      rzp1.on("payment.failed", function (response: any) {
        toast({
          title: "Payment Failed",
          description: `Error: ${response.error.code} - ${response.error.description}`,
          variant: "destructive",
        });
        console.error("Razorpay payment failed:", response.error);
      });
      rzp1.open();
    } catch (error) {
      console.error("Error initiating Razorpay payment:", error);
      toast({
        title: "Payment Error",
        description: "Failed to initiate Razorpay payment.",
        variant: "destructive",
      });
    }
  };

  const [loading, setLoading] = useState(false);

  const handlePrintButtonClick = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        "https://api-spms.onpointsoft.com/api/v1/billing/get-bill",
        {
          responseType: "blob",
        }
      );

      const pdfBlob = new Blob([response.data], { type: "application/pdf" });

      const pdfUrl = URL.createObjectURL(pdfBlob);
      setLoading(false);

      window.open(pdfUrl);
    } catch (error) {
      setLoading(false);

      console.error("Error fetching the PDF:", error);
    }
  };

  return (
    <>
 


      <div className="flex items-center justify-center bg-Secondary">
             {/* <BreadCrumb  className={"bg-Secondary"} /> */}

        <WhiteContainer className="flex  w-[80%] max-md:flex-col-reverse ">
          <div className="w-1/2 px-2 pr-4 max-md:w-full">
           <button
    onClick={() => navigate(-1)}
    className="flex items-center text-sm font-medium text-blue-500 hover:text-blue-600"
  >
    <ChevronLeft  className="mr-2 h-10 w-10" />
  </button>
            {/* Payment Details */}
            <div className="flex flex-col space-y-4">
              <h2 className="text-xl font-semibold  text-center  pt-1 max-md:mt-2">
                {" "}
                {t("propertyLocationFormWizardStepTwoTitle")}
              </h2>
              <div>
                <label className="block text-black mb-2">
                  {t("paymentform.taxpayerNumberLabel")}
                </label>
                <Input
                  value={userData.propertyNumber || "54841845848448"}
                  className="bg-zinc-100"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-black mb-2">
                  {t("paymentform.taxpayerFullNameLabel")}
                </label>
                <Input
                  value={`${userData.name} `}
                  className="bg-zinc-100"
                  readOnly
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col w-1/2 px-2 max-md:w-full">
            <h2 className="text-center text-xl font-semibold mb-6 pt-1">
              {t("paymentform.title")}
            </h2>
            <div className="">
              <Table className="min-w-full border-collapse border border-zinc-400 dark:border-zinc-600">
                <TableHeader>
                  <TableRow className="bg-zinc-200 dark:bg-zinc-700">
                    <TableHead className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                      {t("paymentform.taxDetailsHeader")}
                    </TableHead>
                    <TableHead className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                      {t("paymentform.amountHeader")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Dynamic Tax Rows */}
                  <TableRow key={"tax-value-0"}>
                    <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                      संकलित कर (घरपट्टी)
                    </TableCell>
                    <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                            {taxDetails?.warshikKar?.[0]?.all_property_tax_sum_total !== undefined ? formatAmount(taxDetails.warshikKar[0].all_property_tax_sum_total) : 'N/A'}

                    </TableCell>
                  </TableRow>
                  {Object.entries(taxTypes || {}).map(([key, label]) => {
                    const taxValue = taxDetails.warshikKar[0][key];
                    if (taxValue !== null && taxValue !== undefined) {
                      return (
                        <TableRow key={key}>
                          <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                            {label}
                          </TableCell>
                          <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                            {formatAmount(taxValue)}
                          </TableCell>
                        </TableRow>
                      );
                    }
                    return null;
                  })}

                  {/* Total Row */}
                  <TableRow className="bg-zinc-200 dark:bg-zinc-700">
                    <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2 font-bold">
                      {t("paymentform.totalPayment")}
                    </TableCell>
                    <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2 font-bold">
                      {taxDetails?.warshikKar?.[0]?.total_tax !== undefined
                        ? formatAmount(taxDetails.warshikKar[0].total_tax)
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
            {/* <div className="w-full h-full flex justify-end mt-4 max-md:mb-3 max-md:mt-2">
              <Button
                variant="submit"
                className="hover:bg-blue-700"
                onClick={handleRazorpayPayment}
              >
                {t("paymentform.proceedToPayButton")}
              </Button>
            </div> */}
          </div>
        </WhiteContainer>
      </div>
    </>
  );
};

export default PaymentForm;
