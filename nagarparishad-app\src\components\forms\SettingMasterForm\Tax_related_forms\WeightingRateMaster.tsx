import React, { useState, useContext, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { GlobalContext } from "@/context/GlobalContext";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { useConstructionRateController } from "@/controller/tax/ConstructionRateController";
import { ConstructionRateSetting } from "@/model/tax/constructionRate";
import { toast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import AsyncSelect from "@/components/ui/react-select";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ReactselectInterface } from "@/model/global-master";
import { PropertyClassMasterListApiOject } from "@/model/PropertyClassMaster";
import { z } from "zod";
import { useUsageMasterController } from "@/controller/master/UsageMasterController";
import { useWeightingRateController } from "@/controller/tax/WeightingRateController";
import {
  WeightingRateSetting,
  WeightingRateUpdateApi,
} from "@/model/tax/weightingRate";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { propertyUsageTypeFilterFn } from "@/components/globalcomponent/TanstackFilter";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ResponseData } from "@/model/auth/authServices";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Api from "@/services/ApiServices";
import PropertyApi from "@/services/PropertyServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
interface weightingRateInterface {
  editData?: WeightingRateUpdateApi;
}
const WeightingRateMaster = ({ editData }: weightingRateInterface) => {
  const { t } = useTranslation();
  const {
    weightingRateList,
    createWeightingRate,
    updateWeightingRate,
    deleteWeightingRate,
    propertyLoading,
  } = useWeightingRateController();
  const constructionRateLists = weightingRateList?.data || []; // Access the data array
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const userRef = useRef(null);
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
  const schema = z.object({
    financial_year: z.string().optional(), // Optional for backward compatibility
    reassessment_range_id: z.string().trim().min(1, t("errorsRequiredField")),
    value: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
    status: z.string().trim().min(1, t("errorsRequiredField")),
    usage_type_id: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const [formData, setFormData] = useState({
    financial_year: "",
    reassessment_range_id: "",
    value: null,
    status: "Active",
    usage_type_id: "",
  });

  const usageList: any = useUsageMasterController();
  const usageOptions: ReactselectInterface[] = usageList.usageList?.map(
    (usagetype: any) => ({
      value: usagetype.usage_type_id,
      label: usagetype.usage_type,
    })
  );
  const dynamicValues = {
    name: t("setting.WeightingRate"),
  };
  // const { setMasterComponent, setOpen } = useContext(GlobalContext);

  const [reassessmentRanges, setReassessmentRanges] = useState([]);
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Setting, FormName.WeightingRate,Action.CanRead );
  const canUpdate = canPerformAction(ModuleName.Setting, FormName.WeightingRate,Action.CanUpdate );
  const CanCreate = canPerformAction(ModuleName.Setting, FormName.WeightingRate,Action.CanCreate );
  const CanDelete = canPerformAction(ModuleName.Setting, FormName.WeightingRate,Action.CanDelete );

  const Form = FormProvider;

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      financial_year: editData?.financial_year || "",
      reassessment_range_id: editData?.reassessment_range_id || "",
      value: editData?.value || null,
      status: editData?.status || "Active",
      usage_type_id: editData?.usage_type_id || "",
    },
  });
  const {
    formState: { errors },
    reset,
    control,
  } = form;



  const handleSelectChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
  };



  const checkDuplicate = (reassessmentRangeId: string, usageId: string) => {
    return constructionRateLists.some(
      (item) =>
        item.reassessmentRange?.reassessment_range_id === reassessmentRangeId &&
        item.usage_type.usage_type_id === usageId &&
        (!isEditing || item.weighting_rate_id !== editingId)
    );
  };

  const handleEdit = (item: WeightingRateSetting) => {
    setOpen(true);
    setIsEditing(true);
    setEditingId(item.weighting_rate_id);

    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      financial_year: item.financial_year,
      reassessment_range_id: item.reassessmentRange?.reassessment_range_id || "",
      value: item.value,
      status: item.status,
      usage_type_id: item.usage_type.usage_type_id,
    });
  };

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<WeightingRateSetting | null>(
    null
  );

  function handleDelete(item: WeightingRateSetting): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = async () => {
    if (selectedItem) {
      try {
        await deleteWeightingRate(selectedItem.weighting_rate_id);
        toast({
          title: t("api.formdelete", dynamicValues),
          variant: "success",
        });
      } catch (error) {
        toast({
          title: error.message,
          variant: "destructive",
        });
      }
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleConfirmDeletes = () => {
    if (selectedItem) {
      deleteWeightingRate(selectedItem.weighting_rate_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };



  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();


    if (checkDuplicate(data.reassessment_range_id, data.usage_type_id)) {
      toast({
        title: t(
          "या पुनर्मूल्यांकन श्रेणीसाठी आणि मालमत्ता वापर प्रकार साठी दर आधीच अस्तित्वात आहे"
        ),
        variant: "destructive",
      });
      return;
    }

    if (isEditing) {
      updateWeightingRate(
        {
          weightingRateId: editingId,
          weightingRateData: data,
        },
        {
          onSuccess: (response) => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            reset({
              financial_year: "",
              reassessment_range_id: "",
              value: '',
              status: "Active",
              usage_type_id: "",
            });
            setIsEditing(false);
            setEditingId(null);
          },
          onError: (error) => {
            console.log("error", error);
            toast({
              title: "error",
              variant: "destructive",
            });
          },
        }
      );
    } else {
      console.log("in creationprocess");
      createWeightingRate(data, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          reset({
            financial_year: "",
            reassessment_range_id: "",
            value: '',
            status: "Active",
            usage_type_id: "",
          });
        },
        onError: (error) => {
          console.log("error", error);
          toast({
            title: error.message || t("api.error"),
            variant: "destructive",
          });
        },
      });
    }
  };
  useEffect(() => {
    fetchReassessmentRanges();
  }, []);

  const fetchReassessmentRanges = async () => {
    try {
      console.time("Fetch reassessment ranges");
      PropertyApi.getReassessmentRanges((response) => {
        console.timeEnd("Fetch reassessment ranges");

        if (response.status && response.data.statusCode === 200) {
          const formattedData = response.data.data.map(item => ({
            ...item,
            reassessment_id: item.reassessment_range_id,
            reassessment_range: `${item.start_range} to ${item.end_range}`
          }));
          setReassessmentRanges(formattedData);
        }
      });
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  const columns: ColumnDef<WeightingRateSetting>[] = [
    {
      accessorKey: "reassessmentRange",
      header: t("setting.reassessmentRange"),
      cell: ({ row }) => {
        const startRange = row.original?.reassessmentRange?.start_range;
        const endRange = row.original?.reassessmentRange?.end_range;
        return <div>{startRange && endRange ? `${startRange} to ${endRange}` : row.original?.financial_year}</div>;
      },
    },
    {
      accessorKey: "propertyUsageType",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertyAssessmentDetailsForm.propertyUsageType")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original.usage_type.usage_type}</div>,
      filterFn: propertyUsageTypeFilterFn,
    },
    {
      accessorKey: "status",
      header: t("Status"),
      cell: ({ row }) => <div>{row.original?.status}</div>,
    },
    {
      accessorKey: "value",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("Value")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original?.value}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: "actions",
            header: t("Actions"),
            cell: ({ row }) => (
              <>
                {canUpdate && (
                  <button
                    className="h-8 w-8 p-0"
                    onClick={() => handleEdit(row.original)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {CanDelete && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleDelete(row.original)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  return (
    <div className="w-full h-fit p-6" ref={userRef && userRef}>
      <p className="w-full flex items-center justify-between text-2xl font-semibold mb-2">
        {t("setting.WeightingRate")}
      </p>
      {(canUpdate || CanCreate) && (
      <WhiteContainer>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid md:grid-cols-5 gap-x-3">
              <div>
                {/* <Label>{t("financialYear")}
          <span className="ml-1 text-red-500">*</span>
        </Label> */}
                <FormField
                  control={form.control}
                  name="reassessment_range_id"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("setting.reassessmentRange")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <Select
                        {...field}
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder={t("setting.selectReassessment")} />
                        </SelectTrigger>
                        <SelectContent>
                          {reassessmentRanges.map((range) => (
                            <SelectItem
                              key={range.reassessment_range_id}
                              value={range.reassessment_range_id}
                            >
                              {`${range.start_range} to ${range.end_range}`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.reassessment_range_id && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div>
                {/* <Label>{t("propertyAssessmentDetailsForm.propertyUsageType")}</Label> */}
                <FormField
                  control={form.control}
                  name="usage_type_id"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        {" "}
                        {t("propertyAssessmentDetailsForm.propertyUsageType")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <Controller
                        control={form.control}
                        name="usage_type_id"
                        render={({ field: controllerField }) => (
                          <AsyncSelect
                            value={
                              usageOptions.find(
                                (option: any) =>
                                  option.value === controllerField.value
                              ) || null
                            }
                            placeholder={t(
                              "propertyAssessmentDetailsForm.propertyUsageType"
                            )}
                            className="mt-1 h-[2.5rem]"
                            options={usageOptions}
                            colourOptions={usageOptions}
                            onChange={(selectedOption: any) => {
                              controllerField.onChange(selectedOption.value);
                            }}
                          />
                        )}
                      />
                      {errors.usage_type_id && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div>
                {/* <Label>{t("Status")}</Label> */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel> {t("setting.status")}
                      <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <Select
                        onValueChange={(value) =>
                          handleSelectChange("status", value)
                        }
                        value={formData.status}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder={t("selectStatus")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Active">Active</SelectItem>
                          <SelectItem value="Inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.status && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div>
                {/* <Label>{t("Value")}</Label> */}
                <FormField
                  control={form.control}
                  name="value"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("setting.value")}
                      <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <Input
                        type="number"
                        id="value"
                        name="value"
                        value={field.value}
                        onChange={(event) =>
                          field.onChange(Number(event.target.value))
                        }
                        placeholder={t("setting.value")}
                        className="mt-1 block w-full"
                        required
                        min="0" // Add min attribute to prevent negative values
                        step="0.01"

                      />
                      {errors.value && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid  mb-1 ml-1 max-md:flex max-md:justify-end  pt-[32px] ">
                <Button type="submit">
                  {isEditing ? t("update") : t("add")}
                </Button>
              </div>
            </div>
          </form>
        </Form>
        <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
          {" "}
          {t("allFieldAreRequire")}{" "}
        </p>
      </WhiteContainer>
      )}

      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={constructionRateLists}
          searchKey={"propertyAssessmentDetailsForm.propertySubType"}
          searchColumn={"propertyUsageType"}
          loader={propertyLoading ? true : false}
        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.financial_year}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default WeightingRateMaster;
