import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { InjectRepository } from '@nestjs/typeorm';
import { Financial_yearRepository, PaidDataRepository, PropertyMasterRepository, DemandRecordDataRepository, MilkatKareRepository, MilkatKarTaxeRepository } from 'libs/database/repositories';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';
import { DemandReportData } from 'libs/database/entities/demandeReportData.entity';
import { TAX_TYPES } from 'libs/helpers/src/tax-types.helper';

@Injectable()
export class DemandReportService {
  constructor(
    private readonly propertyRepository: PropertyMasterRepository,
    private readonly annualKarAkaraniService: AnnualKarAkaraniService,
    private readonly paidDataRepository: PaidDataRepository,
    private readonly financialYearRepository: Financial_yearRepository,
    private readonly demandReportDataRepository: DemandRecordDataRepository,
    private readonly milkatKarRepository: MilkatKareRepository,
    private readonly milkatKarTaxRepository: MilkatKarTaxeRepository,
  ) {}

  async exportDemandReportToExcel(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Demand Report');

    const staticColumns = [
      { header: 'Property Number', key: 'property_number', width: 20 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
      { header: 'Status', key: 'status', width: 15 },
    ];

    const taxColumns = Object.entries(TAX_TYPES).flatMap(([taxKey, taxLabel]) => [
      { header: `${taxLabel} मागील`, key: `${taxKey}_prev_remaining`, width: 18 },
      { header: `${taxLabel} चालू`, key: `${taxKey}_curr_remaining`, width: 18 },
      { header: `${taxLabel} total`, key: `${taxKey}_remaining`, width: 18 },
      { header: `${taxLabel} मागील paid`, key: `${taxKey}_prev_paid`, width: 18 },
      { header: `${taxLabel} चालू paid`, key: `${taxKey}_curr_paid`, width: 18 },
      { header: `${taxLabel} paid total`, key: `${taxKey}_paid`, width: 18 },
    ]);

    worksheet.columns = [
      { header: 'Index', key: 'index', width: 8 },
      ...staticColumns,
      { header: 'इमारत मागील Remaining', key: 'all_property_tax_sum_prev_remaining', width: 20 },
      { header: 'इमारत चालू Remaining', key: 'all_property_tax_sum_curr_remaining', width: 20 },
      { header: 'इमारत Remaining total', key: 'all_property_tax_sum_remaining', width: 20 },
      { header: 'इमारत मागील Paid', key: 'all_property_tax_sum_prev_paid', width: 20 },
      { header: 'इमारत चालू Paid', key: 'all_property_tax_sum_curr_paid', width: 20 },
      { header: 'इमारत Paid total', key: 'all_property_tax_sum_paid', width: 20 },
      ...taxColumns,
      { header: 'Total Amount Paid', key: 'total_amount_paid', width: 20 },
      { header: 'Total Amount Remaining', key: 'total_amount_remaining', width: 20 },
      { header: 'Property Type Discount', key: 'property_type_discount', width: 20 },
      { header: 'Other Discount', key: 'other_discount', width: 20 },
      { header: 'Remaining Amount', key: 'remaining_amount', width: 20 },
      { header: 'Book Number', key: 'book_number', width: 15 },
      { header: 'Receipt Number', key: 'book_receipt_number', width: 18 },
    ];

    const columnKeys = worksheet.columns.map(col => col.key);
    const properties = await this.propertyRepository.find();
    let propertyIndex = 1;

    for (const property of properties) {
      const demandRows = await this.demandReportDataRepository.find({
        where: { property: { property_id: property.property_id } },
        order: { createdAt: 'ASC' },
        relations: ['ReciptInfo', 'ReciptInfo.bookNumber']
      });

      let isFirstRow = true;
      for (const row of demandRows) {
        const filteredRow = {};
        columnKeys.forEach(key => {
          if (key === 'index') {
            filteredRow[key] = isFirstRow ? propertyIndex : '';
          } else if (key === 'property_number') {
            filteredRow[key] = isFirstRow ? row.property_number : '';
          } else if (key === 'financial_year') {
            filteredRow[key] = isFirstRow ? row.financial_year : '';
          } else if (key === 'book_number') {
            filteredRow[key] = row.ReciptInfo?.bookNumber?.book_number || row.ReciptInfo?.book_number || '';
          } else if (key === 'book_receipt_number') {
            filteredRow[key] = row.ReciptInfo?.book_receipt_number || '';
          } else {
            filteredRow[key] = row[key];
          }
        });

        worksheet.addRow(filteredRow);
        const lastRow = worksheet.lastRow;
        if (lastRow) {
          lastRow.eachCell(cell => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
          });
        }
        isFirstRow = false;
      }
      if (demandRows.length > 0) propertyIndex++;
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportAllMilkatKarDataToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Milkat Kar Data');

    const taxColumns = Object.entries(TAX_TYPES).flatMap(([taxKey, taxLabel]) => [
      { header: `${taxLabel} चालू`, key: `${taxKey}_current`, width: 18 },
      { header: `${taxLabel} मागील`, key: `${taxKey}_previous`, width: 18 },
      { header: `${taxLabel} total`, key: `${taxKey}`, width: 18 },
    ]);

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Ward', key: 'ward', width: 15 },
      { header: 'Zone Name', key: 'zone_name', width: 15 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
      { header: 'इमारत चालू', key: 'all_property_tax_sum_current', width: 15 },
      { header: 'इमारत मागील', key: 'all_property_tax_sum', width: 15 },
      { header: 'इमारत Total', key: 'all_property_tax_sum_total', width: 15 },
      ...taxColumns,
      { header: 'Total Tax Current', key: 'total_tax_current', width: 15 },
      { header: 'Total Tax Previous', key: 'total_tax_previous', width: 15 },
      { header: 'Total Tax', key: 'total_tax', width: 15 },
      { header: 'Other Tax Sum', key: 'other_tax_sum_tax', width: 15 },
      { header: 'Other Tax Sum Current', key: 'other_tax_sum_tax_current', width: 15 },
      { header: 'Other Tax Sum Previous', key: 'other_tax_sum_tax_previous', width: 15 },
    ];

    const properties = await this.propertyRepository.find({ relations: ['ward', 'zone', 'property_owner_details'] });

    for (const property of properties) {
      let ownerName = '';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      const milkatData = await this.milkatKarRepository.find({
        where: {
          property: { property_id: property.property_id },
          financial_year: financialYear,
        },
        relations: ['milkatKarTax'],
      });

      for (const milkat of milkatData) {
        const rowData: any = {
          propertyNumber: property.propertyNumber,
          old_propertyNumber: property.old_propertyNumber || '',
          owner_name: ownerName,
          ward: property.ward?.ward_name || '',
          zone_name: property.zone?.zoneName || '',
          financial_year: milkat.financial_year || '',
          all_property_tax_sum: milkat.all_property_tax_sum ?? 0,
          all_property_tax_sum_total: milkat.all_property_tax_sum ?? 0,
          all_property_tax_sum_current: 0,
          total_tax: milkat.total_tax ?? 0,
          total_tax_current: 0,
          total_tax_previous: 0,
          other_tax_sum_tax: milkat.other_tax_sum_tax ?? 0,
          other_tax_sum_tax_current: 0,
          other_tax_sum_tax_previous: 0,
        };

        Object.keys(TAX_TYPES).forEach(taxKey => {
          rowData[taxKey] = 0;
          rowData[`${taxKey}_current`] = 0;
          rowData[`${taxKey}_previous`] = 0;
        });

        milkat.milkatKarTax.forEach((taxDetail: any) => {
          if (taxDetail.tax_data) {
            const parsedTaxData = JSON.parse(taxDetail.tax_data);
            Object.keys(TAX_TYPES).forEach(taxKey => {
              if (parsedTaxData[taxKey]) {
                rowData[taxKey] += (parsedTaxData[taxKey].total ?? 0);
                rowData[`${taxKey}_current`] += (parsedTaxData[taxKey].current ?? 0);
                rowData[`${taxKey}_previous`] += (parsedTaxData[taxKey].previous ?? 0);
              }
            });
            rowData.total_tax_current += (parsedTaxData.total_tax_current ?? 0);
            rowData.total_tax_previous += (parsedTaxData.total_tax_previous ?? 0);
            rowData.other_tax_sum_tax_current += (parsedTaxData.other_tax_sum_tax_current ?? 0);
            rowData.other_tax_sum_tax_previous += (parsedTaxData.other_tax_sum_tax_previous ?? 0);
            rowData.all_property_tax_sum_current += (parsedTaxData.all_property_tax_sum_current ?? 0);
          }
        });

        worksheet.addRow(rowData);
      }
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportAllWarshikKarDataToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Warshik Kar Data');

    const taxLabels = Object.values(TAX_TYPES);
    const taxKeys = Object.keys(TAX_TYPES);

    const headerRow1 = [
      'अ.क्र. (Sr No)',
      'मालमत्ता क्रमांक (Property Number)', 'जुना मालमत्ता क्रमांक (Old Property Number)', 'मालकाचे नाव (Owner Name)',
      'भोगवटदाराचे नाव', 'प्रभाग (Ward)', 'क्षेत्र (Zone)', 'मालमत्ता प्रकार (Property Type)', 'मजला (Floor)',
      'बांधकाम वर्ष', 'बांधकाम क्षेत्र (Construction Area)', 'वापर प्रकार (Usage Type)', 'वापर तपशील क्र. (Usage Detail No)',
      'लांबी (Length)', 'रुंदी (Breadth)', 'क्षेत्रफळ (चौ.फूट) (Area Sq Ft)', 'क्षेत्रफळ (चौ.मीटर) (Area Sq Meter)',
      'भां डव ली मूय', 'बां धका म'
    ];

    headerRow1.push("इमारत कर");
    taxLabels.forEach(label => headerRow1.push(label));
    headerRow1.push('एकूण कर (Total Tax)');
    headerRow1.push("इमारत कर", '');
    taxLabels.forEach(label => headerRow1.push(label, ''));
    headerRow1.push('एकूण कर (Total Tax)', '');
    headerRow1.push('बुक नंबर', 'पावती क्र.', 'पावती दिनांक');
    headerRow1.push(`वसूल इमारत कर`, '');
    taxLabels.forEach(label => headerRow1.push(`वसूल ${label}`, ''));
    headerRow1.push('वसूल एकूण कर (Total Tax)', '');
    headerRow1.push(`शिल्लक इमारत कर`, '');
    taxLabels.forEach(label => headerRow1.push(`शिल्लक ${label}`, ''));
    headerRow1.push('शिल्लक एकूण कर (Total Tax)', '');

    worksheet.addRow(headerRow1);

    const headerRow2 = Array(29).fill('');
          headerRow2.push('मागील बाकी', 'चालू बाकी');

    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    
    headerRow2.push('मागील बाकी', 'चालू बाकी', '', '', '');
    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    headerRow2.push('मागील बाकी', 'चालू बाकी');
    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    headerRow2.push('मागील बाकी', 'चालू बाकी');
          headerRow2.push('मागील बाकी', 'चालू बाकी');
          headerRow2.push('मागील बाकी', 'चालू बाकी');

    worksheet.addRow(headerRow2);

    let col = 30;
    const mergeHeaders = (count: number) => {
      for (let i = 0; i < count; i++) {
        worksheet.mergeCells(1, col, 1, col + 1);
        col += 2;
      }
    };
let total_mergerdcell=taxLabels.length+1;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);
    col += 3;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);
    col += 2;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);

    [worksheet.getRow(1), worksheet.getRow(2)].forEach(row => {
      row.eachCell(cell => {
        cell.font = { bold: true };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.border = {
          top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' },
        };
      });
    });


     const applyColorToRange = (startCol: string, endCol: string, color: string) => {
        for (let i = 1; i <= 2; i++) { // Apply to first two rows
            const row = worksheet.getRow(i);
            for (let colLetter = startCol; colLetter <= endCol; colLetter = String.fromCharCode(colLetter.charCodeAt(0) + 1)) {
                const cell = row.getCell(colLetter);
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: color }
                };
            }
        }
    };


    // applyColorToRange('R', 'AC', 'ADD8E6'); // Light blue
    // applyColorToRange('AD', 'BA', '90EE90'); // green 
    // applyColorToRange('AA', 'BZ', 'FFFF00'); // Light Yellow 
    // applyColorToRange('CA', 'CX', 'D3D3D3'); // Gray
    const properties = await this.propertyRepository.find({
      relations: [
        'ward', 'zone', 'property_owner_details', 'property_usage_details','property_owner_details.owner_type',
        'property_usage_details.propertyType', 'property_usage_details.usageType',
        'property_usage_details.floorType',
        'milkatKar', 'milkatKar.milkatKarTax'
      ],
    });

    let srNo = 1;
    for (const property of properties.slice(0,50)) {
       const ownerName = property.property_owner_details?.filter(    (owner) => owner.owner_type.owner_type === "स्वत:" ).map((owner) => owner.name) .join(", ") || "--";
       const otherOwner = property.property_owner_details?.filter(    (owner) => owner.owner_type.owner_type !== "स्वत:" ).map((owner) => owner.name) .join(", ") || "--";

      const warshikData = await this.annualKarAkaraniService.getWarshikKarAkarni(property.propertyNumber, 'propertyNumber', financialYear);
      const warshikKar = (warshikData?.data as any)?.warshikKar?.[0] || {};

      const demandRecords = await this.demandReportDataRepository.find({
        where: { property: { property_id: property.property_id }, financial_year: financialYear },
        relations: ['ReciptInfo', 'ReciptInfo.bookNumber'],
        order: { createdAt: 'ASC' }
      });

      const paid = { total_current: 0, total_previous: 0, total_overall: 0 };
      const remaining = { total_current: 0, total_previous: 0, total_overall: 0 };

        paid[`all_property_tax_sum_curr_paid`] = 0;
        paid[`all_property_tax_sum_prev_paid`] = 0;
        remaining[`all_property_tax_sum_curr_remaining`] = 0;
        remaining[`all_property_tax_sum_prev_remaining`] = 0;

      taxKeys.forEach(key => {
        paid[`${key}_current`] = 0;
        paid[`${key}_previous`] = 0;
        remaining[`${key}_current`] = 0;
        remaining[`${key}_previous`] = 0;
      });

      const bookNumbers = [];
      const receiptNumbers = [];
      const receiptDates = [];
      for (const record of demandRecords) {
       paid[`all_property_tax_sum_curr_paid`]  += (record[`all_property_tax_sum_curr_paid`] || 0);
        paid[`all_property_tax_sum_prev_paid`] += (record[`all_property_tax_sum_prev_paid`] || 0);
        remaining[`all_property_tax_sum_curr_remaining`] += (record[`all_property_tax_sum_curr_remaining`] || 0);
        remaining[`all_property_tax_sum_prev_remaining`] += (record[`all_property_tax_sum_prev_remaining`] || 0);

        taxKeys.forEach(key => {
          paid[`${key}_current`] += (record[`${key}_curr_paid`] || 0);
          paid[`${key}_previous`] += (record[`${key}_prev_paid`] || 0);
          remaining[`${key}_current`] += (record[`${key}_curr_remaining`] || 0);
          remaining[`${key}_previous`] += (record[`${key}_prev_remaining`] || 0);
        });
        paid.total_current += taxKeys.reduce((sum, key) => sum + (record[`${key}_curr_paid`] || 0), 0) + (record[`all_property_tax_sum_curr_paid`] || 0);
        paid.total_previous += taxKeys.reduce((sum, key) => sum + (record[`${key}_prev_paid`] || 0), 0)+(record[`all_property_tax_sum_prev_paid`] || 0);
        paid.total_overall += record.total_amount_paid || 0;
        remaining.total_current += taxKeys.reduce((sum, key) => sum + (record[`${key}_curr_remaining`] || 0), 0)+(record[`all_property_tax_sum_curr_remaining`] || 0);
        remaining.total_previous += taxKeys.reduce((sum, key) => sum + (record[`${key}_prev_remaining`] || 0), 0) +(record[`all_property_tax_sum_prev_remaining`] || 0);
        remaining.total_overall += record.total_amount_remaining || 0;

        if (record.ReciptInfo) {
          const date = new Date(record.ReciptInfo.createdAt).toLocaleDateString();
          bookNumbers.push(record.ReciptInfo.bookNumber?.book_number || '');
          receiptNumbers.push(record.ReciptInfo.book_receipt_number || '');
          receiptDates.push(date);
        }
      }

      const processUsageDetails = (usageDetails) => {
        if (!usageDetails || usageDetails.length === 0) {
          return [{}];
        }
        return usageDetails;
      };

  for (const [index, usage] of processUsageDetails(property.property_usage_details).entries()) {
      const rowData = [];
      const milkatKar = property.milkatKar.find(m => m.financial_year === financialYear);
      const taxData = milkatKar?.milkatKarTax.find(t => t.property_usage_details && t.property_usage_details.property_usage_details_id === usage.property_usage_details_id);
      let capitalValue = '';
      let rrConstructionRate = '';
      if (taxData && taxData.tax_data) {
        try {
          const parsedTaxData: any = JSON.parse(taxData.tax_data as unknown as string);
          capitalValue = parsedTaxData.capital_value || '';
          rrConstructionRate = parsedTaxData.taxCalculationDetails?.rrConstructionRate || '';
        } catch (e) {
          console.error("Error parsing tax_data JSON:", e);
        }
      }

  if (index === 0) {
             rowData.push( 
          srNo++,
          property.propertyNumber,
          property.old_propertyNumber || '',
          ownerName,
          otherOwner,
          property.ward?.ward_name || '',
          property.zone?.zoneName || '',
          usage.propertyType?.propertyType || '',
          usage.floorType?.floor_name || '',
          usage?.construction_start_year || '',
          '',
          usage.usageType?.usage_type || '',
          usage.property_usage_details_id || '',
          usage.length || '',
          usage.width || '',
          usage.are_sq_ft || '',
          usage.are_sq_meter || '',
          capitalValue,
          rrConstructionRate
         );
  }else{
           rowData.push( '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', capitalValue, rrConstructionRate);

        }

        rowData.push(warshikKar[`all_property_tax_sum_total`] || 0);
        taxKeys.forEach(key => rowData.push(warshikKar[`${key}`] || 0));
        rowData.push(warshikKar['total_tax'] || 0);
        rowData.push(warshikKar[`all_property_tax_sum`] || 0);
        rowData.push(warshikKar[`all_property_tax_sum_current`] || 0);

        taxKeys.forEach(key => {
          rowData.push(warshikKar[`${key}_previous`] || 0);
          rowData.push(warshikKar[`${key}_current`] || 0);
        });

        rowData.push(warshikKar['total_tax_previous'] || 0);
        rowData.push(warshikKar['total_tax_current'] || 0);
        rowData.push(bookNumbers.join(', '), receiptNumbers.join(', '), receiptDates.join(', '));

        rowData.push(paid[`all_property_tax_sum_prev_paid`] || 0);
        rowData.push(paid[`all_property_tax_sum_curr_paid`] || 0);

        taxKeys.forEach(key => {
          rowData.push(paid[`${key}_previous`] || 0);
          rowData.push(paid[`${key}_current`] || 0);
        });

        rowData.push(paid.total_previous || 0);
        rowData.push(paid.total_current || 0);

          const prevRemaining = (warshikKar[`all_property_tax_sum`] || 0) - (paid[`all_property_tax_sum_prev_paid`] || 0);
          const currRemaining = (warshikKar[`all_property_tax_sum_current`] || 0) - (paid[`all_property_tax_sum_curr_paid`] || 0);
          rowData.push(prevRemaining > 0 ? prevRemaining : 0);
          rowData.push(currRemaining > 0 ? currRemaining : 0);

        taxKeys.forEach(key => {
          const prevRemaining = (warshikKar[`${key}_previous`] || 0) - (paid[`${key}_previous`] || 0);
          const currRemaining = (warshikKar[`${key}_current`] || 0) - (paid[`${key}_current`] || 0);
          rowData.push(prevRemaining > 0 ? prevRemaining : 0);
          rowData.push(currRemaining > 0 ? currRemaining : 0);
        });

        const totalPrevRemaining = (warshikKar['total_tax_previous'] || 0) - (paid.total_previous || 0);
        const totalCurrRemaining = (warshikKar['total_tax_current'] || 0) - (paid.total_current || 0);
        rowData.push(totalPrevRemaining > 0 ? totalPrevRemaining : 0);
        rowData.push(totalCurrRemaining > 0 ? totalCurrRemaining : 0);

        const row = worksheet.addRow(rowData);
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' },
          };
        });
      }
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportPaidUserReportToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Paid Users');

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
      { header: 'Total Paid Amount', key: 'total_paid_amount', width: 20 },
      { header: 'Payment Date', key: 'payment_date', width: 18 },
    ];

    const paidProperties = await this.paidDataRepository.find({
      where: { financial_year: financialYear },
      relations: ['property', 'property.property_owner_details'],
    });

    for (const paidData of paidProperties) {
      let ownerName = '';
      if (paidData.property?.property_owner_details && paidData.property.property_owner_details.length > 0) {
        const mainOwner = paidData.property.property_owner_details.find(o => o.is_payer) || paidData.property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        propertyNumber: paidData.property?.propertyNumber || '',
        owner_name: ownerName,
        financial_year: paidData.financial_year,
        total_paid_amount: paidData.total_amount,
        payment_date: paidData.createdAt ? new Date(paidData.createdAt).toLocaleDateString() : '',
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportNotPaidUserReportToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Not Paid Users');

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Ward', key: 'ward', width: 15 },
      { header: 'Zone Name', key: 'zone_name', width: 15 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
    ];

    const allProperties = await this.propertyRepository.find({ relations: ['property_owner_details', 'ward', 'zone'] });
    const paidPropertyNumbers = (await this.paidDataRepository.find({
      where: { financial_year: financialYear },
      select: ['property'],
      relations: ['property'],
    })).map(paidData => paidData.property?.propertyNumber);

    const notPaidProperties = allProperties.filter(property => !paidPropertyNumbers.includes(property.propertyNumber));

    for (const property of notPaidProperties) {
      let ownerName = '';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber || '',
        owner_name: ownerName,
        ward: property.ward?.ward_name || '',
        zone_name: property.zone?.zoneName || '',
        financial_year: financialYear,
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportPropertyReportToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Property Report');

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Ward', key: 'ward', width: 15 },
      { header: 'Zone Name', key: 'zone_name', width: 15 },
      { header: 'Property Type', key: 'property_type', width: 15 },
      { header: 'Usage Type', key: 'usage_type', width: 15 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
    ];

    const properties = await this.propertyRepository.find({
      relations: ['property_owner_details', 'ward', 'zone', 'property_usage_details', 'property_usage_details.propertyType', 'property_usage_details.usageType'],
    });

    for (const property of properties) {
      let ownerName = '';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber || '',
        owner_name: ownerName,
        ward: property.ward?.ward_name || '',
        zone_name: property.zone?.zoneName || '',
        property_type: property.property_usage_details?.[0]?.propertyType?.propertyType || '',
        usage_type: property.property_usage_details?.[0]?.usageType?.usage_type || '',
        financial_year: financialYear,
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}
