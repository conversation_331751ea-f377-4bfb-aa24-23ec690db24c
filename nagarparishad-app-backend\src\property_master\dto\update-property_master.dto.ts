import { PartialType } from '@nestjs/swagger';
import { IsString, <PERSON>N<PERSON>ber, IsOptional, IsUUID, IsBoolean, IsIn, Min } from 'class-validator';
import { CreatePropertyMasterDto } from './create-property_master.dto';

// Update DTO for the main property entity
export class UpdatePropertyMasterDto extends PartialType(CreatePropertyMasterDto) {}

// DTO for property owner details
export class PropertyOwnerDetailsDto {
  @IsOptional()
  @IsUUID()
  property_owner_details_id?: string;


  
  @IsString()
  @IsOptional()
  owner_type_id: string;

  @IsOptional()
  @IsString()
  owner_type?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  mobile_number?: string;

  @IsOptional()
  @IsString()
  email_id?: string;

  @IsOptional()
  @IsString()
  aadhar_number?: string;

  @IsOptional()
  @IsString()
  pan_card?: string;

  @IsOptional()
  @IsIn(['MALE', 'FEMALE', 'OTHER', null]) // Allows these specific values, including null
  gender?: 'MALE' | 'FEMALE' | 'OTHER' | null;

  @IsOptional()
  @IsBoolean()
  marital_status?: boolean;

  @IsOptional()
  @IsString()
  partner_name?: string;
}

// DTO for common fields
export class CommonFieldsDto {
  @IsOptional()
  @IsString()
  commonFieldId?: string; 

  
  @IsOptional()
  @IsString()
  GISID?: string;

  @IsOptional()
  @IsString()
  propertyDescription?: string;

  @IsOptional()
  @IsString()
  completionCertificate?: string;

  @IsOptional()
  @IsString()
  accessRoad?: string;

  @IsOptional()
  @IsString()
  individualToilet?: 'yes' | 'no';

  @IsOptional()
  @IsString()
  toiletType?: string;

  @IsOptional()
  @Min(0)
  @IsNumber()
  totalNumber?: number | null;

  @IsOptional()
  @IsString()
  lightingFacility?: string;

  @IsOptional()
  @IsString()
  tapConnection?: string;

  @IsOptional()
  @Min(0)
  @IsNumber()
  totalConnections?: number | null;

  @IsOptional()
  @IsString()
  solarProject?: 'yes' | 'no';

  @IsOptional()
  @IsString()
  rainWaterHarvesting?: 'yes' | 'no';

  @IsOptional()
  @IsString()
  sewageSystem?: 'yes' | 'no';

  @IsOptional()
  @Min(0)
  @IsNumber()
  groundFloorArea?: number | null;

  @IsOptional()
  @Min(0)
  @IsNumber()
  remainingGroundFloorArea?: number | null;
}

// DTO for property usage details
export class PropertyUsageDetailsDto {
  @IsOptional()
  @IsUUID()
  property_usage_details_id?: string ;

  @IsOptional()
  @IsString()
  propertyType_id?: string;

  @IsOptional()
  @IsString()
  usage_type_id?: string;

  @IsOptional()
  @IsString()
  floor_id?: string;

  @IsOptional()
  @IsString()
  usage_sub_type_master_id?: string;


  @IsOptional()
  @IsString()
  property_type_desc?: string;

  @IsOptional()
  @IsNumber()
  construction_area?: number;

  @IsOptional()
  @IsNumber()
  length?: number;

  @IsOptional()
  @IsNumber()
  width?: number;

  @IsOptional()
  @IsNumber()
  are_sq_ft?: number;

  @IsOptional()
  @IsNumber()
  are_sq_meter?: number;

  @IsOptional()
  @IsString()
  floor?: string;

  @IsOptional()
  @IsString()
  flat_no?: string;

  @IsOptional()
  @IsNumber()
  annual_rent?: number;

  @IsOptional()
  @IsString()
  construction_start_date?: string;

  @IsOptional()
  @IsString()
  construction_end_date?: string;

  @IsOptional()
  @IsString()
  construction_start_year?: string;

  @IsOptional()
  @IsString()
  authorized?: 'yes' | 'no';
}

// Main DTO for updating a property
export class UpdatePropertyDto {
  @IsOptional()
  @IsString()
  old_propertyNumber?: string;

  @IsOptional()
  @IsString()
  city_survey_number?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsString()
  @IsOptional()
  house_number?: string;
  
  @IsString()
  @IsOptional()
  block_number?: string;

  @IsString()
  @IsOptional()
  plot_number?: string;
  
  @IsString()
  @IsOptional()
  landmark: string;

  @IsString()
  @IsOptional()
  property_desc : string;

  @IsOptional()
  @IsString()
  house_or_apartment_name?: string;

  @IsOptional()
  @IsString()
  latitude?: string;

  @IsOptional()
  @IsString()
  longitude?: string;

  @IsOptional()
  @IsString()
  mobile_number?: string;

  @IsOptional()
  @IsString()
  email_id?: string;

  @IsOptional()
  @IsNumber()
  plot_area?: number;

  @IsOptional()
  @IsNumber()
  Plot_construction_area?: number;

  @IsOptional()
  @IsNumber()
  construction_area?: number;

  @IsOptional()
  @IsString()
  remark?: string;

    @IsOptional()
  @IsString()
  property_remark?: string;

  @IsOptional()
  @IsUUID()
  street_id?: string;

  @IsOptional()
  @IsUUID()
  ward_id?: string;
  
  @IsOptional()
  @IsUUID()
  floor_id?: string;

  @IsOptional()
  @IsUUID()
  zone_id?: string;

  @IsOptional()
  property_owner_details?: PropertyOwnerDetailsDto[];

  @IsOptional()
  property_usage_details?: PropertyUsageDetailsDto[];

  @IsOptional()
  commonFields?: CommonFieldsDto;

  @IsOptional()
  deleted_property_usage_details_id? : string[];
}
