import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";

// Translation resources
import enTranslation from "./en.json";
import mrTranslation from "./mr.json";
import gjTranslation from "./gj.json";
import hiTranslation from "./hi.json";

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: "mr"|| localStorage.getItem("i18nextLng") ,
    fallbackLng: "mr",
    debug: true,
    resources: {
      en: {
        translation: enTranslation,
      },
      mr: {
        translation: mrTranslation,
      },
      hi:{
        translation: hiTranslation,

      },
      gj:{
        translation: gjTranslation,

      }
    },
    detection: {
      order: ["localStorage", "cookie", "querystring", "navigator", "htmlTag"],
      caches: ["localStorage", "cookie"],
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
