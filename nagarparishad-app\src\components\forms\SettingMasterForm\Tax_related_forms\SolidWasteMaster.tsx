import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRR_RateController } from '@/controller/tax/RR-RateController';
import WhiteContainer from '@/components/custom/WhiteContainer';
import TanStackTable from '@/components/globalcomponent/tanstacktable';
import { ConstructionRateSetting } from '@/model/tax/constructionRate';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { RR_RateSetting } from '@/model/tax/RR-Rate';
import AsyncSelect from "@/components/ui/react-select";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from '@/components/ui/use-toast';
import { useZoneMasterController } from '@/controller/master/ZoneMasterController';
import { ReactselectInterface } from '@/model/global-master';
import { ZoneObject } from '@/model/zone-master';
import { z } from 'zod';
import { useSolidWasteController } from '@/controller/tax/SolidWasteRateController';
import { SolidWasteRatemasterUpdateApi, solidWasteRateSetting } from '@/model/tax/solidWaste';
import { useUsageSubController } from '@/controller/master/UsageSubController';
import DeletePopUpScreen from '@/components/custom/DeletePopUpScreen';
import { ArrowUpDown, Edit, Trash } from 'lucide-react';
import { propertyUsageSubTypeFilterFn } from '@/components/globalcomponent/TanstackFilter';
import { GlobalContext } from '@/context/GlobalContext';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ResponseData } from '@/model/auth/authServices';
import Api from '@/services/ApiServices';
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
import PropertyApi from '@/services/PropertyServices';
interface solidWasteRateInterface {
  editData?: SolidWasteRatemasterUpdateApi;
}

const SolidWasteMaster = ({editData}:solidWasteRateInterface) => {
  const [rates, setRates] = useState([
    {
      rr_rate_id: 'c128f61b-163b-4f7e-b907-7b788e169eaf',
      financial_year: '2023-2024',
      value: 1180.0,
      status: 'Active',
      zone_id: '325f6d4d-91be-4971-a5cf-c2839eccb593',
    },
  ]);
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // New state for editing

  const schema = z.object({
    financial_year: z.string().optional(), // Optional for backward compatibility
    reassessment_range_id: z.string().trim().min(1, t("errorsRequiredField")),
    value: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
    status: z.string().trim().min(1, t("errorsRequiredField")),
    usage_sub_type_master_id : z.string().trim().min(1, t("errorsRequiredField")),
  });

  const { solidWasteRateList,deleteSolidWasteRate,createSolidWasteRate,updateSolidWasteRate, propertyLoading } = useSolidWasteController();
  const constructionRateLists = solidWasteRateList?.data || []; // Access the data array
  console.log ("constructionRateLists", solidWasteRateList);
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const [financialYears, setFinancialYears] = useState([]);
  const userRef = useRef(null);
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
  const [formData, setFormData] = useState({
    financial_year: '',
    value: null,
    status: 'Active',
    usage_sub_type_master_id: '',
  });
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Setting, FormName.GhanKachraRate,Action.CanRead );
  const canUpdate = canPerformAction(ModuleName.Setting, FormName.GhanKachraRate,Action.CanUpdate );
  const CanCreate = canPerformAction(ModuleName.Setting, FormName.GhanKachraRate,Action.CanCreate );
  const CanDelete = canPerformAction(ModuleName.Setting, FormName.GhanKachraRate,Action.CanDelete );

  // const handleInputChange = (e) => {
  //   const { name, value } = e.target;
  //   setFormData({ ...formData, [name]: value });
  // };
  const [reassessmentRanges, setReassessmentRanges] = useState([]);

  useEffect(() => {
    fetchReassessmentRanges();
  }, []);

  const fetchReassessmentRanges = async () => {
    try {
      console.time("Fetch reassessment ranges");
      PropertyApi.getReassessmentRanges((response) => {
        console.timeEnd("Fetch reassessment ranges");

        if (response.status && response.data.statusCode === 200) {
          const formattedData = response.data.data.map(item => ({
            ...item,
            reassessment_id: item.reassessment_range_id,
            reassessment_range: `${item.start_range} to ${item.end_range}`
          }));
          setReassessmentRanges(formattedData);
        }
      });
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };
  const usageSubList: any = useUsageSubController();
  const dynamicValues = {
    name: t("setting.solidWasteRate"),
  };

  const usagesubOptions: ReactselectInterface[] =
    usageSubList?.usageSubList?.map((usagesubtype: any) => ({
      value: usagesubtype.usage_sub_type_master_id,
      label: usagesubtype.usage_sub_type,
    }));

    const Form = FormProvider;

    const form = useForm<z.infer<typeof schema>>({
      resolver: zodResolver(schema),
      defaultValues: {
        financial_year: editData?.financial_year || "",
        reassessment_range_id: editData?.reassessment_range_id || "",
        value: editData?.value || null,
        status: editData?.status || "Active",
        usage_sub_type_master_id: editData?.usage_sub_type_master_id || "",
      },
    });
    const {  formState: { errors }, reset, control } = form;


    const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData({ ...formData, [name]: name === 'value' ? Number(value) : value });
    };


  const handleSelectChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
  };

  const handleusageSubChange = (selectedOption) => {
    setFormData({ ...formData, usage_sub_type_master_id: selectedOption.value });
  };

  const checkDuplicate = (reassessmentRangeId: string, usageSubId: string) => {
    return constructionRateLists.some(item =>
      item.reassessmentRange?.reassessment_range_id === reassessmentRangeId &&
      item.UsageSubType.usage_sub_type_master_id === usageSubId &&
      (!isEditing || item.ghanKachra_rate_id !== editingId)
    );
  };

  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();



    console.log("Form data:", data);
    if (checkDuplicate(data.reassessment_range_id, data.usage_sub_type_master_id)) {
      toast({
        title: t("या पुनर्मूल्यांकन श्रेणीसाठी आणि मालमत्ता वापर उपप्रकारसाठी दर आधीच अस्तित्वात आहे"),
        variant: "destructive",
      });
      return;
    }


      if (isEditing) {
        updateSolidWasteRate(
          {
            solidWasteRateId: editingId,
            solidWasteRateData: data,
          },
          {
            onSuccess: (response) => {
              toast({
                title: t("api.formupdate", dynamicValues),
                variant: "success",
              });
              reset({
                financial_year:"",
                reassessment_range_id: "",
                value: '',
                status:"Active",
                usage_sub_type_master_id:"",
              });
              setIsEditing(false);
            setEditingId(null);
            },
            onError: (error) => {
              console.log("error",error)
              toast({
                title:"error",
                variant: "destructive",
              });
            },
          }
        );
      } else {
        console.log("in creationprocess");
        createSolidWasteRate(data, {
          onSuccess: (response) => {
            toast({
              title: t("api.formcreate", dynamicValues),
              variant: "success",
            });
           reset({
              financial_year:"",
              reassessment_range_id: "",
              value: '',
              status:"Active",
              usage_sub_type_master_id:"",
            });
          },
          onError: (error) => {
            console.log("error", error)
            toast({
              title: error.message || t("api.error"),
              variant: "destructive",
            });
          },
        });

    }
  };
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<solidWasteRateSetting | null>(null);

  function handleDelete(item:solidWasteRateSetting): void {
    setSelectedItemId(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDeletes = async () => {
    if (selectedItemId) {
      try {
        await deleteSolidWasteRate(selectedItemId.ghanKachra_rate_id);
        toast({
          title: t("api.formdelete"),
          variant: "success",
        });
      } catch (error) {
        toast({
          title: error.message,
          variant: "destructive",
        });
      }
    }

    setIsDeleteOpen(false);
    setSelectedItemId(null);
  };
  const handleConfirmDelete = () => {
    if (selectedItemId) {
      deleteSolidWasteRate(selectedItemId.ghanKachra_rate_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItemId(null);
  };
  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItemId(null);
  };

  const handleEdit = (rate:solidWasteRateSetting) => {
   setOpen(true)
    setIsEditing(true);
    setEditingId(rate.ghanKachra_rate_id);

   toggleCollapse();

   if (userRef.current) {
     userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
   }
   reset({
     financial_year: rate.financial_year,
     reassessment_range_id: rate.reassessmentRange?.reassessment_range_id || "",
     value: rate.value,
     status: rate.status,
     usage_sub_type_master_id: rate.UsageSubType.usage_sub_type_master_id,
   });

  };




  const columns: ColumnDef<solidWasteRateSetting>[] = [
    {
      accessorKey: "reassessmentRange",
      header: t("setting.reassessmentRange"),
      cell: ({ row }) => {
        const startRange = row.original?.reassessmentRange?.start_range;
        const endRange = row.original?.reassessmentRange?.end_range;
        return <div>{startRange && endRange ? `${startRange} to ${endRange}` : row.original?.financial_year}</div>;
      },
    },
    {
      accessorKey: "usage_sub_type",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertyAssessmentDetailsForm.propertyUsageSubType")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original.UsageSubType?.usage_sub_type}</div>,
      filterFn:propertyUsageSubTypeFilterFn,
    },
     {
      accessorKey: "status",
      header: t("Status"),
      cell: ({ row }) => <div>{row.original?.status}</div>,
    },

    {
      accessorKey: "value",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("Value")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original?.value}</div>,
    },

    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: "actions",
            header: t("Actions"),
            cell: ({ row }) => (
              <>
                {canUpdate && (
                  <button
                    className="h-8 w-8 p-0"
                    onClick={() => handleEdit(row.original)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {CanDelete && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleDelete(row.original)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  // const handleSubmit = (e) => {
  //   e.preventDefault();
  //   setRates([
  //     ...rates,
  //     {
  //       ...formData,
  //       rr_rate_id: Math.random().toString(36).substr(2, 9),
  //     },
  //   ]);
  //   setFormData({ financial_year: '', value: '', status: 'Active', zone_id: '' });
  // };

  const handleDeletes = (id) => {
    setRates(rates.filter(rate => rate.rr_rate_id !== id));
  };

  return (
    <div className="p-6"
    ref={userRef && userRef}
>
      <h1 className="text-2xl font-bold mb-4">{t("setting.solidWasteRate")}</h1>
      {(canUpdate || CanCreate) && (
      <WhiteContainer>
      <Form {...form}>

      <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="grid md:grid-cols-5 gap-x-3">
            <div>
            <FormField
                control={form.control}
                name="reassessment_range_id"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("setting.reassessmentRange")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Select
               {...field}
               onValueChange={field.onChange}
                     defaultValue={field.value}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("setting.selectReassessment")} />
                </SelectTrigger>
                <SelectContent>
                  {reassessmentRanges.map((range) => (
                    <SelectItem key={range.reassessment_range_id} value={range.reassessment_range_id}>
                      {`${range.start_range} to ${range.end_range}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.reassessment_range_id && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>
            <div>
              {/* <Label>{t("propertyAssessmentDetailsForm.propertyUsageSubType")}</Label> */}
              <FormField
                control={form.control}
                name="usage_sub_type_master_id"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel> {t("propertyAssessmentDetailsForm.propertyUsageSubType")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
                    <Controller
                          control={form.control}
                          name="usage_sub_type_master_id"
                          render={({ field: controllerField }) => (
              <AsyncSelect
              value={
                usagesubOptions.find(
                  (option: any) =>
                    option.value === controllerField.value
                ) || null
              }
                              placeholder={t("propertyAssessmentDetailsForm.propertyUsageSubType")}
                className="mt-1 h-[2.5rem]"
                options={usagesubOptions}
                colourOptions={usagesubOptions}
                onChange={(selectedOption: any) => {
                  controllerField.onChange(selectedOption.value)
                }}                />
            )}
            />
            {errors.usage_sub_type_master_id && (
                    <FormMessage className="ml-1">
                      {t("errors.requiredField")}
                    </FormMessage>
                  )}
            </FormItem>
              )}
            />
            </div>
            <div>
              {/* <Label>{t("Status")}</Label> */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>                {t("setting.status")}
                    <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Select
                 {...field}
                 onValueChange={field.onChange}
                 defaultValue={field.value}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("selectStatus")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("setting.value")}
                      <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
              <Input
              {...field}
                type="number"
                id="value"
                name="value"
                value={field.value}
                onChange={(event) => field.onChange(Number(event.target.value))}
                placeholder={t("setting.value")}
                className="mt-1 block w-full"
                required
                min="0"
              />
                {errors.value && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
              </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid  mb-1 ml-1 max-md:flex max-md:justify-end  pt-[32px] ">
              <Button type="submit">
              {isEditing ? t("update") : t("add")}
              </Button>
            </div>
          </div>
        </form>
        </Form>
        <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
            {" "}
            {t("allFieldAreRequire")}{" "}
          </p>
</WhiteContainer>
)}
      {/* RR Rates Table */}


      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={constructionRateLists}
          searchKey={"propertyAssessmentDetailsForm.propertyUsageSubType"}
          searchColumn={"usage_sub_type"}
          loader={propertyLoading ? true : false}
        />
      </WhiteContainer>
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={"delete"}
          onDelete={handleConfirmDelete}
        />

    </div>
  );
};

export default SolidWasteMaster;
