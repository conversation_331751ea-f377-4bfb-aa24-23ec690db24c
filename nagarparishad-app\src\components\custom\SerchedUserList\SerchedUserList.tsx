import React, { FC, useContext, useState } from "react";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { Button } from "@/components/ui/button";
import WhiteContainer from "../WhiteContainer";
import { GlobalContext } from "@/context/GlobalContext";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Printer } from "lucide-react";
import axios from "axios";
import { toast } from "@/components/ui/use-toast";
const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

const SearchedUserList: FC = () => {
  const navigate = useNavigate();

  // const { searchResults } = useContext(GlobalContext);
  // const data = searchResults?.data?.data || [];
  const { t } = useTranslation();
  const [printloading, setPrintLoading] = useState(false);
  const location = useLocation();
  const searchResult = location.state?.searchResults || [];
  const result = searchResult?.data
  console.log("searchResult",searchResult)
  // Define columns for the table
  const dummyData = [
    {
      propertyNumber: "PN001",
      ownerName: "Rajesh Kumar",
      mobileNumber: "9876543210",
      wardName: "Ward 1",
      zone: "Zone A",
      paidTax: "₹15,000",
      dueTax: "₹5,000",
    },
    {
      propertyNumber: "PN002",
      ownerName: "Rajesh Kumar",
      mobileNumber: "9876543210",
      wardName: "Ward 2",
      zone: "Zone B",
      paidTax: "₹20,000",
      dueTax: "₹3,000",
    },
    {
      propertyNumber: "PN003",
      ownerName: "Rajesh Kumar",
      mobileNumber: "9876543210",
      wardName: "Ward 3",
      zone: "Zone C",
      paidTax: "₹18,000",
      dueTax: "₹2,000",
    },
    {
      propertyNumber: "PN004",
      ownerName: "Rajesh Kumar",
      mobileNumber: "9876543210",
      wardName: "Ward 4",
      zone: "Zone D",
      paidTax: "₹22,000",
      dueTax: "₹4,000",
    },
    {
      propertyNumber: "PN005",
      ownerName: "Rajesh Kumar",
      mobileNumber: "9876543210",
      wardName: "Ward 5",
      zone: "Zone E",
      paidTax: "₹19,000",
      dueTax: "₹1,500",
    },
  ];
  const handleOnlinepayment = async (item: any) => {
    try {
      // Get the latest warshikKar entry
      const latestWarshikKar = item.warshikKar?.sort((a: any, b: any) => 
        new Date(b.financial_year).getTime() - new Date(a.financial_year).getTime()
      )[0];

      if (!latestWarshikKar?.warshik_karId) {
        toast({
          title: "Error",
          description: `${item.propertyNumber }  has no warshik_kar`,
          variant: "destructive",
        });
        return;
      }

      const response = await axios.get(
        `${apiBaseUrl}/v1/user-property/getPropertyTax?warshik_karId=${latestWarshikKar.warshik_karId}&propertyNumber=${item.propertyNumber}`
      );
      
      if (response.data) {
        navigate("/payment", {
          state: {
            rowData: {
              ...item,
              name: item.property_owner_details?.[0]?.name,
              warshik_karId: latestWarshikKar.warshik_karId

            },
            taxDetails: response.data.data.taxDetails,
            taxTypes:response.data.data.taxTypes
          },
          
        });
        console.log("warshik_karId",latestWarshikKar.warshik_karId)

      } else {
        toast({
          title: "Error",
          description: "Failed to fetch tax details",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching tax details:", error);
      toast({
        title: "Error",
        description: "Failed to fetch tax details. Please try again.",
        variant: "destructive",
      });
    }
  };


  // const handleView = (item: any) => {
  //   navigate("/search-list/detail", { state: item });
  // };

  const handleView = async (item: any) => {
    try {
      const response = await axios.get(
        `${apiBaseUrl}/v1/property-master/getOne?searchOn=property_id&value=${item.property_id}`
      );
  
      if (response.data) {
        navigate("/search-list/detail", {
          state: {
            propertyData: response.data.data, 
          },
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch property details",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching property details:", error);
      toast({
        title: "Error",
        description: "Failed to fetch property details. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  const columns: any[] = [
    {
      accessorKey: "srNo",
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "propertyNumber",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.propertyNo")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.propertyNumber}</div>
      ),
    },
    {
      accessorKey: "ownerName",
      header: ({ column }) => (
        <Button
          className="px-2 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.ownerName")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => {
        const owners = row.original?.property_owner_details || [];
        return (
          <div className="capitalize">
            {owners.map((owner: any) => owner.name).join(", ")}
          </div>
        );
      },
    },    
    {
      accessorKey: "zone",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.zone")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.original.zone?.zoneName}</div>,
    },
    {
      accessorKey: "wardName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("wardName")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original?.ward?.ward_name}</div>
      ),
    },
    {
      accessorKey: "mobileNumber",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("Mobile")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.property_owner_details[0]?.mobile_number || "--"}</div>
      ),
    },
    {
      accessorKey: "paidTax",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("paidTax")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.paidTax || "--"}</div>
      ),
    },
    {
      accessorKey: "dueTax",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("remainderTax")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.dueTax || "--"}</div>
      ),
    },
    {
      accessorKey: t("Actions"),
      header: ({ column }) => (
        <Button
          className="px-0  text-base font-semibold text-center flex justify-center w-full"
          variant="ghost"
        >
        {t("Actions")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="flex w-fit">
          <div>
            <Button
              variant="submit"
              className=" justify-center w-full   !border-BlueText !text-white h-8 py-2 my-2"
              onClick={() => handleView(row?.original)}
            >
              {t("table.view")}
            </Button>
          </div>
          <div className="">
            <Button
              onClick={() => handleOnlinepayment(row?.original)}
              variant="submit"
              className=" justify-center w-full   !border-BlueText !text-white h-8 py-2 my-2 ml-3"
              disabled={printloading}
            >
          {t("BillDetails")}
            </Button>
          </div>
        </div>
      ),
    },
  ];

  const payTax = (row: any) => {
    navigate("/payment", { state: { rowData: row.original } });
  };

  return (
    <div className="bg-Secondary w-full py-9">
      <WhiteContainer className="p-10 max-w-[80%] mx-auto">
        <h2 className="text-xl font-semibold mb-4 text-center">
          {t("availableAssets")}
        </h2>
        <div className="max-h-screen overflow-y-auto">
          <TanStackTable columns={columns} data={result} />
        </div>
      </WhiteContainer>
    </div>
  );
};

export default SearchedUserList;
