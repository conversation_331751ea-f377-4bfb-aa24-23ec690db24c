import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import logo from "../../../assets/img/homepage/logo.png";
import flag from "../../../assets/img/homepage/flag.png";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Header = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const [inputValue, setInputValue] = useState("");

  const currentPath = location.pathname;

  // Paths where login should be hidden
  const hideLoginPaths = ["/search-list", "/search-list/detail", "/payment","/login", "/forgot-password"]
  const shouldHideLogin = hideLoginPaths.includes(currentPath);

  return (
    <div className="bg-white text-black h-fit">
      <div className="max-w-[92rem] mx-auto sm:py-6 py-0 px-2">
        <div className="flex justify-between h-16 items-center">
          <Link to={"/"}>
            <div className="flex items-center">
              <div className="w-44 min-w-32 min-h-32 h-36 mr-2 -mt-5 sm:block hidden">
                <img src={logo} alt="Logo" className="w-full h-full" />
              </div>
              <div className="w-full h-full">
                <h2 className="sm:text-4xl text-2xl text-[#661600] font-bold">
                  {t("HeaderSectionLogoname")}
                </h2>
              </div>
            </div>
          </Link>

          <div className="flex items-center gap-x-4">
            {!shouldHideLogin && (
              <Link to={"/login"}>
                <Button className="px-3 py-2 text-sm font-medium ml-4">
                  {t("HeaderSectionLogin")}
                </Button>
              </Link>
            )}
            <div className="my-4">
              {/* <img src={flag} alt="Flag" className="w-full h-full" /> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
