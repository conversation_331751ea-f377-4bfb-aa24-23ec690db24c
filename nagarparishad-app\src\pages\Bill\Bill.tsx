import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { Percent } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader } from "@/components/globalcomponent/Loader";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { useBookController } from "@/controller/tax/BookeMasterController";
import TaxListApi from "@/services/TaxServices";
import Api from "@/services/ApiServices";
import PaymentListApi from "@/services/PaymentLogsService";
import { useTranslation } from "react-i18next";

const Bill = () => {
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");
  const [propertyNumber, setPropertyNumber] = useState("");
  const [oldPropertyNumber, setOldPropertyNumber] = useState("");
  const [searchOnParameter, setSearchOnParameter] = useState("");
  const [loading, setLoading] = useState(false);
  const [namunaDetailNine, setNamunaDetail] = useState(null);
  const [financialYears, setFinancialYears] = useState([]);

  const [selectedBookNumber, setSelectedBookNumber] = useState("");
  const [selectedReceiptNumber, setSelectedReceiptNumber] = useState("");
  const [receiptData, setReceiptData] = useState([]);
  const [selectedTaxPayer, setSelectedTaxPayer] = useState("");
  const [remark, setRemark] = useState("");
  const [paymentDate, setPaymentDate] = useState("");
  const [receiptLoading, setReceiptLoading] = useState(false);
  const [taxData, setTaxData] = useState([]);
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [payTaxAmount, setPayTaxAmount] = useState(0);
  const [prevPaidTotal, setPrevPaidTotal] = useState(0);
  const [currPaidTotal, setCurrPaidTotal] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [applyDiscount, setApplyDiscount] = useState(false);
  const [discountAmount, setDiscountAmount] = useState(0);
  const { BookList, bookLoading } = useBookController();
  const { t } = useTranslation();

  const handlePropertyNumberChange = (e) => {
    setPropertyNumber(e.target.value.trim().toUpperCase());
    setOldPropertyNumber("");
    setSearchOnParameter("propertyNumber");
  };

  const handleOldPropertyNumberChange = (e) => {
    setOldPropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyNumber("");
    setSearchOnParameter("old_propertyNumber");
  };

  const handlePaidChange = (index, value, maxAmount, type) => {
    if (applyDiscount) return; // Prevent editing if discount is applied

    if (maxAmount === 0) return;
    let numValue = Math.max(0, parseInt(value));
    numValue = Math.min(numValue, maxAmount);

    const updatedData = [...taxData];
    if (!updatedData[index]) {
      updatedData[index] = { prev: 0, curr: 0 };
    }

    if (type === "prev") {
      updatedData[index].prev = numValue;
    } else if (type === "curr") {
      updatedData[index].curr = numValue;
    }

    setTaxData(updatedData);

    const prevTotal = updatedData.reduce((sum, row) => sum + (row?.prev), 0);
    const currTotal = updatedData.reduce((sum, row) => sum + (row?.curr), 0);

    setPrevPaidTotal(prevTotal);
    setCurrPaidTotal(currTotal);

    updatePaymentAmount(prevTotal, currTotal);
  };

  const updatePaymentAmount = (prevTotal, currTotal, calculateDiscount = true) => {
    const totalBeforeDiscount = prevTotal + currTotal;

    if (applyDiscount && calculateDiscount) {
      const discount = Math.round(totalBeforeDiscount * 0.05);
      setDiscountAmount(discount);
      setPayTaxAmount(totalBeforeDiscount - discount);
    } else {
      setDiscountAmount(0);
      setPayTaxAmount(totalBeforeDiscount);
    }
  };

  const formatNumber = (num) => {
    const value = Number(num);
    return value.toFixed(2);
  };

  const resetFormFields = () => {
    setSelectedBookNumber("");
    setSelectedReceiptNumber("");
    setReceiptData([]);
    setRemark("");
    setPaymentDate("");
    setPaymentMethod("cash");
    setTaxData([]);
    setPayTaxAmount(0);
    setPrevPaidTotal(0);
    setCurrPaidTotal(0);
    setApplyDiscount(false);
    setDiscountAmount(0);
  };

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
        const currentYear = response.data.data.find(year => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
      toast({
        title: "वित्तीय वर्षे आणण्यात अयशस्वी",
        variant: "destructive",
      });
    }
  };

  const handleBill = async () => {
    const selectedPropertyNumber = oldPropertyNumber || propertyNumber;
    if (!selectedFinancialYear) {
      toast({
        title: "कृपया वित्तीय वर्ष निवडा",
        variant: "destructive",
      });
      return;
    }
    if (!selectedPropertyNumber) {
      setNamunaDetail(null);
      toast({
        title: "कृपया मालमत्ता क्रमांक प्रविष्ट करा",
        variant: "destructive",
      });
      return;
    }
  
    setLoading(true);
    setNamunaDetail(null);
    setTaxData([]);
    setPrevPaidTotal(0);
    setCurrPaidTotal(0);
    setPayTaxAmount(0);
    setDiscountAmount(0);
    setApplyDiscount(false);
  
    // Reset date, book number, and receipt number
    setPaymentDate("");
    setSelectedBookNumber("");
    setSelectedReceiptNumber("");
  
    try {
      const obj = {
        value: selectedPropertyNumber,
        searchOn: searchOnParameter,
        fy: selectedFinancialYear,
      };
      const response = await PaymentListApi.getCurrentPropertyBill(obj);
      if (response.data.statusCode === 200 && response.data?.data) {
        setNamunaDetail(response.data.data);
        const latestName = response.data.data?.property_owner_details.filter(
          (name) => name.owner_type.owner_type === "स्वत:"
        );
        setSelectedTaxPayer(latestName[0]?.name);
        const initialTaxData = [
          { prev: 0, curr: 0 },
          ...Object.keys(response.data?.data.tax_types).map(() => ({
            prev: 0,
            curr: 0,
          })),
        ];
        setTaxData(initialTaxData);
      } else {
        toast({
          title: "या मालमत्तेसाठी डेटा उपलब्ध नाही",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching bill data:", error);
      toast({
        title: "बिल डेटा आणण्यात अयशस्वी",
        description:
          error.response?.data?.message || "कृपया पुन्हा प्रयत्न करा",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const fetchReceiptsOfSelectedNumber = async (bookNumber) => {
    setReceiptLoading(true);

    try {
      TaxListApi.getReceiptNumbers(bookNumber, (response) => {
        if (response.status) {
          const sortedReceiptData = response.data.data.sort((a, b) => a - b);
          setReceiptData(sortedReceiptData);
        } else {
          console.error("Error fetching receipts:", response.data);
        }
        setReceiptLoading(false);
      });
    } catch (error) {
      console.error("API error:", error);
      setReceiptLoading(false);
    }
  };

  useEffect(() => {
    if (selectedBookNumber) {
      fetchReceiptsOfSelectedNumber(selectedBookNumber);
    }
  }, [selectedBookNumber]);

  const handleSave = async () => {
    if (isSaving) return;
    setIsSaving(true);
  
    const payload = {
      property_id: namunaDetailNine?.property_id,
      property_number: namunaDetailNine?.propertyNumber,
      financial_year: selectedFinancialYear,
      book_number: selectedBookNumber,
      book_receipt_number: selectedReceiptNumber,
      all_property_tax_sum_total: (taxData[0]?.prev) + (taxData[0]?.curr),
      all_property_tax_sum_prev: taxData[0]?.prev,
      all_property_tax_sum_curr: taxData[0]?.curr,
      discount_amount: discountAmount, // Add discount amount to the payload
    };
  
    Object.entries(namunaDetailNine.tax_types).forEach(([key, value], index) => {
      const idx = index + 1;
      const prevAmount = taxData[idx]?.prev;
      const currAmount = taxData[idx]?.curr;
  
      payload[key] = prevAmount + currAmount;
      payload[`${key}_prev`] = prevAmount;
      payload[`${key}_curr`] = currAmount;
    });
  
    payload.total_tax = payTaxAmount;
    payload.remaining_amount = warshikKarItem?.total_tax - payTaxAmount - (applyDiscount ? discountAmount : 0);
    payload.payment_mode = paymentMethod;
    payload.remark = remark;
    payload.payment_date = paymentDate;
    payload.tax_payer_name = selectedTaxPayer;
    payload.property_type_discount=namunaDetailNine?.property_type_discount || 0;
  
    try {
      const response = await PaymentListApi.paidCurrentBill(payload);
      if (response.status === 201 || response.status === 200) {
        toast({
          title: "पेमेंट यशस्वी!",
          description: "तुमचे पेमेंट यशस्वीरित्या प्रक्रिया झाले आहे.",
          variant: "success",
        });
        resetFormFields();
        handleBill();
      } else {
        throw new Error("अपेक्षित प्रतिसाद नाही");
      }
    } catch (error) {
      console.error("Payment error:", error);
      toast({
        title: "पेमेंट अयशस्वी",
        description:
          error.response?.data?.message || "कृपया पुन्हा प्रयत्न करा",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  

  const calculateTotals = () => {
    if (!namunaDetailNine?.warshikKar?.[0])
      return { prevTotal: 0, currTotal: 0, total: 0 };

    const warshikKarItem = namunaDetailNine.warshikKar[0];
    let prevTotal = 0;
    let currTotal = 0;
    let total = 0;

    prevTotal += warshikKarItem.all_property_tax_sum_prev;
    currTotal += warshikKarItem.all_property_tax_sum_curr;
    total += warshikKarItem.all_property_tax_sum_total;

    Object.keys(namunaDetailNine.tax_types).forEach((key) => {
      prevTotal += warshikKarItem[`${key}_prev`];
      currTotal += warshikKarItem[`${key}_curr`];
      total += warshikKarItem[key];
    });

    return { prevTotal, currTotal, total };
  };

  const totals = calculateTotals();
  const warshikKarItem = namunaDetailNine?.warshikKar?.[0];

  const handleDateChange = (e) => {
    const selectedDate = new Date(e.target.value);
    const currentDate = new Date();
    const minYear = 1880;
    const maxYear = currentDate.getFullYear();
    

    if (isNaN(selectedDate.getTime()) || selectedDate.getFullYear() < minYear || selectedDate.getFullYear() > maxYear) {
      toast({
        title: "कृपया वैध तारीख निवडा",
        variant: "destructive",
      });
      return;
    }

    if (selectedDate > currentDate) {
      toast({
        title: "कृपया भविष्यातील तारीख निवडू  नका",
        variant: "destructive",
      });
      return;
    }

    setPaymentDate(e.target.value);
  };

  const handleFullPayment = () => {
    if (!namunaDetailNine || !warshikKarItem) return;

    const updatedData = [...taxData];
    updatedData[0] = {
      prev: warshikKarItem.all_property_tax_sum_prev,
      curr: warshikKarItem.all_property_tax_sum_curr,
    };

    Object.keys(namunaDetailNine.tax_types).forEach((key, index) => {
      updatedData[index + 1] = {
        prev: warshikKarItem[`${key}_prev`],
        curr: warshikKarItem[`${key}_curr`],
      };
    });

    setTaxData(updatedData);

    const prevTotal = updatedData.reduce(
      (sum, row) => sum + (row?.prev || 0),
      0
    );
    const currTotal = updatedData.reduce(
      (sum, row) => sum + (row?.curr || 0),
      0
    );

    setPrevPaidTotal(prevTotal);
    setCurrPaidTotal(currTotal);
    updatePaymentAmount(prevTotal, currTotal);
  };

  const handleDiscountChange = (checked) => {
    setApplyDiscount(checked);

    if (checked) {
      // If discount is checked, automatically apply full payment
      handleFullPayment();

      // Calculate discount on the total amount
      const total = totals.total;
      const discount = Math.round(total * 0.05);
      setDiscountAmount(discount);
      setPayTaxAmount(total - discount);
    } else {
      // Reset to current values without discount
      updatePaymentAmount(prevPaidTotal, currPaidTotal, false);
    }
  };
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins w-full ml-3 mb-6">{t("Bill")}</h1>
        <WhiteContainer>
          <div>
            <div className="mt-1 mb-3">
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
                <div className="grid-cols-subgrid">
                  <Label>
                    {t("financialYear")}
                    <span className="ml-1 text-red-500">*</span>
                  </Label>
                  <Select
                    onValueChange={setSelectedFinancialYear}
                    value={selectedFinancialYear}
                    defaultValue="2024-2025"
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectYear")} />
                    </SelectTrigger>
                    <SelectContent>
                      {financialYears.map((year) => (
                        <SelectItem
                          key={year.financial_year_range}
                          value={year.financial_year_range}
                        >
                          {year.financial_year_range}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("namunaEight.propertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    type="text"
                    placeholder={t("namunaEight.propertyNumber")}
                    transliterate={false}
                    value={propertyNumber}
                    onChange={handlePropertyNumberChange}
                    onKeyDown={(e) => e.key === "Enter" && handleBill()}
                  />
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("property.oldPropertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.oldPropertyNumber")}
                    value={oldPropertyNumber}
                    onChange={handleOldPropertyNumberChange}
                    onKeyDown={(e) => e.key === "Enter" && handleBill()}
                  />
                </div>

                <div className="flex items-end align-middle">
                  <Button variant="submit" type="submit" onClick={handleBill}>
                    {t("search")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center my-8">
            <Loader />
          </div>
        )}

        {namunaDetailNine && (
          <WhiteContainer>
            <div className="">
              <div className="pb-0">
                <div className="text-2xl font-bold text-center text-gray-800">करांचे तपशील</div>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 gap-6">
                 

                  {/* Property Information */}
                  <div className="mb-6 border-b pb-6  p-6 rounded-lg shadow-sm">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <p className="mb-2"><strong>वॉर्ड:</strong> {namunaDetailNine?.ward?.ward_name || "--"}</p>
                        <p className="mb-2">
                          <strong>नाव:</strong>{" "}
                          {namunaDetailNine?.property_owner_details
                            ?.filter((user) => user?.owner_type?.owner_type === "स्वत:")
                            .map((user) => user?.name)
                            .join(", ")}
                        </p>
                        <p className="mb-2"><strong>पत्ता:</strong> {namunaDetailNine?.street?.street_name || "--"}</p>
                        <p className="mb-2"><strong>झोन:</strong> {namunaDetailNine.zone?.zoneName || "--"}</p>
                      </div>
                      <div>
                        <p className="mb-2">
                          <strong>भोगवटादार नाव:</strong>{" "}
                          {namunaDetailNine?.property_owner_details
                            ?.filter((user) => user?.owner_type?.owner_type !== "स्वत:")
                            .map((user) => user?.name)
                            .join(", ") || "--"}
                        </p>
                        <p className="mb-2"><strong>मालमत्ता क्रमांक:</strong> {namunaDetailNine?.propertyNumber || "--"}</p>
                        <p className="mb-2"><strong>जुना मालमत्ता क्रमांक:</strong> {namunaDetailNine.old_propertyNumber || "--"}</p>
                        <p className="mb-2">
                          <strong>मोबाईल:</strong>{" "}
                          {(() => {
                            const ownerDetails = namunaDetailNine?.property_owner_details || [];
                            const svatOwner = ownerDetails.find((owner) => owner.owner_type.owner_type === "स्वत:");
                            const bhogwtadarOwner = ownerDetails.find((owner) => owner.owner_type.owner_type === "भोगवटादार");
                            return svatOwner ? svatOwner.mobile_number : bhogwtadarOwner ? bhogwtadarOwner.mobile_number : "--";
                          })()}
                        </p>
                      </div>
                    </div>
                  </div>
 {/* Receipt and Date Info at the top */}
                  <div className="  rounded-lg shadow-sm">
                    <div className="p-6">
                      <div className="grid md:grid-cols-4 gap-6">
                        <div>
                          <Label className="mb-2 block font-medium text-gray-700">बुक क्रमांक</Label>
                          <Select
                            onValueChange={setSelectedBookNumber}
                            value={selectedBookNumber}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="बुक क्रमांक निवडा" />
                            </SelectTrigger>
                            <SelectContent>
                              {BookList.sort((a, b) => a.book_number - b.book_number).map((book) => (
                                <SelectItem
                                  key={book.book_id}
                                  value={book.book_number}
                                >
                                  {book.book_number}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label className="mb-2 block font-medium text-gray-700">पावती क्रमांक</Label>
                          <Select
                            onValueChange={setSelectedReceiptNumber}
                            value={selectedReceiptNumber}
                            disabled={!selectedBookNumber || receiptLoading}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="पावती क्रमांक निवडा" />
                            </SelectTrigger>
                            <SelectContent>
                              {receiptData?.sort((a, b) => a - b).map((receipt) => (
                                <SelectItem key={receipt} value={receipt}>
                                  {receipt}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label className="mb-2 block font-medium text-gray-700">भरणा दिनांक</Label>
                          <Input
                            type="date"
                            value={paymentDate}
                            onChange={handleDateChange}
                            className="w-full cursor-pointer"
                            max={today}
                          />
                        </div>

                        <div className="flex items-center space-x-2 h-full pt-6">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="discount"
                              checked={applyDiscount}
                              onCheckedChange={handleDiscountChange}
                            />
                            <div className="flex items-center space-x-1">
                              <Label htmlFor="discount" className="font-medium text-gray-700"> सवलत</Label>
                              <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                                5                                <Percent className="h-3 w-3 mr-1" />

                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Tax Details Table and Payment Details */}
                  {paymentDate && (
                    <>
                      <div className="w-full space-y-4">
                        <div className="flex justify-end">
                          <Button
                            onClick={handleFullPayment}
                            disabled={applyDiscount}
                            className="mb-2"
                          >
                            पूर्ण पेमेंट
                          </Button>
                        </div>

                        <div className="rounded-lg border shadow-sm overflow-hidden">
                          <Table>
                            <TableHeader>
                              <TableRow className="bg-gray-100">
                                <TableHead className="font-semibold text-gray-900 text-center">करांचे तपशील</TableHead>
                                <TableHead className="font-semibold text-gray-900 text-center">थकबाकी रक्कम</TableHead>
                                <TableHead className="font-semibold text-gray-900 text-center">चालू मागणी रक्कम</TableHead>
                                <TableHead className="font-semibold text-gray-900 text-center">एकूण रक्कम</TableHead>
                                <TableHead className="font-semibold text-gray-900 text-center">मागील वसूल</TableHead>
                                <TableHead className="font-semibold text-gray-900 text-center">चालू वसूल</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              <TableRow>
                                <TableCell className="text-center">संकलित कर (घरपट्टी)</TableCell>
                                <TableCell className="text-center">
                                  <span className="w-24 text-center mx-auto">
                                    {formatNumber(warshikKarItem?.all_property_tax_sum_prev)}
                                  </span>
                                </TableCell>
                                <TableCell className="text-center">
                                  <span className="w-24 text-center mx-auto">
                                    {formatNumber(warshikKarItem?.all_property_tax_sum_curr)}
                                  </span>
                                </TableCell>
                                <TableCell className="text-center">
                                  <span className="w-24 text-center mx-auto">
                                    {formatNumber(warshikKarItem?.all_property_tax_sum_total)}
                                  </span>
                                </TableCell>
                                <TableCell className="text-center">
                                  <input
                                    type="number"
                                    value={taxData[0]?.prev}
                                    onChange={(e) => {
                                      handlePaidChange(
                                        0,
                                        e.target.value,
                                        warshikKarItem?.all_property_tax_sum_prev,
                                        "prev"
                                      );
                                    }}
                                    className="w-28 text-center mx-auto p-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100 disabled:cursor-not-allowed appearance-none"
                                    min="0"
                                    max={warshikKarItem?.all_property_tax_sum_prev}
                                    disabled={!warshikKarItem?.all_property_tax_sum_prev || warshikKarItem?.all_property_tax_sum_prev === 0 || applyDiscount}
                                    onKeyDown={(e) => {
                                      if (e.key === "-" || e.key === "." || e.key === "e" || e.key === "ArrowUp" || e.key === "ArrowDown") {
                                        e.preventDefault();
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell className="text-center">
                                  <input
                                    type="number"
                                    value={taxData[0]?.curr || 0}
                                    onChange={(e) => {
                                      handlePaidChange(
                                        0,
                                        e.target.value,
                                        warshikKarItem?.all_property_tax_sum_curr,
                                        "curr"
                                      );
                                    }}
                                    className="w-28 text-center mx-auto p-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100 disabled:cursor-not-allowed appearance-none"
                                    min="0"
                                    max={warshikKarItem?.all_property_tax_sum_curr}
                                    disabled={!warshikKarItem?.all_property_tax_sum_curr || warshikKarItem?.all_property_tax_sum_curr === 0 || applyDiscount}
                                    onKeyDown={(e) => {
                                      if (e.key === "-" || e.key === "." || e.key === "e" || e.key === "ArrowUp" || e.key === "ArrowDown") {
                                        e.preventDefault();
                                      }
                                    }}
                                  />
                                </TableCell>
                              </TableRow>
                              {namunaDetailNine &&
                                Object.entries(namunaDetailNine.tax_types).map(([key, value], index) => (
                                  <TableRow key={key} className={value === "Total Amount Due" ? "font-semibold bg-gray-50" : ""}>
                                    <TableCell className="text-center">{value}</TableCell>
                                    <TableCell className="text-center">
                                      <span className="w-24 mx-auto">
                                        {formatNumber(warshikKarItem[`${key}_prev`])}
                                      </span>
                                    </TableCell>
                                    <TableCell className="text-center">
                                      <span className="w-24 mx-auto">
                                        {formatNumber(warshikKarItem[`${key}_curr`])}
                                      </span>
                                    </TableCell>
                                    <TableCell className="text-center">
                                      <span className="w-24 mx-auto">
                                        {formatNumber(warshikKarItem[key])}
                                      </span>
                                    </TableCell>
                                    <TableCell className="text-center">
                                      <input
                                        type="number"
                                        value={taxData[index + 1]?.prev}
                                        onChange={(e) =>
                                          handlePaidChange(
                                            index + 1,
                                            e.target.value,
                                            warshikKarItem[`${key}_prev`],
                                            "prev"
                                          )
                                        }
                                        className="w-28 text-center mx-auto p-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100 disabled:cursor-not-allowed"
                                        min="0"
                                        max={warshikKarItem[`${key}_prev`]}
                                        disabled={!warshikKarItem[`${key}_prev`] || warshikKarItem[`${key}_prev`] === 0 || applyDiscount}
                                        onKeyDown={(e) => {
                                          if (e.key === "-" || e.key === "." || e.key === "e") {
                                            e.preventDefault();
                                          }
                                        }}
                                      />
                                    </TableCell>
                                    <TableCell className="text-center">
                                      <input
                                        type="number"
                                        value={taxData[index + 1]?.curr}
                                        onChange={(e) =>
                                          handlePaidChange(
                                            index + 1,
                                            e.target.value,
                                            warshikKarItem[`${key}_curr`],
                                            "curr"
                                          )
                                        }
                                        className="w-28 text-center mx-auto p-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100 disabled:cursor-not-allowed"
                                        min="0"
                                        max={warshikKarItem[`${key}_curr`] || 0}
                                        disabled={!warshikKarItem[`${key}_curr`] || warshikKarItem[`${key}_curr`] === 0 || applyDiscount}
                                        onKeyDown={(e) => {
                                          if (e.key === "-" || e.key === "." || e.key === "e") {
                                            e.preventDefault();
                                          }
                                        }}
                                      />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              <TableRow className="font-bold bg-gray-100">
                                <TableCell className="text-center">एकूण देय रक्कम:</TableCell>
                                <TableCell className="text-center">{formatNumber(totals.prevTotal)}</TableCell>
                                <TableCell className="text-center">{formatNumber(totals.currTotal)}</TableCell>
                                <TableCell className="text-center">{formatNumber(totals.total)}</TableCell>
                                <TableCell className="text-center">{formatNumber(prevPaidTotal)}</TableCell>
                                <TableCell className="text-center">{formatNumber(currPaidTotal)}</TableCell>
                              </TableRow>
                              {/* {applyDiscount && (
                                <TableRow className="font-bold text-green-700 bg-green-50">
                                  <TableCell className="text-center">तात्पुरती सवलत (5%):</TableCell>
                                  <TableCell className="text-center" colSpan={3}></TableCell>
                                  <TableCell className="text-center" colSpan={2}>- {formatNumber(discountAmount)}</TableCell>
                                </TableRow>
                              )} */}
                            </TableBody>
                          </Table>
                        </div>
                      </div>

                      {/* Payment Details */}
                      <div className="mt-6">
                        <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">भरणा तपशील</h2>

                        <div className="grid grid-cols-3 gap-4 mb-6">
                          <div>
                            <label className="block text-gray-700 mb-2 font-medium">एकूण कर रक्कम:</label>
                            <input
                              type="text"
                              value={formatNumber(warshikKarItem?.total_tax)}
                              className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                              readOnly
                            />
                          </div>

                          <div className="">
                            <label className="block text-gray-700 mb-2 font-medium"> सवलत (5%):</label>
                            <input
                              type="text"
                              value={` ${formatNumber(discountAmount)}`}
                              className="w-full p-2 border border-green-300 rounded-md bg-green-50 text-green-700 font-semibold"
                              readOnly
                            />
                          </div>

                          <div>
                            <label className="block text-gray-700 mb-2 font-medium">भरणा कर रक्कम:</label>
                            <input
                              type="text"
                              value={formatNumber(payTaxAmount)}
                              className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 font-bold"
                              readOnly
                            />
                          </div>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6 mb-6">
                          <div>
                            <label className="block text-gray-700 mb-2 font-medium">शेरा</label>
                            <input
                              type="text"
                              value={remark}
                              onChange={(e) => setRemark(e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded-md"
                              placeholder="शेरा प्रविष्ट करा..."
                            />
                          </div>

                          <div>
                            <label className="block text-gray-700 mb-2 font-medium">भरणा पद्धती:</label>
                            <div className="flex items-center space-x-4 mt-2">
                              <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="radio"
                                  name="paymentMethod"
                                  value="cash"
                                  checked={paymentMethod === "cash"}
                                  onChange={() => setPaymentMethod("cash")}
                                  className="w-4 h-4 text-blue-600 cursor-pointer"
                                />
                                <span>रोख</span>
                              </label>
                              <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="radio"
                                  name="paymentMethod"
                                  value="cheque"
                                  checked={paymentMethod === "cheque"}
                                  onChange={() => setPaymentMethod("cheque")}
                                  className="w-4 h-4 text-blue-600 cursor-pointer"
                                />
                                <span>चेक</span>
                              </label>
                              <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                  type="radio"
                                  name="paymentMethod"
                                  value="online"
                                  checked={paymentMethod === "online"}
                                  onChange={() => setPaymentMethod("online")}
                                  className="w-4 h-4 text-blue-600 cursor-pointer"
                                />
                                <span>ऑनलाईन</span>
                              </label>
                            </div>
                          </div>
                        </div>

                        <div className="text-right font-bold text-gray-900 mb-6">
                          <p className="text-lg bg-blue-50 p-3 rounded-md inline-block">
                            उर्वरित देय कर रक्कम:{" "}
                            <span className="text-xl">
                              {formatNumber(warshikKarItem?.total_tax - payTaxAmount - (applyDiscount ? discountAmount : 0))}
                            </span>
                          </p>
                        </div>

                        <div className="mt-6 text-center">
                          <Button
                            onClick={handleSave}
                            disabled={payTaxAmount === 0 || isSaving}
                            className="px-8 py-2 text-lg"
                          >
                            {isSaving ? "जतन करत आहे..." : "जतन करा"}
                          </Button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default Bill;
