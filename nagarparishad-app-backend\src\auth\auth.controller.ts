import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Logger,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateAuthDto } from './dto/update-auth.dto';
import { EmailLoginDto } from './dto/email-login.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiProperty,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AccessTokenGuard,
  GetCurrentUser,
  GetCurrentUserId,
  RefreshTokenGuard,
} from '@jwt/jwt-auth';
import { OtpDto } from './dto/validate-otp.dto';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';
import { ForgotPasswordDto } from './forgot-password/dto/forgot-password.dto';
import { VerifyOtpDto } from './forgot-password/dto/verify-otp.dto';
import { ResetPasswordDto } from './forgot-password/dto/reset-password.dto';

@Public()
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('forgot-password/send-otp')
  async sendOtp(@Body(new ValidationPipe()) forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.sendOtp(forgotPasswordDto.email);
  }

  @Post('forgot-password/verify-otp')
  async verifyOtp(@Body(new ValidationPipe()) verifyOtpDto: VerifyOtpDto) {
    return this.authService.verifyOtp(verifyOtpDto.email, verifyOtpDto.otp);
  }

  @Post('forgot-password/reset-password')
  async resetPassword(@Body(new ValidationPipe()) resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.email,
      resetPasswordDto.otp,
      resetPasswordDto.password,
    );
  }

  @ApiOperation({ summary: 'Sign-up a new user in system' })
  @ApiResponse({
    status: 201,
    description: 'User has been successfully created',
  })
  @ApiResponse({
    status: 409,
    description: 'Email Already Exists',
  })
  @ApiResponse({
    status: 409,
    description: 'Mobile Number Already Exists',
  })
  @Post('sign-up')
  signUp(@Body() createUserDto: CreateUserDto) {
    return this.authService.signUp(createUserDto);
  }

  @ApiOperation({ summary: 'Sign-in user verify email' })
  @ApiResponse({
    status: 201,
    description: 'OTP sent to registered email',
  })
  @ApiResponse({
    status: 404,
    description: 'Invalid Email or Password',
  })
  @Post('sign-in')
  sighIn(@Body() emailLoginDto: EmailLoginDto) {
    return this.authService.signIn(emailLoginDto);
  }

  @ApiOperation({ summary: 'Validate OTP' })
  @ApiResponse({
    status: 201,
    description: 'User has been successfully LoggedIn',
  })
  @ApiResponse({
    status: 404,
    description: 'Invali OTP',
  })
  @Post('validate/otp')
  validateOtp(@Body() otpDto: OtpDto) {
    return this.authService.validateOtp(otpDto);
  }

  @ApiOperation({ summary: 'Sign-in user' })
  @ApiBearerAuth('access-token')
  @ApiResponse({
    status: 201,
    description: 'Signed out successfully',
  })
  @UseGuards(AccessTokenGuard)
  @Get('sign-out')
  signOut(@GetCurrentUserId() user_id: string) {
    return this.authService.signOut(user_id);
  }

  @ApiOperation({ summary: 'Sign-in user' })
  @ApiBearerAuth('refresh-token')
  @ApiResponse({
    status: 201,
    description: 'Signed out successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Access Denied',
  })
  @UseGuards(RefreshTokenGuard)
  @Get('refreshtoken')
  refreshToken(
    @GetCurrentUserId() userId: string,
    @GetCurrentUser('refreshToken') refreshToken: string,
  ) {
    return this.authService.refreshToken(userId, refreshToken);
  }
}
