import React, { useState, useEffect } from "react";
import WhiteContainer from "../custom/WhiteContainer";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "../ui/button";
import { Download } from "lucide-react";
import Api from "@/services/ApiServices";
import { toast } from "../ui/use-toast";
import { Loader } from "@/components/globalcomponent/Loader";
import ReportApi from "@/services/ReportServices";
import { useTranslation } from "react-i18next";

interface ReportCardProps {
  title: string;
  description: string;
  generationTime: string;
  format: string;
  size?: string;
  downloadAction: (financialYear: string, setProgress: (progress: number) => void) => Promise<void>;
  financialYear: string;
}

const ReportCard: React.FC<ReportCardProps> = ({
  title,
  generationTime,
  size,
  description,
  downloadAction,
  financialYear,
}) => {
  const [loading, setLoading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const { t } = useTranslation();

  const handleDownload = async () => {
    if (!financialYear) {
      toast({
        title: t("reportModule.selectFinancialYear"),
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setDownloadProgress(0);
    try {
      await downloadAction(financialYear, setDownloadProgress);
    } finally {
      setLoading(false);
      setDownloadProgress(0);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 flex flex-col md:flex-row justify-between items-start md:items-center border border-gray-200">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="text-base md:text-lg font-semibold">{title}</h3>
          <span className="px-2 py-0.5 text-xs rounded-full bg-purple-100 text-purple-700">
            {t("reportModule.financial")}
          </span>
        </div>
        <p className="text-gray-700 mb-3">{description}</p>
        {/* <div className="flex gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <span className="material-symbols-outlined text-base">calendar_today</span>
            <span>Generated: {generationTime}</span>
          </div>
          {size && (
            <div className="flex items-center gap-1">
              <span className="material-symbols-outlined text-base">description</span>
              <span>Size: {size}</span>
            </div>
          )}
        </div> */}
      </div>
      <Button
        onClick={handleDownload}
        disabled={loading || !financialYear}
        className="mt-4 md:mt-0  text-white"
      >
        {loading ? (
          <>
            {/* <Loader className=" animate-spin" /> */}
            {t("reportModule.processing")}
          </>
        ) : (
          <>
            <Download className="h-4 w-4 mr-2" />
            {t("reportModule.download")}
          </>
        )}
      </Button>
    </div>
  );
};



const ReportModule: React.FC = () => {
  const [selectedFinancialYear, setSelectedFinancialYear] = useState<string>("");
  const [financialYears, setFinancialYears] = useState<any[]>([]);
  const { t } = useTranslation();

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.status && response.data.data) {
        setFinancialYears(response.data.data);
        if (response.data.data.length > 0) {
 const currentYear = response.data.data.find((year) => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }        }
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
    }
  };

  const handleDownloadMilkatKar = async (financialYear: string, setProgress: (progress: number) => void) => {
    try {
      console.log("Starttt-->");
      const response = await ReportApi.downloadMilkatKarReport(financialYear, (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
        console.log(`Progress---->: ${percentCompleted}%`);
        setProgress(percentCompleted);
      });

      if (response.status === 200) {
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `MilkatKar_Report_${financialYear}.xlsx`;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        toast({
          title: t("reportModule.milkatkarReportDownloaded"),
          variant: "success",
        });
      }
    } catch (error) {
      console.error("Error downloading Milkatkar Report:", error);
      toast({
        title: t("reportModule.milkatkarReportFailed"),
        variant: "destructive",
      });
    }
  };

  const handleDownloadVarshikKar = async (financialYear: string, setProgress: (progress: number) => void) => {
    try {
      const response = await ReportApi.downloadVarshikKarReport(financialYear, (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
        setProgress(percentCompleted);
      });

      if (response.status === 200) {
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `VarshikKar_Report_${financialYear}.xlsx`;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        toast({
          title: t("reportModule.varshikkarReportDownloaded"),
          variant: "success",
        });
      }
    } catch (error) {
      console.error("Error downloading Varshikkar Report:", error);
      toast({
        title: t("reportModule.varshikkarReportFailed"),
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins w-full ml-3">
          {t("reportModule.downloadReportInExcel")}
        </h1>
        <WhiteContainer>
          <div className="grid md:grid-cols-3 gap-x-3 gap-2 items-end">
            {/* Dropdown for selecting Financial Year */}
            <div>
              <Label>
                {t("reportModule.financialYear")}
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select
                onValueChange={setSelectedFinancialYear}
                value={selectedFinancialYear}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={t("reportModule.selectYear")} />
                </SelectTrigger>
                <SelectContent>
                  {financialYears.map((year: any) => (
                    <SelectItem
                      key={year.financial_year_range}
                      value={year.financial_year_range}
                    >
                      {year.financial_year_range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </WhiteContainer>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <ReportCard
            title={t("reportModule.milkatkarReportTitle")}
            description={t("reportModule.milkatkarReportDescription")}
            generationTime="2-3 minutes"
            format="Excel (.xlsx)"
            size="~5MB"
            downloadAction={handleDownloadMilkatKar}
            financialYear={selectedFinancialYear}
          />
          <ReportCard
            title={t("reportModule.varshikkarReportTitle")}
            description={t("reportModule.varshikkarReportDescription")}
            generationTime="2-3 minutes"
            format="Excel (.xlsx)"
            size="~3MB"
            downloadAction={handleDownloadVarshikKar}
            financialYear={selectedFinancialYear}
          />
          {/* <ReportCard
            title={t("reportModule.paidUserReportTitle")}
            description={t("reportModule.paidUserReportDescription")}
            generationTime="1-2 minutes"
            format="Excel (.xlsx)"
            size="~2MB"
            downloadAction={async (financialYear, setProgress) => {
              try {
                const response = await ReportApi.downloadPaidUserReport(financialYear, (progressEvent) => {
                  const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
                  setProgress(percentCompleted);
                });
                if (response.status === 200) {
                  const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `PaidUser_Report_${financialYear}.xlsx`;
                  document.body.appendChild(a);
                  a.click();
                  a.remove();
                  window.URL.revokeObjectURL(url);
                  toast({
                    title: t("reportModule.paidUserReportDownloaded"),
                    variant: "success",
                  });
                }
              } catch (error) {
                console.error("Error downloading Paid User Report:", error);
                toast({
                  title: t("reportModule.paidUserReportFailed"),
                  variant: "destructive",
                });
              }
            }}
            financialYear={selectedFinancialYear}
          /> */}
          {/* <ReportCard
            title={t("reportModule.notPaidUserReportTitle")}
            description={t("reportModule.notPaidUserReportDescription")}
            generationTime="1-2 minutes"
            format="Excel (.xlsx)"
            size="~2MB"
            downloadAction={async (financialYear, setProgress) => {
              try {
                const response = await ReportApi.downloadNotPaidUserReport(financialYear, (progressEvent) => {
                  const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
                  setProgress(percentCompleted);
                });
                if (response.status === 200) {
                  const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `NotPaidUser_Report_${financialYear}.xlsx`;
                  document.body.appendChild(a);
                  a.click();
                  a.remove();
                  window.URL.revokeObjectURL(url);
                  toast({
                    title: t("reportModule.notPaidUserReportDownloaded"),
                    variant: "success",
                  });
                }
              } catch (error) {
                console.error("Error downloading Not Paid User Report:", error);
                toast({
                  title: t("reportModule.notPaidUserReportFailed"),
                  variant: "destructive",
                });
              }
            }}
            financialYear={selectedFinancialYear}
          /> */}
          <ReportCard
            title={t("reportModule.propertyReportTitle")}
            description={t("reportModule.propertyReportDescription")}
            generationTime="3-5 minutes"
            format="Excel (.xlsx)"
            size="~10MB"
            downloadAction={async (financialYear, setProgress) => {
              try {
                const response = await ReportApi.downloadPropertyReport(financialYear, (progressEvent) => {
                  const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
                  setProgress(percentCompleted);
                });
                if (response.status === 200) {
                  const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `Property_Report_${financialYear}.xlsx`;
                  document.body.appendChild(a);
                  a.click();
                  a.remove();
                  window.URL.revokeObjectURL(url);
                  toast({
                    title: t("reportModule.propertyReportDownloaded"),
                    variant: "success",
                  });
                }
              } catch (error) {
                console.error("Error downloading Property Report:", error);
                toast({
                  title: t("reportModule.propertyReportFailed"),
                  variant: "destructive",
                });
              }
            }}
            financialYear={selectedFinancialYear}
          />
        </div>
      </div>
    </div>
  );
};

export default ReportModule;
