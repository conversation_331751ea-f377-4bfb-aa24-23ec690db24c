import React, { useContext } from "react";
import { useTranslation } from "react-i18next";
import WhiteContainer from "../WhiteContainer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Download, ShieldCheck, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import BreadCrumb from "../BreadCrumb";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import PropertyApi from "@/services/PropertyServices";
import { PropertyContext } from "@/context/PropertyContext";
import { GlobalContext } from "@/context/GlobalContext";
import {
  GetSingleRecord,
  PropertyLocationDetails,
  PropertyRegistrationInterface,
  AssessmentDetailsForm,
  PropertyAssessmentDetailsForm,
  PlotDetailsForm,
} from "@/model/propertyregistration-master";
const PropertyView = () => {
  const { t } = useTranslation();
  const { usePropertyDetails } = usePropertyRegistrationController();
 const { setPropertyInformation, setPropertyNumber } = useContext(PropertyContext);
  const { updateProperty, setUpdateProperty, setpropertyId } = useContext(GlobalContext);
  type Checked = DropdownMenuCheckboxItemProps["checked"];
  const navigate = useNavigate();
  const handleNavigate = () => {
    navigate(-1);
  };
  const location = useLocation();
  const propertyId: string = location?.state || "9b21d9a3-8824-430e-bb39-877d7119997c";
  console.log("property id",propertyId)





  function handleEdit(item: any): void {
    PropertyApi.getSingleProperty(
      propertyId, // Use the propertyId from component state
      (response: { status: boolean; data: any }) => {
        if (response.status && response.data.statusCode === 200) {
          console.log("uodate sstatus edit", updateProperty);

          setUpdateProperty(true);
          const responseData: any = response.data.data;
          console.log("response data owner details:---- ", responseData);
          setpropertyId(() => responseData?.property_id);
          // Property Location Details
          const PropertyLocationDetails: PropertyLocationDetails = {
            property_number: responseData?.propertyNumber || "",
            property_old_number: responseData?.old_propertyNumber || "",
            property_master_id: responseData?.property_id || "",
            building_permission_number:
              responseData?.building_permission_number || "",
            city_survey_number: responseData?.city_survey_number || "",
            property_status: responseData?.property_status?.toString() || "",
            plot_number: responseData?.plot_number || "",
            block_number: responseData?.block_number || "",
            house_number: responseData?.house_number || "",
            location: responseData?.location?.location_id || "",
            house_or_apartment_name:
              responseData?.house_or_apartment_name || "",
            street: responseData?.street || null,
            landmark: responseData?.landmark || "",
            country: responseData?.country || "",
            city: responseData?.city || null,
            latitude:
              responseData?.latitude && responseData.latitude !== ""
                ? responseData.latitude
                : null,
            longitude:
              responseData?.longitude && responseData.longitude !== ""
                ? responseData.longitude
                : null,

            ward: responseData?.ward || null,
            zone: responseData?.zone || null,
            adminstrativeBoundary:
              responseData?.adminstrativeBoundary?.adminstrativeBoundary_id ||
              "",
            electionBoundary:
              responseData?.electionBoundary?.electionBoundary_id || "",
            uploaded_files: responseData?.uploaded_files || [],
            sr_no: responseData?.sr_no || "",
            property_desc: responseData?.property_desc || "",
            area: responseData?.area?.area_id || "",
            gat_number: responseData?.gat_no || "",
            property_remark: responseData?.property_remark || "",
          };

          // Assessment Details Form
          const AssessmentDetailsForm: any = {
            property_owner_details: responseData?.property_owner_details?.map(
              (detail: any) => ({
                property_owner_details_id:
                  detail?.property_owner_details_id || "", // Safeguard for ID
                name: detail?.name || "",
                mobile_number: detail?.mobile_number || "",
                email_id: detail?.email_id || "",
                aadhar_number: detail?.aadhar_number || "",
                owner_type: detail?.owner_type?.owner_type_id || "",
                marriage_flag: detail?.marital_status || "no",
                partner_name: detail?.partner_name || "",
                pan_card: detail?.pan_card || "",
                gender: detail?.gender?.toString() || "",
              })
            ),
          };

          // Property Assessment Details Form
          const PropertyAssessmentDetailsForm = {
            property_usage_details:
              responseData?.property_usage_details?.map((detail) => ({
                property_usage_details_id:
                  detail.property_usage_details_id || "",
                construction_area: detail.construction_area || "", // Changed to ""
                length: detail.length || "", // Changed to ""
                width: detail.width || "", // Changed to ""
                are_sq_ft: detail.are_sq_ft || "", // Changed to ""
                are_sq_meter: detail.are_sq_meter || "", // Changed to ""
                construction_end_date: detail.construction_end_date
                  ? new Date(detail.construction_end_date)
                  : null,
                construction_start_date: detail.construction_start_date
                  ? new Date(detail.construction_start_date)
                  : null,
                Building_age: detail.Building_age || "", // Changed to ""
                floor: detail.floorType
                ? {
                    floor_id: detail.floorType.floor_id || "", // Defaults to an empty string if floor_id is missing
                    floor_name: detail.floorType.floor_name || "", // Defaults to an empty string if floor_name is missing
                  }
                : {
                    floor_id: "", // Defaults to an empty string if floor is not provided
                    floor_name: "", // Defaults to an empty string if floor is not provided
                  }, // Changed to ""
                flat_no: detail.flat_no || "", // Changed to ""
                authorized: detail.authorized || false,
                propertyType: {
                  propertyType_id: detail.propertyType?.propertyType_id || "", // Changed to ""
                  propertyType: detail.propertyType?.propertyType || "", // Changed to ""
                },
                usageType: {
                  usage_type_id: detail.usageType?.usage_type_id || "", // Changed to ""
                  usage_type: detail.usageType?.usage_type || "", // Changed to ""
                },
                usageSubType: detail.usageSubType
                  ? {
                      usage_sub_type_master_id:
                        detail.usageSubType.usage_sub_type_master_id || "", // Changed to ""
                      usage_sub_type: detail.usageSubType.usage_sub_type || "", // Changed to ""
                    }
                  : {
                      usage_sub_type_master_id: "", // Changed to ""
                      usage_sub_type: "", // Changed to ""
                    }, // Handle null case for usageSubType
                construction_year: detail.construction_start_year || "",
                room_detail: detail?.tapshil || "",
                remark: detail.remark || "",
                annual_rent: detail.annual_rent || null,
              })) || [], // Default case if property_usage_details is not present
          };

          // Plot Details Form
          const PlotDetailsForm: any = {
            commomDetailId: responseData?.commonFields?.id,
            GISID: responseData?.commonFields?.GISID || "",
            propertyDescription:
              responseData?.commonFields?.propertyDescription || "",
            completionCertificate:
              responseData?.commonFields?.completionCertificate || null,
            accessRoad: responseData?.commonFields?.accessRoad || "",
            individualToilet:
              responseData?.commonFields?.individualToilet || "",
            toiletType: responseData?.commonFields?.toiletType || "",
            totalNumber: Number(responseData?.commonFields?.totalNumber || ""),
            lightingFacility:
              responseData?.commonFields?.lightingFacility || "",
            tapConnection: responseData?.commonFields?.tapConnection || null,
            totalConnections:
              responseData?.commonFields?.totalConnections || "",
            solarProject: responseData?.commonFields?.solarProject || "",
            rainWaterHarvesting:
              responseData?.commonFields?.rainWaterHarvesting || "",
            sewageSystem: responseData?.commonFields?.sewageSystem || "",
            groundFloorArea: Number(
              responseData?.commonFields?.groundFloorArea || ""
            ),
            // remainingGroundFloorArea:
            // responseData?.commonFields?.remainingGroundFloorArea || "",
            remainingGroundFloorArea: Number(
              responseData?.commonFields?.remainingGroundFloorArea || 0
            ),
          };

          // Set the state with updated values
          setPropertyInformation((prevState: any) => ({
            ...prevState,
            PropertyLocationDetails: PropertyLocationDetails,
            AssessmentDetailsForm: AssessmentDetailsForm,
            PropertyAssessmentDetailsForm: PropertyAssessmentDetailsForm,
            PlotDetailsForm: PlotDetailsForm,
          }));

          navigate("/property/property-registration");
        } else {
          console.log("something went wrong");
        }
      }
    );
  }
  const {
    data: propertyDetails,
    isLoading,
    error,
  } = usePropertyDetails(propertyId,"property_id");

  const singlePropertyDetails = propertyDetails?.data;
  const ownerDetails=singlePropertyDetails?.property_owner_details;
  const propertyUsageDetails=singlePropertyDetails?.property_usage_details;

const propertyNumber = singlePropertyDetails?.propertyNumber

  const namumaEightNavigate = () => {
    navigate("/namuna-eight", { state: { propertyNumber } });
  };
  const namumaNineNavigate = () => {
    navigate("/namuna-nine", { state: { propertyNumber } });
  };
 
  const ownerColumn = [
    {
      accessorKey: "index",
      header: `${t("ownerDetails.SrNo")}`,
     cell: ({ row }: { row: any })  => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "name",
      header: `${t("ownerDetails.name")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.name}</div>,
    },
    {
      accessorKey: "owner_type",
      header: `${t("ownerDetails.type")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.owner_type?.owner_type}</div>,
    },
    {
      accessorKey: "mobile_number",
      header: `${t("ownerDetails.mobileNumber")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.mobile_number}</div>,
    },
    {
      accessorKey: "email_id",
      header: `${t("ownerDetails.emailId")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.email_id}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
     cell: ({ row }: { row: any })  => <div>{formatAdharNo(row.original?.aadhar_number)}</div>,
    },
  ];

  const usageColumns = [
    {
      accessorKey: "index",
      header: `${t("propertyDetail.SrNo")}`,
     cell: ({ row }: { row: any })  => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "propertyType",
      header: `${t("propertyDetail.propertyType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.propertyType?.propertyType}</div>,
    },
    {
      accessorKey: "construction_start_year",
      header: `${t("propertyDetail.constructionDate")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.construction_start_year}</div>,
    },
    {
      accessorKey: "usageType",
      header: `${t("propertyDetail.usageType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.usageType?.usage_type}</div>,
    },

        {
      accessorKey: "floorType",
      header: `${t("मजला")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.floorType?.floor_name || ""}</div>,
    },
    {
      accessorKey: "usageSubType",
      header: `${t("propertyDetail.usageSubType")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.usageSubType?.usage_sub_type}</div>,
    },
    {
      accessorKey: "length",
      header: `${t("propertyDetail.length")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.length}</div>,
    },
    {
      accessorKey: "width",
      header: `${t("propertyDetail.width")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.width}</div>,
    },
    {
      accessorKey: "are_sq_ft",
      header: `${t("propertyDetail.areaSqFt")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.are_sq_ft}</div>,
    },
    {
      accessorKey: "are_sq_meter",
      header: `${t("propertyDetail.areaSqMt")}`,
     cell: ({ row }: { row: any })  => <div>{row.original?.are_sq_meter}</div>,
    },
    {
      accessorKey: "authorized",
      header: `${t("propertyDetail.propertyStatus")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.authorized ? "अधिकृत" : "अनधिकृत"}</div>,
    },
  ];
  

  return (
    <>
      <BreadCrumb className={undefined} />
      <div className="flex h-fit ">
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <WhiteContainer>
            <div className="w-full flex-col md:flex-row  flex justify-between items-center">
              <div className=" ">
                <div className="flex w-full ">
                  {/* <ShieldCheck
                    color="green"
                    className="w-8 h-8 cursor-pointer"
                  /> */}
                  {/* <span className="w-20 text-center inline-block px-2 py-2 font-semibold text-sm leading-tight text-green-700 bg-green-100 rounded-2xl">
                    Paid
                  </span> */}
                  <h1 className="text-xl font-semibold font-Poppins w-full ml-3 ">
                    {t("propertyView.propertyDetails")} -{" "}
                    {singlePropertyDetails?.propertyNumber}
                  </h1>
                </div>
              </div>
              <div>
                <Button variant="submit" onClick={handleEdit}>{t("table.edit")}</Button>
                <Button
                  variant="submit"
                  className="ml-4"
                  onClick={namumaEightNavigate}
                >
                  {t("propertyView.namunaEight")}
                </Button>
                <Button
                  variant="submit"
                  className="ml-4"
                  onClick={namumaEightNavigate}
                >
                  {t("bill")}
                </Button>
                <Button
                  variant="submit"
                  className="ml-4"
                  onClick={namumaNineNavigate}
                >
                  {t("propertyView.namunaNine")}
                </Button>
                {/* <DropdownMenu>
                    <Button variant="submit" className="ml-4">{t("table.printReport")}</Button>
                  <DropdownMenuTrigger asChild>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-40">
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem
                    // checked={activePayTime ? true : false}
                    // onCheckedChange={() => {
                    //   setActivePayTime(false);
                    // }}
                    >
                      अहवाल 8
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                    // checked={activePayTime ? true : false}
                    // onCheckedChange={() => {
                    //   setActivePayTime(false);
                    // }}
                    >
                      अहवाल 9
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                    // checked={activePayTime ? true : false}
                    // onCheckedChange={() => {
                    //   setActivePayTime(false);
                    // }}
                    >
                      अहवाल 10
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuContent>
                </DropdownMenu> */}
                {/* <Button
                  className="ml-4"
                  variant="outline"
                  color="primary"
                  onClick={() => handleNavigate()}
                >
                  {t("table.back")}{" "}
                </Button> */}
              </div>
            </div>
          </WhiteContainer>
          <WhiteContainer>
            <div className="flex items-centerjustify-between w-full h-full">
              <h1 className="text-[18px] font-semibold font-Poppins w-full ml-3">
                {t("propertyView.basicInformation")}
              </h1>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mx-3 my-2">
              
                <div>
                  <p className="font-semibold">
                    {t("propertyView.SrNo")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.sr_no}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.citySurveyNumber")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.city_survey_number}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.ward")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.ward?.ward_name}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.zone")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.zone?.zoneName}
                    </span>
                  </p>
                </div>
           
         
                <div>
                  <p className="font-semibold">
                    {t("propertyView.houseName")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.house_or_apartment_name}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.street")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.street?.street_name}
                    </span>
                  </p>
                </div>
  
                <div>
                  <p className="font-semibold">
                    {t("propertyLocationDetailsForm.gatNumber")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.gat_no}
                    </span>
                  </p>
                </div>
  
                <div>
                  <p className="font-semibold">
                    {t("propertyView.GisNumber")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.gis_number}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.latitude")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.latitude}
                    </span>
                  </p>
                </div>
            
{/*            
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotArea")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.plot_area}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotConstructionArea")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.Plot_construction_area}
                    </span>
                  </p>
                </div>
                <div>
                  <p className="font-semibold">
                    {t("propertyView.plotEmptyArea")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.Plot_empty_area}
                    </span>
                  </p>
                </div> */}
                <div>
                  <p className="font-semibold">
                    {t("propertyView.longitude")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.longitude}
                    </span>
                  </p>
                </div>
             
          
                <div>
                  <p className="font-semibold">
                    {t("propertyView.waste_tax")}:{" "}
                    <span className="font-normal">
                      {singlePropertyDetails?.waste_tax}
                    </span>
                  </p>
                </div>
             
            </div>
          </WhiteContainer>

          <WhiteContainer className="overflow-x-auto">
            <Tabs defaultValue="owner" className="w-full ">
              <TabsList className="w-[100%] md:w-fit !h-12">
                <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="owner"
                >
                  {t("propertyView.ownerDetails")}{" "}
                </TabsTrigger>
                <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="property"
                >
                  {t("propertyView.propertyDetails")}{" "}
                </TabsTrigger>
                <TabsTrigger
                  className="w-1/2 p-[10px]  rounded text-[15px] "
                  value="account"
                >
                  {t("propertyView.taxDetails")}{" "}
                </TabsTrigger>
                {/* <TabsTrigger
                  className="w-1/2 p-[10px]  rounded text-[16px] "
                  value="documents"
                >
                  {t("propertyView.documents")}{" "}
                </TabsTrigger> */}
              </TabsList>
              <TabsContent value="owner">
              <TanStackTable columns={ownerColumn} data={ownerDetails || []} />

              </TabsContent>
              <TabsContent value="property">
              <TanStackTable columns={usageColumns} data={propertyUsageDetails || []} />

              </TabsContent>
              <TabsContent value="account">
                <Table className="w-full text-sm text-left text-zinc-500 mt-8 ">
                  <TableHeader className="text-[16px] text-zinc-700 capitalize bg-gray-100">
                    <TableRow>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("taxDetails.date")}{" "}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("taxDetails.taxYear")}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("taxDetails.amount")} {"(₹)"}{" "}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("taxDetails.status")}{" "}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {singlePropertyDetails?.getAllPayments?.data?.map((payment, index) => (
                      <TableRow key={index}>
                        <TableCell className="py-3 px-6">{payment.payment_date}</TableCell>
                        <TableCell className="py-3 px-6">{payment.financial_year}</TableCell>
                        <TableCell className="py-3 px-6">{payment.amount}</TableCell>
                        <TableCell className="py-3 px-6">
                          <span className={`w-28 text-center inline-block px-2 py-1 font-semibold text-sm leading-tight ${payment.payment_status === 'COMPLETED' ? 'text-green-700 bg-green-100' : 'text-red-700 bg-red-100'} rounded-full`}>
                            {payment.payment_status}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
              <TabsContent value="documents">
                <Table className="w-full text-sm text-left text-zinc-500 mt-8">
                  <TableHeader className="text-[16px] text-zinc-700 capitalize bg-gray-100">
                    <TableRow>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("documents.SrNo")}{" "}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("documents.documentName")}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("documents.documentType")}{" "}
                      </TableHead>
                      <TableHead
                        scope="col"
                        className="py-3 px-6 font-semibold"
                      >
                        {t("documents.action")}{" "}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="py-4 px-6">1</TableCell>
                      <TableCell className="py-4 px-6">
                        Report Q1 2024 Sales
                      </TableCell>
                      <TableCell className="py-4 px-6">PDF</TableCell>
                      <TableCell className="py-4 px-6 flex ">
                        {" "}
                        <Download className="w-5 h-5 text-BlueText cursor-pointer" />
                        <Eye className="w-5 h-5 ml-4 cursor-pointer" />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="py-4 px-6">2</TableCell>
                      <TableCell className="py-4 px-6">
                        Project X Proposal
                      </TableCell>
                      <TableCell className="py-4 px-6">PNG</TableCell>
                      <TableCell className="py-4 px-6 flex">
                        {" "}
                        <Download className="w-5 h-5 text-BlueText cursor-pointer" />
                        <Eye className="w-5 h-5 ml-4 cursor-pointer" />
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </WhiteContainer>
        </div>
      </div>
    </>
  );
};

export default PropertyView;