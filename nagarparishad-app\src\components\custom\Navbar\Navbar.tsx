import React from "react";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useFontSize } from "@/context/FontSizeContext";

const Navbar = () => {
  const { t, i18n } = useTranslation();
  const { increaseFontSize, decreaseFontSize, resetFontSize } = useFontSize();

  const languageMap = {
    en: "English",
    mr: "मराठी",
    gj: "Gujarati",
    hi: "Hindi"
  };

  const changeLanguage = (lng) => {
    console.log("Changing language to:", lng);
    i18n.changeLanguage(lng);
    console.log("Current language:", i18n.language);
  };

  return (
    <section className="w-full bg-Primary text-white">
      <div className="max-w-7xl mx-auto flex sm:justify-between justify-end py-3 px-4 sm:px-6 lg:px-8">
        <ul className="sm:flex hidden gap-x-4 h-fit">
          <li className="border-r-2 px-2 py-1 font-normal text-sm">
            <a href="javascript:void(0)"> {t("navbar.downloadApp")}</a>
          </li>
          <li className="border-r-2 pr-4 py-1 text-sm">
            <a href="javascript:void(0)"> {t("navbar.mainTopics")} </a>
          </li>
        </ul>
        <div className="flex sm:justify-between justify-end gap-x-2">
          <div className="hidden md:flex space-x-2 items-center">
            <Button
              className="button w-12 bg-transparent"
              size="sm"
              onClick={decreaseFontSize}
            >
              A-
            </Button>
            <Button
              className="button w-12 bg-transparent"
              size="sm"
              onClick={resetFontSize}
            >
              A
            </Button>
            <Button
              className="button w-12 bg-transparent"
              size="sm"
              onClick={increaseFontSize}
            >
              A+
            </Button>
          </div>
          <Select value={i18n.language} onValueChange={changeLanguage}>
            <SelectTrigger className="border-[#092542] button h-fit mt-0">
              {languageMap[i18n.language] || i18n.language}
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="mr">मराठी</SelectItem>
              {/* <SelectItem value="gj">Gujarati</SelectItem>
              <SelectItem value="hi">Hindi</SelectItem>
              <SelectItem value="en">English</SelectItem> */}
            </SelectContent>
          </Select>
        </div>
      </div>
    </section>
  );
};

export default Navbar;
