import React, { useContext, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Edit, Eye, PlusCircle, Trash } from "lucide-react";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { PROPERTY } from "@/constant/config/api.config";
import { SquarePen } from "lucide-react";
import { Trash2 } from "lucide-react";
import { GlobalContext } from "@/context/GlobalContext";
import { ZoneObject } from "@/model/zone-master";
import { toast } from "@/components/ui/use-toast";
import Api from "@/services/ApiServices";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import AddNewBtn from "@/components/globalcomponent/AddNewForm";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";
import PropertyApi from "@/services/PropertyServices";
import {
  GetSingleRecord,
  PropertyLocationDetails,
  PropertyRegistrationInterface,
  AssessmentDetailsForm,
  PropertyAssessmentDetailsForm,
  PlotDetailsForm,
} from "@/model/propertyregistration-master";
import { PropertyContext } from "@/context/PropertyContext";
import { useNavigate } from "react-router-dom";
import { ResponseData } from "@/model/auth/authServices";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioItem,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { MoreVertical } from "lucide-react";
import { ownerNameFilterFn, wardFilterFn, zoneFilterFn } from "@/components/globalcomponent/TanstackFilter";
import {  usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import SkeletonLoader from "./SkeletonLoader ";

const Propertylisting = () => {
  const { t } = useTranslation();
    const { canPerformAction } = usePermissions();
    const canRead = canPerformAction(ModuleName.Property, FormName.Property,Action.CanRead );
    const canUpdate = canPerformAction(ModuleName.Property, FormName.Property,Action.CanUpdate );
    const CanCreate = canPerformAction(ModuleName.Property, FormName.Property,Action.CanCreate );
    const CanDelete = canPerformAction(ModuleName.Property, FormName.Property,Action.CanDelete );



    console.log("ActionAction",canRead,)

  const { setPropertyInformation, setPropertyNumber } = useContext(PropertyContext);
  const { updateProperty, setUpdateProperty, setpropertyId } = useContext(GlobalContext);
  const navigate = useNavigate();

  const {
    propertyList,
    propertyLoading,
    deleteProperty,
    pagination,
    searchValue,
    setSearchValue,
    searchOn,
    setSearchOn,
  } = usePropertyRegistrationController();
  setUpdateProperty(() => false);

  const dynamicValues = {
    name: t("zone.zoneLabel"),
  };

  const handleSearchKeyChange = (value: string) => {
    setSearchOn(value);
    setSearchValue(""); // Reset search value when changing search field
  };
  const [isActionInProgress, setIsActionInProgress] = useState(false);
  const [processingItemId, setProcessingItemId] = useState<string | null>(null);

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: "sr_no",
      header: t("SrNo"),
      cell: ({ row }) => <div className="capitalize">{row.original.sr_no}</div>,
      enableSorting: true, // Enable sorting for this
    },
    {
      accessorKey: "propertyNumber",
      header: t("property.propertyNumberColumn"),
      cell: ({ row }) => (
        <div className="capitalize ">{row.original.propertyNumber}</div>
      ),
    },
    {
      accessorKey: "old_propertyNumber",
      header: t("property.oldPropertyNumber"),
      cell: ({ row }) => (
        <div className=" min-w-[115px] devanagari-text">
          {row.original.old_propertyNumber} 
        </div>
      ),
    },
    {
      accessorKey: "firstname",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("propertyOwner")}
        </div>
      ),
      cell: ({ row }) => {
        const owners = row.original.property_owner_details;
        const primaryOwners = owners.filter(
          (owner: any) => owner?.owner_type?.owner_type === "स्वत:"
        );

        return (
          <div className="capitalize relative">
            {primaryOwners.length > 0 && (
              <>
                {primaryOwners.slice(0, 1).map((owner, index) => (
                  <p key={owner.property_owner_details_id} className="text-nowrap max-w-[400px] truncate hover:cursor-pointer"
                  title={owner.name}
>
                    {owner.name}
                  </p>
                ))}
                {primaryOwners.length > 1 && (
                  <DropdownMenu>
                    <p className="text-right mb-0">
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-BlueText text-white"
                        >
                          +{primaryOwners.length - 1}
                        </Button>
                      </DropdownMenuTrigger>
                    </p>
                    <DropdownMenuContent className="max-w-[450px] px-3 py-2 text-wrap">
                      {primaryOwners.slice(1).map((owner) => (
                        <DropdownMenuItem key={owner.property_owner_details_id}>
                          {owner.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            )}
          </div>
        );
      },
      filterFn: ownerNameFilterFn,
    },

    {
      accessorKey: "bhogwatdar",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("property.bhogwatadar")}
        </div>
      ),
      cell: ({ row }) => {
        const bhogwatadars = row.original.property_owner_details.filter(
          (owner) => owner?.owner_type?.owner_type !== "स्वत:"
        );

        return (
          <div className="capitalize relative">
            {bhogwatadars.length > 0 && (
              <>
                {bhogwatadars.slice(0, 1).map((owner, index) => {
                  const isBhadekaru =
                    owner?.owner_type?.owner_type === "भाडेकरू";
                  return (
                    <p
                      key={owner.property_owner_details_id}
                      className="text-nowrap max-w-[400px] truncate hover:cursor-pointer"
                      title={owner.name}

                    >
                      {owner.name}
                      {isBhadekaru && <span> (भाडेकरू)</span>}
                    </p>
                  );
                })}
                {bhogwatadars.length > 1 && (
                  <DropdownMenu>
                    <p className="text-right mb-0">
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-BlueText text-white"
                        >
                          +{bhogwatadars.length - 1}
                        </Button>
                      </DropdownMenuTrigger>
                    </p>

                    <DropdownMenuContent className="max-w-[450px] px-3 py-2 text-wrap">
                      {bhogwatadars.slice(1).map((owner) => {
                        const isBhadekaru =
                          owner?.owner_type?.owner_type === "भाडेकरू";
                        return (
                          <DropdownMenuItem
                            key={owner.property_owner_details_id}
                          >
                            {owner.name}
                            {isBhadekaru && <span> (भाडेकरू)</span>}
                          </DropdownMenuItem>
                        );
                      })}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "propertyType",
      header: t("property.propertytype"),
      cell: ({ row }) => {
        const propertyTypes = row.original.property_usage_details.map(
          (usage: { propertyType: { propertyType: any } }) =>
            usage.propertyType?.propertyType
        );
        return (
          <div className="capitalize min-w-[140px] relative">
            {propertyTypes.length > 0 && propertyTypes[0]}
            {propertyTypes.length > 1 && (
              <DropdownMenu>
                <p className="text-right">
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className=" min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-BlueText text-white absolute right-0 bottom-0"
                    >
                      +{propertyTypes.length - 1}
                    </Button>
                  </DropdownMenuTrigger>
                </p>
                <DropdownMenuContent className="max-w-[300px]  px-3 py-2">
                  {propertyTypes.slice(1).map((type, index) => (
                    <DropdownMenuItem key={index}>{type}</DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "property_usage_details",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("property.usageDetails")}
        </div>
      ),
      cell: ({ row }) => {
        const usages = row.original.property_usage_details;
        return (
          <div className="capitalize relative">
            {usages.length > 0 && (
              <>
                {usages[0].usageType?.usage_type}
                {usages.length > 1 && (
                  <DropdownMenu>
                    <p className="text-right">
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className=" min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-BlueText text-white absolute right-0 bottom-0"
                        >
                          +{usages.length - 1}
                        </Button>
                      </DropdownMenuTrigger>
                    </p>
                    <DropdownMenuContent className="max-w-[300px] px-3 py-2">
                      {usages.slice(1).map((usage) => (
                        <DropdownMenuItem key={usage.property_usage_details_id}>
                          {usage.usageType.usage_type}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            )}
          </div>
        );
      },
    },

    {
      accessorKey: "ward_ward_name",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("wardcolumn")}
        </div>
      ),
      cell: ({ row }) => (
        <div className="capitalize"> {row.original?.ward?.ward_name}</div>
      ),
      filterFn: wardFilterFn,
    },
    {
      accessorKey: "zone_zoneName",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("zonecolumn")}
        </div>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.original?.zone?.zoneName}</div>
      ),
      filterFn: zoneFilterFn,
    },
    {
      accessorKey: "mobile_number",
      header: ({ column }) => (
        <div className="px-0 justify-start text-base font-semibold">
          {t("Mobile")}
        </div>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.original.mobile_number}</div>
      ),
    },

    {
      accessorKey: "Actions",
      header: t("action"),
      enableHiding: false,
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button  className="h-8 w-8 p-0 justify-start bg-none"
                      disabled={isActionInProgress && processingItemId === row?.original?.property_id}>
              <MoreVertical />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-fit px-3 py-2">
            <DropdownMenuRadioGroup>
              {canRead && (
                <DropdownMenuRadioItem value="view" className="cursor-pointer px-0">
                  <Button
                    variant="submit"
                    className="justify-center w-full !border-BlueText !text-white h-8"
                    onClick={() => handleView(row?.original)}
                    disabled={isActionInProgress}
                  >
                    {isActionInProgress && processingItemId === row?.original?.property_id ? 
                      "Processing..." : t("table.view")}
                  </Button>
                </DropdownMenuRadioItem>
              )}
              {canUpdate && (
                <DropdownMenuRadioItem value="edit" className="cursor-pointer px-0">
                  <Button
                    variant="submit"
                    className="justify-start w-full h-8"
                    onClick={() => handleEdit(row?.original)}
                    disabled={isActionInProgress}
                  >
                    {isActionInProgress && processingItemId === row?.original?.property_id ? 
                      "Processing..." : t("table.edit")}
                  </Button>
                </DropdownMenuRadioItem>
              )}
              {CanDelete && (
                <DropdownMenuRadioItem value="delete" className="cursor-pointer px-0">
                  <Button
                    variant="outline"
                    className="justify-center w-full !border-red-700 !text-red-600 h-8"
                    onClick={() => handleDelete(row?.original)}
                    disabled={isActionInProgress}
                  >
                    {isActionInProgress && processingItemId === row?.original?.property_id ? 
                      "Processing..." : t("table.delete")}
                  </Button>
                </DropdownMenuRadioItem>
              )}
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    }
    
  ];

  const handleView = async (item: any) => {
    // Prevent multiple clicks on the same or different items while processing
    if (isActionInProgress) return;
    
    try {
      setIsActionInProgress(true);
      setProcessingItemId(item?.property_id);
      
      // Navigate to the view page
      navigate("/property/property-view", { state: item?.property_id });
    } finally {
      // Reset the state after a short delay to prevent accidental double-clicks
      setTimeout(() => {
        setIsActionInProgress(false);
        setProcessingItemId(null);
      }, 500);
    }
  };

  function handleEdit(item: any): void {
    if (isActionInProgress) return;
    
    try {
      setIsActionInProgress(true);
      setProcessingItemId(item?.property_id);
      
      PropertyApi.getSingleProperty(
        item.property_id,
        (response: { status: boolean; data: any }) => {
          if (response.status && response.data.statusCode === 200) {
            console.log("uodate sstatus edit", updateProperty);

            setUpdateProperty(true);
            const responseData: any = response.data.data;
            console.log("response data owner details:---- ", responseData);
            setpropertyId(() => responseData?.property_id);
            // Property Location Details
            const PropertyLocationDetails: PropertyLocationDetails = {
              property_number: responseData?.propertyNumber || "",
              property_old_number: responseData?.old_propertyNumber || "",
              property_master_id: responseData?.property_id || "",
              building_permission_number:
                responseData?.building_permission_number || "",
              city_survey_number: responseData?.city_survey_number || "",
              property_status: responseData?.property_status?.toString() || "",
              plot_number: responseData?.plot_number || "",
              block_number: responseData?.block_number || "",
              house_number: responseData?.house_number || "",
              location: responseData?.location?.location_id || "",
              house_or_apartment_name:
                responseData?.house_or_apartment_name || "",
              street: responseData?.street || null,
              landmark: responseData?.landmark || "",
              country: responseData?.country || "",
              city: responseData?.city || null,
              latitude:
                responseData?.latitude && responseData.latitude !== ""
                  ? responseData.latitude
                  : null,
              longitude:
                responseData?.longitude && responseData.longitude !== ""
                  ? responseData.longitude
                  : null,

              ward: responseData?.ward || null,
              zone: responseData?.zone || null,
              adminstrativeBoundary:
                responseData?.adminstrativeBoundary?.adminstrativeBoundary_id ||
                "",
              electionBoundary:
                responseData?.electionBoundary?.electionBoundary_id || "",
              uploaded_files: responseData?.uploaded_files || [],
              sr_no: responseData?.sr_no || "",
              property_desc: responseData?.property_desc || "",
              area: responseData?.area?.area_id || "",
              gat_number: responseData?.gat_no || "",
              property_remark: responseData?.property_remark || "",
            };

            // Assessment Details Form
            const AssessmentDetailsForm: any = {
              property_owner_details: responseData?.property_owner_details?.map(
                (detail: any) => ({
                  property_owner_details_id:
                    detail?.property_owner_details_id || "", // Safeguard for ID
                  name: detail?.name || "",
                  mobile_number: detail?.mobile_number || "",
                  email_id: detail?.email_id || "",
                  aadhar_number: detail?.aadhar_number || "",
                  owner_type: detail?.owner_type?.owner_type_id || "",
                  marriage_flag: detail?.marital_status || "no",
                  partner_name: detail?.partner_name || "",
                  pan_card: detail?.pan_card || "",
                  gender: detail?.gender?.toString() || "",
                })
              ),
            };

            // Property Assessment Details Form
            const PropertyAssessmentDetailsForm = {
              property_usage_details:
                responseData?.property_usage_details?.map((detail) => ({
                  property_usage_details_id:
                    detail.property_usage_details_id || "",
                  construction_area: detail.construction_area || "", // Changed to ""
                  length: detail.length || "", // Changed to ""
                  width: detail.width || "", // Changed to ""
                  are_sq_ft: detail.are_sq_ft || "", // Changed to ""
                  are_sq_meter: detail.are_sq_meter || "", // Changed to ""
                  construction_end_date: detail.construction_end_date
                    ? new Date(detail.construction_end_date)
                    : null,
                  construction_start_date: detail.construction_start_date
                    ? new Date(detail.construction_start_date)
                    : null,
                  Building_age: detail.Building_age || "", // Changed to ""
                  floor: detail.floorType
                  ? {
                      floor_id: detail.floorType.floor_id || "", // Defaults to an empty string if floor_id is missing
                      floor_name: detail.floorType.floor_name || "", // Defaults to an empty string if floor_name is missing
                    }
                  : {
                      floor_id: "", // Defaults to an empty string if floor is not provided
                      floor_name: "", // Defaults to an empty string if floor is not provided
                    }, // Changed to ""
                  flat_no: detail.flat_no || "", // Changed to ""
                  authorized: detail.authorized || false,
                  propertyType: {
                    propertyType_id: detail.propertyType?.propertyType_id || "", // Changed to ""
                    propertyType: detail.propertyType?.propertyType || "", // Changed to ""
                  },
                  usageType: {
                    usage_type_id: detail.usageType?.usage_type_id || "", // Changed to ""
                    usage_type: detail.usageType?.usage_type || "", // Changed to ""
                  },
                  usageSubType: detail.usageSubType
                    ? {
                        usage_sub_type_master_id:
                          detail.usageSubType.usage_sub_type_master_id || "", // Changed to ""
                        usage_sub_type: detail.usageSubType.usage_sub_type || "", // Changed to ""
                      }
                    : {
                        usage_sub_type_master_id: "", // Changed to ""
                        usage_sub_type: "", // Changed to ""
                      }, // Handle null case for usageSubType
                  construction_year: detail.construction_start_year || "",
                  room_detail: detail?.tapshil || "",
                  remark: detail.remark || "",
                  annual_rent: detail.annual_rent || null,
                })) || [], // Default case if property_usage_details is not present
            };

            // Plot Details Form
            const PlotDetailsForm: any = {
              commomDetailId: responseData?.commonFields?.id,
              GISID: responseData?.commonFields?.GISID || "",
              propertyDescription:
                responseData?.commonFields?.propertyDescription || "",
              completionCertificate:
                responseData?.commonFields?.completionCertificate || null,
              accessRoad: responseData?.commonFields?.accessRoad || "",
              individualToilet:
                responseData?.commonFields?.individualToilet || "",
              toiletType: responseData?.commonFields?.toiletType || "",
              totalNumber: Number(responseData?.commonFields?.totalNumber || ""),
              lightingFacility:
                responseData?.commonFields?.lightingFacility || "",
              tapConnection: responseData?.commonFields?.tapConnection || null,
              totalConnections:
                responseData?.commonFields?.totalConnections || "",
              solarProject: responseData?.commonFields?.solarProject || "",
              rainWaterHarvesting:
                responseData?.commonFields?.rainWaterHarvesting || "",
              sewageSystem: responseData?.commonFields?.sewageSystem || "",
              groundFloorArea: Number(
                responseData?.commonFields?.groundFloorArea || ""
              ),
              // remainingGroundFloorArea:
              // responseData?.commonFields?.remainingGroundFloorArea || "",
              remainingGroundFloorArea: Number(
                responseData?.commonFields?.remainingGroundFloorArea || 0
              ),
            };

            // Set the state with updated values
            setPropertyInformation((prevState: any) => ({
              ...prevState,
              PropertyLocationDetails: PropertyLocationDetails,
              AssessmentDetailsForm: AssessmentDetailsForm,
              PropertyAssessmentDetailsForm: PropertyAssessmentDetailsForm,
              PlotDetailsForm: PlotDetailsForm,
            }));

            navigate("/property/property-registration");
          } else {
            console.log("something went wrong");
          }
        }
      );
    } finally {
      setTimeout(() => {
        setIsActionInProgress(false);
        setProcessingItemId(null);
      }, 500);
    }
  }

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  async function handleDelete(item: any): Promise<void> {
    if (isActionInProgress) return;
    
    try {
      setIsActionInProgress(true);
      setProcessingItemId(item?.property_id);
      
      setSelectedItem(item);
      setIsDeleteOpen(true);
    } finally {
      setTimeout(() => {
        setIsActionInProgress(false);
        setProcessingItemId(null);
      }, 500);
    }
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      console.log("sleteditemmm",selectedItem)
      deleteProperty(selectedItem.property_id, {
        onSuccess: (response: ResponseData) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: t("api.delete"),
              variant: "success",
            });
          } else {
            console.log("error hh", error);

            toast({
              title: response?.message,
              variant: "destructive",
            });
          }
        },
        onError: (error) => {
          console.log("error", error);
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const MasterType: string = PROPERTY.PROPERTY;
  const handleUpdateStatus = () => {
    setUpdateProperty(() => false);
  };

  return (
    <>
      <div className="flex h-fit ">
        <div className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  ">
          <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
            {t("propertyListing")}
          </h1>

          {MasterType && (
            <AddNewBtn
              masterType={PROPERTY.PROPERTY}
              handleClick={handleUpdateStatus}
            />
          )}
         <WhiteContainer>
       
              <TanStackTable
                columns={columns}
                data={propertyList}
                masterType={PROPERTY.PROPERTY}
                searchKey={"property.propertyNumberColumn"}
                searchColumn={"firstname"}
                btnVariant={"submit"}
                searchColumnArray={[
                  "property_number",
                  "owner_details_name",
                  "old_property_number",
                  "ward_name",
                  "mobile_no",
                  "zone_name"
                ]}
                loader={propertyLoading ? true : false}
                serverPagination={{
                  currentPage: pagination.page,
                  pageSize: pagination.limit,
                  totalPages: pagination.totalPages,
                  totalRecords: pagination.totalRecords,
                  onPageChange: pagination.setPage,
                  onPageSizeChange: pagination.setLimit,
                }}
                onSearch={(value: string) => setSearchValue(value)} // Pass search value to controller
                onSearchFieldChange={handleSearchKeyChange} // Pass search field change to controller
                selectedSearchField={searchOn} // Pass current search field
              />
            
          </WhiteContainer>
        </div>
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default Propertylisting;
