import { t } from "i18next";
import React, { useState, useRef } from "react";
import Webcam from "react-webcam";
import { Button } from "../ui/button";
import { CircleX, Eye, Webcam as LucideWebcam, Trash2 } from "lucide-react";
import { Camera } from "lucide-react";
import ImageServices from "@/services/ImageServices";

interface WebcamCaptureProps {
  onCapture?: (photo: string) => void;
}

const WebcamCapture: React.FC<WebcamCaptureProps> = ({ onCapture }) => {
  const [isWebcamOpen, setIsWebcamOpen] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const webcamRef = useRef<Webcam>(null);

  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);

  const openWebcam = () => setIsWebcamOpen(true);
  const closeWebcam = () => setIsWebcamOpen(false);

  const capturePhoto = () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      setCapturedPhoto(imageSrc);
      handleCapture(imageSrc as string);
      setIsWebcamOpen(false);
    }
  };

  const handleCapture = (photo: string) => {
    setCapturedPhotos((prev) => [...prev, photo]);
  };

  const viewImage = async (imageSrc: string) => {
    try {
      await ImageServices.viewImage(imageSrc);
    } catch (error) {
      console.error("Error viewing image:", error);
    }
  };

  return (
    <div className="webcam-capture relative">
      {!isWebcamOpen && (
        <>
          <div className="2xl:w-[90%] w-full h-fit py-5 border rounded-20 mt-2 flex flex-col justify-center items-center">
            <div>
              {/* <LucideWebcam className="w-7 h-7 m-auto text-BlueText" /> */}
              <Camera className="w-9 h-9 m-auto text-BlueText" />
              <h2 className="text-center mt-2 text-lg font-medium text-[#222222]">
                Click to Capture
              </h2>
              <Button
                className="mt-1 px-4 py-5 my-3 w-full"
                type="button"
                onClick={openWebcam}
              >
                {/* <LucideWebcam className="w-6 h-6 mr-2" /> */}
                {t("openWebcam")}{" "}
              </Button>
            </div>
            <hr className="w-full my-3" />
            <div className="w-full mt-2 px-5">
              {capturedPhotos.length > 0 ? (
                <div>
                  <h3 className="text-sm  mb-2">{t("uploadedFiles")}</h3>

                  <div className="mt-1 flex  gap-2 w-full overflow-x-auto">
                    {capturedPhotos?.map((photo, index) => (
                      <div
                        key={index}
                        className="relative min-w-[145px] mb-2 rounded-xl overflow-hidden"
                      >
                        <img
                          src={photo}
                          alt={`Captured ${index}`}
                          className="w-full h-32 object-cover border border-gray-300 "
                        />
                        <div className="bg-slate-200/60 w-full h-full absolute top-0 left-0 flex items-center justify-center gap-3 opacity-0 hover:opacity-100 transition-opacity duration-500">
                          <button
                            onClick={() => viewImage(photo)}
                            className="text-gray-600 bg-gray-300 p-2 rounded-full h-fit "
                          >
                            <Eye />
                          </button>
                          <button
                            onClick={() => {
                              setCapturedPhotos((prev) =>
                                prev.filter((p) => p !== photo)
                              );
                            }}
                            className="text-red-400 bg-red-200 p-2 rounded-full h-fit"
                          >
                            <Trash2 />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-center">No files uploaded.</p>
              )}
            </div>
          </div>
        </>
      )}

      {isWebcamOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="flex flex-col items-center justify-center  p-4 bg-white rounded-lg">
            <Webcam
              audio={false}
              ref={webcamRef}
              screenshotFormat="image/jpeg"
              className="w-full"
            />
            <div className="mt-2 flex space-x-4">
              <button
                type="button"
                className="px-4 py-2 bg-blue-500 text-white rounded"
                onClick={capturePhoto}
              >
                {t("takePhoto")}
              </button>
              <button
                type="button"
                className="px-4 py-2 bg-gray-500 text-white rounded"
                onClick={closeWebcam}
              >
                {t("cancel")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebcamCapture;
