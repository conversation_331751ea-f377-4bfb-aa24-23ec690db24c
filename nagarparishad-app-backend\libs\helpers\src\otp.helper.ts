import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { randomBytes } from 'crypto';
import { UserOtpRepository } from 'libs/database/repositories';
import { OTP_STATUS } from './constants';
import { AuthHelper } from './auth.helper';

@Injectable()
export class OtpHelperService {
  constructor(private readonly otpRepository: UserOtpRepository, private readonly authHelper: AuthHelper) {}

  async generateOtp(user: string): Promise<string> {
    const otpLength = 6;
    const digits = '0123456789';
    const buffer = randomBytes(otpLength);
    let otp = '';
    for (let i = 0; i < otpLength; i++) {
      otp += digits.charAt(buffer.readUInt8(i) % digits.length);
    }
    const expiresAt = new Date(Date.now() + 60 * 1000);

    const userOtp = {
      user,
      otp,
      expiresAt,
    };

    await this.otpRepository.saveData(userOtp);
    return otp;
  }


  async generateOtpForFiveMin(user: string): Promise<string> {
    const otpLength = 6;
    const digits = '0123456789';
    const buffer = randomBytes(otpLength);
    let otp = '';

    for (let i = 0; i < otpLength; i++) {
        otp += digits.charAt(buffer.readUInt8(i) % digits.length);
    }

    // Set expiration time to 6 minutes (360,000 milliseconds)
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000);

    const userOtp = {
        user,
        otp,
        expiresAt,
    };

    await this.otpRepository.saveData(userOtp);
    return otp;
}


  async validateOtp(user: string, otp: string) {
    try {
      const userOtp = await this.otpRepository.findById(user, otp);

      if (!userOtp) {
        throw new UnauthorizedException('Invalid OTP');
      }
    } catch (error) {
      throw error;
    }
  }

  async generateOtpV2(payload: any){
        
    const otpLength = 6;
    const digits = '0123456789';
    const buffer = randomBytes(otpLength);
    let otp = '';
    for (let i = 0; i < otpLength; i++) {
      otp += digits.charAt(buffer.readUInt8(i) % digits.length);
    }
    const expiresAt = new Date(Date.now() + 60 * 1000);

    //Hash the otp


    const data = {
      mobile_number: payload.mobile_number,
      additionalInfo: payload.key_value,
      otp,
      status: OTP_STATUS.OTP_SENT,
      expiresAt,
      verificationId: payload.verificationId
    }

    await this.otpRepository.saveData(data);

    return otp;
    
  }

  async validateotpV2(validateOtp: {verificationId: string, otp: string})
  {
    const getOtp = await this.otpRepository.getOtpForVerificationId(validateOtp.verificationId, validateOtp.otp); 

    if (!getOtp) {
      throw new UnauthorizedException('Invalid OTP');
    }
    //Emit a event to mark the otp has Verified & soft Delete
    return getOtp;
  }

  // @Cron('0 0 0 * * *') // Runs at midnight every day
  // @Cron('*/30 * * * * *')   Runs at every 30 seconds
  // async deleteExpiredOtp() {
  //   try {
  //     await this.otpRepository
  //       .deleteExiperedOtp()
  //       .then(() => {
  //         Logger.log('Expired OTP deleted');
  //       })
  //       .catch(() => {
  //         Logger.log('Failed To delete');
  //       });
  //   } catch (error) {
  //     throw error;
  //   }
  // }
}
