import { Input } from "@/components/ui/input";
import React, { useContext, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/globalcomponent/Loader";
import Property<PERSON><PERSON> from "@/services/PropertyServices";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { GlobalContext } from "@/context/GlobalContext";
import { toast } from "@/components/ui/use-toast";
import AdvancedSearch from "./AdvancedSeacrh";
import { Eye, Copy, Search, Phone, SearchX } from "lucide-react";
import { CopyToClipboard } from "react-copy-to-clipboard";

import {
  AlignJustify,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  X,
} from "lucide-react";
import NotificationBell from "@/components/notifications/NotificationBell";
import NotificationSidebar from "@/components/notifications/NotificationSidebar";
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { UserData } from "@/model/global-master";
import Ctrlk from "@/assets/img/global/CTRLK.png";
import Api from "@/services/ApiServices";

interface HeaderProps {
  toggle?: () => void;
}

const DashBoardHeader: React.FC<HeaderProps> = ({ toggle }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { setSearchResults } = useContext(GlobalContext);

  const [inputValue, setInputValue] = useState("");
  const [isModalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fetchedResults, setFetchedResults] = useState([]);
  const [searchError, setSearchError] = useState<string | null>(null);
  const debounceTimeoutRef = useRef(null);
  const [showAllData, setShowAllData] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [limit] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [dataFetched, setDataFetched] = useState(false);

  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
const [showHamburger, setShowHamburger] = useState(false);



  const sendToBackend = async (value, page = 1) => {
    let queryParam;

    if (value.startsWith("SNP")) {
      queryParam = `propertyNumber=${value}`;
    } else if (/^\d+$/.test(value)) {
      queryParam = `value=${value}`;
    } else {
      queryParam = `name=${encodeURIComponent(value)}`;
    }

    try {
      const response = await Api.globalSearch(queryParam, page, limit);

      if (response.status === 200 && response.data) {
        setFetchedResults(response.data.data.data);
        setTotalRecords(response.data.data.totalItems);
        setDataFetched(true);

        if (response.data.data.data.length === 0) {
          setSearchError(t("कोणतेही रेकॉर्ड्स नाहीत!"));
        }
      } else {
        setSearchError(t("Unexpected response from server"));
      }
    } catch (error) {
      setDataFetched(false);
      if (error.response) {
        setSearchError(
          t(
            `Server Error: ${error.response.status} - ${error.response.data.message}`
          )
        );
      } else if (error.request) {
        setSearchError(t("Network Error: Please check your connection."));
      } else {
        setSearchError(t("Error occurred: ") + error.message);
      }
      console.error("Error:", error);
    }
  };

  const handleInputChange = (event) => {
    let value = event.target.value.trim();

    if (value.startsWith("snp" || "SNP")) {
      value = value.toUpperCase();
    }

    setInputValue(value);
    setSearchError(null);
    setModalVisible(!!value);
    setPageNumber(1);
    setActiveIndex(0);
    setDataFetched(false);

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setLoading(true);
      sendToBackend(value).finally(() => setLoading(false));
    }, 1000);
  };

  const handlePageChange = (increment: number) => {
    const newPage = pageNumber + increment;
    if (newPage > 0) {
      setPageNumber(newPage);
      setLoading(true);
      sendToBackend(inputValue, newPage).finally(() => setLoading(false));
    }
  };

  const openDialog = () => {
  setIsOpen(true);
  setShowHamburger(true);
};

const handleClose = () => {
  setIsOpen(false);
  setShowHamburger(false);
    setInputValue("");
  setFetchedResults([]);
  setSearchError(null);
  setPageNumber(1);
  setActiveIndex(0);
  setDataFetched(false);
};

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleViewProperty = (item: any) => {
    navigate("/property/property-view", { state: item?.property_id });
    setModalVisible(false);
    setInputValue("");
    setIsOpen(false);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const [login, setLogin] = useState({
    name: "अमित देसाई",
    logo: "path/to/logo.png",
  });

  const [notificationSidebarOpen, setNotificationSidebarOpen] = useState(false);

  const userData: any = localStorage.getItem("UserData") as any;

  useEffect(() => {
    try {
      if (userData) {
        const userData1: any = JSON.parse(userData);
        setLogin({
          name: userData1.firstName + " " + userData1.lastName,
          logo: "path/to/logo.png",
        });
      } else {
        console.warn("No user data found in localStorage");
      }
    } catch (err) {
      console.error("Error parsing user data: ", err);
    }
  }, [userData]);

  function handleViewAll() {
    setShowAllData(!showAllData);
    setModalVisible(false);
    sendToBackend(inputValue, 1);
    setIsOpen(false);
  }

  const handleToggle = () => {
    setShowAllData((prev) => !prev);
    setInputValue("");
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.ctrlKey && event.key === "k") {
        event.preventDefault();
        openDialog();
      }
      if (event.key === "Escape") {
        handleClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "ArrowDown" && fetchedResults.length > 0) {
        setActiveIndex((prevIndex) => {
          const newIndex = (prevIndex + 1) % fetchedResults.length;

          if (dropdownRef.current) {
            if (newIndex >= 4) {
              dropdownRef.current.scrollTop += 90;
            } else {
              dropdownRef.current.scrollTop = 0;
            }
          }

          return newIndex;
        });
      } else if (event.key === "ArrowUp" && fetchedResults.length > 0) {
        setActiveIndex((prevIndex) => {
          const newIndex =
            prevIndex === 0 ? fetchedResults.length - 1 : prevIndex - 1;

          if (dropdownRef.current) {
            if (newIndex > 5) {
              dropdownRef.current.scrollTop = dropdownRef.current.scrollHeight;
            } else if (newIndex <= 5) {
              dropdownRef.current.scrollTop -= 90;
            }
          }

          return newIndex;
        });
      } else if (
        event.key === "Enter" &&
        activeIndex >= 0 &&
        fetchedResults.length > 0 &&
        dataFetched
      ) {
        handleViewProperty(fetchedResults[activeIndex]);
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, fetchedResults, activeIndex, dataFetched]);

  const handleResultClick = (result) => {
    setInputValue(result.name);
    setModalVisible(false);
  };

  const handleCopy = (number) => {
    navigator.clipboard.writeText(number);

    toast({
      title: "Copied",
      variant: "success",
    });
  };

  return (
    <div className="w-full border flex  justify-between items-center gap-3 px-3 sm:px-6 py-[0.8rem]  flex-nowrap pr-6">
      {/* Left side with menu toggle and search */}
      <div className="relative flex items-center w-full sm:w-auto">
        <div className="flex items-center gap-2">
          <button
            onClick={toggle}
            className="block lg:hidden"
            aria-label="Toggle menu"
          >
            <AlignJustify className="h-5 w-5" />
          </button>

          {/* Search input - responsive width */}
          <div className="relative w-full sm:w-[200px] md:w-[250px] lg:w-[300px] xl:w-[350px] sm:block">
            <Input
              className="rounded-full text-sm focus:ring-blue-500 focus:border-blue-500 border-Graphite shadow-sm w-full bg-white pl-11"
              placeholder={t("मालमत्ता शोधा")}
              onClick={openDialog}
              type="text"
            />
            <img
              className="w-10 h-4 absolute right-3 top-3 object-fill"
              src={Ctrlk}
              alt="Ctrl+K"
            />
            <div className="absolute inset-y-0 left-3 pr-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-zinc-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>{" "}
      </div>

      {/* Right side with notifications and user info */}
 <div className="flex items-center gap-x-2 sm:gap-x-3 text-nowrap px-1 sm:px-2">
  {/* Notification Bell */}
  <NotificationBell  handleSidebarOpen={() => setNotificationSidebarOpen(true)} />

  <div className="flex items-center space-x-2">
    <img
      src="https://storage.googleapis.com/semilynxdev/user/67171709559781980.jpeg"
      alt="User Avatar"
      className="rounded-full w-8 h-8 sm:w-10 sm:h-10"
    />
    <p className="text-zinc-800 dark:text-white font-medium capitalize text-sm sm:text-base hidden sm:inline">
      {login.name}
    </p>
  </div>
</div>



      {showAllData && (
        <>
          <div className="fixed inset-0 bg-black opacity-40 z-40"></div>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white p-3 sm:p-5 rounded-lg sm:rounded-20 shadow-md w-[95%] sm:w-fit sm:min-w-[40rem] md:min-w-[50rem] max-w-full max-h-[95vh] overflow-y-auto">
              <div className="flex justify-between">
                <p className="text-lg font-semibold">{t("All Results")}</p>
                <X className="h-5 w-5 cursor-pointer" onClick={handleToggle} />
              </div>
              <hr className="my-2" />
              <div className="overflow-x-auto">
                <Table className="min-w-full mt-4 border border-gray-300">
                <TableHeader className="bg-gray-100">
                  <TableRow>
                    <TableHead>{t("Index")}</TableHead>
                    <TableHead>{t("Property Number")}</TableHead>
                    <TableHead>{t("Old Property Number")}</TableHead>
                    <TableHead>{t("Owner Name")}</TableHead>
                    <TableHead>{t("Zone")}</TableHead>
                    <TableHead>{t("Ward")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fetchedResults.map((result, index) => (
                    <TableRow 
                      key={result.property_id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleViewProperty(result)}
                    >
                      <TableCell>
                        {(pageNumber - 1) * limit + index + 1}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="mr-1">{result.propertyNumber || t("No Property Number")}</span>
                          <button 
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCopy(result.propertyNumber);
                            }}
                            className="text-blue-500 hover:text-blue-700"
                          >
                            <Copy className="h-3 w-3" />
                          </button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="mr-1">{result.old_propertyNumber || t("N/A")}</span>
                          {result.old_propertyNumber && (
                            <button 
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCopy(result.old_propertyNumber);
                              }}
                              className="text-blue-500 hover:text-blue-700"
                            >
                              <Copy className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {result.property_owner_details[0]?.name || t("No Owner")}
                      </TableCell>
                      <TableCell>
                        {result.zone?.zoneName || t("No Zone")}
                      </TableCell>
                      <TableCell>
                        {result.ward?.ward_name || t("No Ward")}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </div>

              <div className="flex flex-col sm:flex-row justify-between mt-3 gap-2">
                <div className="flex w-fit items-center justify-center text-sm font-medium">
                  Page {pageNumber} of {Math.ceil(totalRecords / limit)}
                </div>
                <div className="pagination-controls flex justify-center sm:justify-end gap-2 sm:gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pageNumber <= 1}
                    onClick={() => handlePageChange(-1)}
                  >
                    <ChevronLeft /> {t("Previous")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pageNumber * limit >= totalRecords}
                    onClick={() => handlePageChange(1)}
                  >
                    {t("Next")} <ChevronRight />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[550px] max-w-[95%] bg-white px-0 py-4 gap-1">
          <DialogHeader className="px-6">
            <DialogTitle className="text-lg sm:text-xl font-semibold">मालमत्ता शोधा</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm text-gray-500">
              मालमत्ता क्रमांक, मालक नाव किंवा मोबाईल क्रमांक टाका
            </DialogDescription>
          </DialogHeader>

          <div className="px-4 sm:px-6 pt-2 relative">
            <div className="relative">
              <Input
                className="rounded-lg text-sm sm:text-base focus-visible:ring-2 focus-visible:ring-blue-500 border-gray-300 shadow-sm w-full bg-white pl-9 sm:pl-11 pr-4 py-5 sm:py-6"
                placeholder={t("मालमत्ता क्रमांक, मालक नाव किंवा मोबाईल क्रमांक")}
                value={inputValue}
                ref={inputRef}
                onChange={handleInputChange}
                type="text"
              />
              <div className="absolute inset-y-0 left-2 sm:left-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
              </div>
              {inputValue && (
                <button 
                  onClick={() => setInputValue('')}
                  className="absolute inset-y-0 right-2 sm:right-3 flex items-center"
                >
                  <X className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>

            {loading && (
              <div className="flex justify-center items-center py-6 sm:py-8">
                <Loader className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-blue-500" />
              </div>
            )}
{isModalVisible && inputValue && !loading && (
  <div className="mt-2">
    {fetchedResults.length > 0 ? (
      <div
        className="w-full bg-white rounded-lg border border-gray-200 shadow-lg max-h-[250px] sm:max-h-[350px] overflow-y-auto"
        ref={dropdownRef}
      >
        {fetchedResults.map((result, index) => (
          <div
            key={result.id}
            className={`p-2 sm:p-3 border-b last:border-b-0 hover:bg-gray-50 cursor-pointer transition-all ${
              index === activeIndex ? "bg-blue-50" : ""
            }`}
            onMouseEnter={() => setActiveIndex(index)}
            onClick={() => handleViewProperty(result)}
          >
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
              <div className="flex-1">
                {result.property_owner_details?.map((owner, idx) => (
                  <div key={idx} className="mb-1">
                    <p className="font-medium text-gray-900 text-sm sm:text-base">
                      {owner.name}
                    </p>
                    <div className="flex items-center text-xs sm:text-sm text-gray-600 mt-1">
                      <Phone className="h-3 w-3 mr-1" />
                      <span className="truncate max-w-[150px] sm:max-w-none">{owner.mobile_number}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="flex flex-col items-start sm:items-end gap-1">
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                    {result.propertyNumber || "N/A"}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopy(result.propertyNumber);
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <Copy className="h-3 w-3" />
                  </button>
                </div>
                {result.old_propertyNumber && (
                  <div className="flex items-center">
                    <span className="text-xs text-gray-500">
                      जुना क्र.: {result.old_propertyNumber}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCopy(result.old_propertyNumber);
                      }}
                      className="ml-1 text-blue-500 hover:text-blue-700"
                    >
                      <Copy className="h-3 w-3" />
                    </button>
                  </div>
                )}
                <span className="text-xs text-gray-500">
                  प्रभाग: {result.ward?.ward_name}
                </span>
              </div>
            </div>
          </div>
        ))}

        {/* Conditionally render the "View All" button */}
        {fetchedResults.length <= 10 && fetchedResults.length >= 10 && (
          <div className="p-2 sm:p-3 border-t bg-gray-50">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs sm:text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 py-1 sm:py-2"
              onClick={handleViewAll}
            >
              सर्व परिणाम पहा ({totalRecords})
            </Button>
          </div>
        )}
      </div>
    ) : (
      <div className="text-center py-6 sm:py-8 bg-gray-50 rounded-lg border border-gray-200">
        <SearchX className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2" />
        <p className="text-sm sm:text-base text-gray-600">कोणतेही परिणाम सापडले नाहीत</p>
        <p className="text-xs sm:text-sm text-gray-500 mt-1">कृपया वेगळे शब्द वापरून पुन्हा प्रयत्न करा</p>
      </div>
    )}
  </div>
)}

          </div>

          <DialogFooter className="px-4 sm:px-6 pt-2">
            <div className="flex flex-wrap justify-center sm:justify-between items-center w-full text-xs text-gray-500 gap-y-2">
              <div className="flex items-center">
                <kbd className="px-1.5 py-0.5 sm:px-2 sm:py-1 bg-gray-100 border border-gray-300 rounded-md text-xs">↑</kbd>
                <kbd className="px-1.5 py-0.5 sm:px-2 sm:py-1 bg-gray-100 border border-gray-300 rounded-md mx-1 text-xs">↓</kbd>
                <span className="text-xs">निवडण्यासाठी</span>
              </div>
              <div className="flex items-center">
                <kbd className="px-1.5 py-0.5 sm:px-2 sm:py-1 bg-gray-100 border border-gray-300 rounded-md text-xs">Enter</kbd>
                <span className="ml-1 text-xs">पाहण्यासाठी</span>
              </div>
              <div className="flex items-center">
                <kbd className="px-1.5 py-0.5 sm:px-2 sm:py-1 bg-gray-100 border border-gray-300 rounded-md text-xs">Esc</kbd>
                <span className="ml-1 text-xs">बंद करण्यासाठी</span>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Notification Sidebar */}
      <NotificationSidebar
        isOpen={notificationSidebarOpen}
        onClose={() => setNotificationSidebarOpen(false)}
      />
    </div>
  );
};

export default DashBoardHeader;
