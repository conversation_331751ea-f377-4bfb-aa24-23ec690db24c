import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
  getPaginationRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
// import type { PaymentLog, PaymentLogResponse } from "@/types/payment";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { ChevronUp, ChevronDown, ArrowUpDown, Trash, Edit } from "lucide-react";
import { Input } from "@/components/ui/input";
import React from "react";
import { toast, useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { usePaymentListRateController } from "@/controller/Bill/BillController";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBookController } from "@/controller/tax/BookeMasterController";
import TaxListApi from "@/services/TaxServices";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import PaymentListApi from "@/services/PaymentLogsService";
import { Loader } from "@/components/globalcomponent/Loader";
// import { useQueryClient } from "@tanstack/react-query";

const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

const EditPopup = ({ isOpen, onClose, payment, onUpdate }) => {
  const [bookNumber, setBookNumber] = useState<any>();
  const [receiptNumber, setReceiptNumber] = useState<any>();
  const [paymentDate, setPaymentDate] = useState<string>("");
  const [receiptData, setReceiptData] = useState([]);
  const [receiptLoading, setReceiptLoading] = useState(false);
  
  const queryClient = useQueryClient();
  const { BookList, bookLoading } = useBookController();

  useEffect(() => {
    if (bookNumber) {
      fetchReceiptsOfSelectedNumber(bookNumber);
    }
  }, [bookNumber]);

  useEffect(() => {
    if (payment) {
      setBookNumber(Number(payment?.receipts?.book_number));
      setReceiptNumber(Number(payment?.receipts?.book_receipt_number));
      setPaymentDate(payment?.payment_date); // Initialize payment date
    }
  }, [payment]);

  const fetchReceiptsOfSelectedNumber = async (bookNumber) => {
    setReceiptLoading(true);
    try {
      TaxListApi.getReceiptNumbers(bookNumber, (response) => {
        if (response.status) {
          setReceiptData(response.data.data);
          const existingReceipt = response.data.data.find(
            (r) => r == Number(payment?.receipts?.book_receipt_number)
          );

          if (existingReceipt) {
            setReceiptNumber(Number(payment?.receipts?.book_receipt_number));
          } else {
            setReceiptNumber(""); // Reset if old receipt is not in the new list
          }
        } else {
          console.error("Error fetching receipts:", response.data);
        }
        setReceiptLoading(false);
      });
    } catch (error) {
      console.error("API error:", error);
      setReceiptLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      const response = await PaymentListApi.updatePaymentLog(
        payment.receipts.receipt_id,
        {
          book_number: bookNumber,
          book_receipt_number: receiptNumber,
          payment_date: paymentDate,
        }
      );
console.log("response----->",response);
      if (!response.status) throw new Error("Failed to update payment log");

      toast({
        description: "बुक यशस्वीपणे अद्यतनित केले गेले.",
        variant: "success",
      });
      queryClient.invalidateQueries(["paymentListMaster"]);
      onClose();
      onUpdate(); // Call the onUpdate function to refresh the data
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

return (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-lg bg-white">
      <DialogHeader>
        <DialogTitle>Edit Payment</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <p>
          <strong>मालमत्ता क्रमांक:</strong>{" "}
          {payment?.property?.propertyNumber}
        </p>
        <p>
          <strong>रक्कम:</strong> ₹{payment?.amount}
        </p>
        <label className="block text-gray-700 mb-2">दिनांक</label>
        <input
          type="date"
          value={paymentDate}
          onChange={(e) => setPaymentDate(e.target.value)}
          className="mt-1 w-full p-2 border rounded"
          max={new Date().toISOString().split('T')[0]}
        />
        <label className="block text-gray-700 mb-2">बुक क्रमांक</label>
        <Select onValueChange={setBookNumber} value={bookNumber}>
          <SelectTrigger className="mt-1 w-full">
            <SelectValue placeholder="Select Book Number" />
          </SelectTrigger>
          <SelectContent>
            {BookList.map((book) => (
              <SelectItem key={book.book_id} value={book.book_number}>
                {book.book_number}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <label className="block text-gray-700 mb-2">पावती क्रमांक</label>
        <Select
          onValueChange={setReceiptNumber}
          value={receiptNumber}
          disabled={!bookNumber || receiptLoading}
        >
          <SelectTrigger className="mt-1 w-full">
            <SelectValue placeholder="Select Receipt Number" />
          </SelectTrigger>
          <SelectContent>
            {receiptData.map((receipt) => (
              <SelectItem key={receipt} value={receipt}>
                {receipt}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          रद्द करा
        </Button>
        <Button onClick={handleUpdate}>अपडेट करा</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);

};

// const [editingPayment, setEditingPayment] = useState();

const BillHistory = () => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const { toast } = useToast();
  const [editingPayment, setEditingPayment] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
  
  const { PaymentListRateList, paymentLoading, pagination,
    searchValue,
    setSearchValue,
    searchOn,
    setSearchOn,} =
    usePaymentListRateController();
  const PaymentListRateLists = PaymentListRateList?.data?.data || [];
  const queryClient = useQueryClient();

  const { t } = useTranslation();

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleSearchKeyChange = (value: string) => {
    setSearchOn(value);
    setSearchValue(""); // Reset search value when changing search field
  };
  const handleConfirmDelete = async () => {
    if (selectedItem) {
      console.log("error----->",selectedItem)
      try {
        const response = await PaymentListApi.deletePayment(selectedItem.original.receipts.receipt_id);

        if (response.status) {
          toast({
            title: "Delete clicked",
            description: `Deleting payment`,
            variant: "success",
          });
          queryClient.invalidateQueries("paymentListMaster");
          setSelectedItem(null);
        } else {
          toast({
            title: "Delete clicked",
            description: `Failed to delete payment`,
            variant: "destructive",
          });
        }
      } catch (error) {

        console.log("error----->",error)
        toast({
          title: "Delete clicked",
          description: `Something went wrong`,
          variant: "destructive",
        });
      }
    }
    setIsDeleteOpen(false);
  };

  const handleDelete = async (row) => {


    setSelectedItem(row);
    setIsDeleteOpen(true);
  };
  const columns = [
    {
      accessorKey: "SrNo",
      header: "अ. क्र.",
      cell: ({ row }) => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorFn: row => row.property, 
      header: t("property.propertyNumberColumn"),
      cell: ({ row }) => {
        // Log the property number to the console
        console.log("row",row.original.property.propertyNumber);
    
        return (
          <div className="capitalize">
            {row.original.property.propertyNumber}
          </div>
        );
      },
    },
    
    {
      accessorKey: "old_propertyNumber",
      header: t("property.oldPropertyNumber"),
      cell: ({ row }) => (
        <div className="capitalize">{row.original.property.old_propertyNumber}</div>
      ),
    },
    {
      accessorKey: "amount",
      header: "रक्कम",
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("amount"));
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return formatted;
      },
    },
    {
      accessorKey: "payment_mode",
      header: "भरणा पद्धती",
      cell: ({ row }) => {
        const mode = row.getValue("payment_mode") as string;
        return mode.charAt(0).toUpperCase() + mode.slice(1).toLowerCase();
      },
    },

    {
      accessorKey: "payment_date",
      header: "दिनांक",
      cell: ({ row }) => {
        return format(new Date(row.getValue("payment_date")), "dd MMM yyyy");
      },
    },
    {
      accessorKey: "receipts.book_number",
      header: "बुक  क्रमांक",
      cell: ({ row }) => {
        const receipts = row.original.receipts;
        return receipts?.book_number || "-";
      },
    },
    {
      accessorKey: "receipts.book_receipt_number",
      header: "पावती क्रमांक",
      cell: ({ row }) => {
        const receipts = row.original.receipts;
        return receipts?.book_receipt_number || "-";
      },
    },
    {
      accessorKey: "Actions",
      header: "कृती",
      cell: ({ row }) => {
        const handleEdit = (payment) => {
          setEditingPayment(payment); // Open the popup with selected payment details
        };

        return (
          <div className="flex space-x-2">
            <button
              className="h-8 w-8 p-0"
              onClick={() => handleEdit(row.original)} // Pass row data
            >
              <Edit className="text-blue-500" />
            </button>

            <button
              // variant="submit"
              className="h-8 w-8 p-0 ml-2"
              onClick={() => handleDelete(row)}
            >
              <Trash className="text-red-500" />
            </button>
          </div>
        );
      },
    },
  ];
 
console.log("PaymentListRateLists",PaymentListRateLists)
 

  return (
    <div className="w-full h-fit p-6">
      <p className="w-full flex items-center justify-between ml-2 text-2xl font-semibold mb-2">
        {t("titles.paymentBillHistory")}
      </p>

      <WhiteContainer className="mt-5">
     
        <TanStackTable
          columns={columns}
          data={PaymentListRateLists}
          searchKey={"property.propertyNumberColumn"}
              searchColumn={"firstname"}
              loader={paymentLoading ? true : false}

          searchColumnArray={[
            "property_number",
            "old_property_number",
            "book_number",
            "receipt_number"           
          ]}
          serverPagination={{
            currentPage: pagination.page,
            pageSize: pagination.limit,
            totalPages: pagination.totalPages,
            totalRecords: pagination.totalRecords,
            onPageChange: pagination.setPage,
            onPageSizeChange: pagination.setLimit,
          }}
          onSearch={(value: string) => setSearchValue(value)} // Pass search value to controller
          onSearchFieldChange={handleSearchKeyChange} // Pass search field change to controller
          selectedSearchField={searchOn} // Pass current search field
        />

        {editingPayment && (
          <EditPopup
            isOpen={!!editingPayment}
            onClose={() => setEditingPayment(null)}
            payment={editingPayment}
            onUpdate={fetch}
          />
        )}
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
            <DeletePopUpScreen
              isOpen={isDeleteOpen}
              toggle={handleCancelDelete}
              itemName={""}
              onDelete={handleConfirmDelete}
            />
          )}
    </div>

  );
};

export default BillHistory;
