import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "@/components/ui/use-toast";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { z } from "zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { GlobalContext } from "@/context/GlobalContext";
import { ResponseData } from "@/model/auth/authServices";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useBookController } from "@/controller/tax/BookeMasterController";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

export interface BookMaster {
  book_id: string;
  book_number: string;
  value: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

interface BookInterface {
  editData?: any;
}

const BookMaster = ({ editData }: BookInterface) => {
  const [books, setBooks] = useState([]);
  const { BookList, createBook,  deleteBook, bookLoading } = useBookController();

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Setting, FormName.BookMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Setting, FormName.BookMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Setting, FormName.BookMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Setting, FormName.BookMaster, Action.CanDelete);
  const [formData, setFormData] = useState({
    book_number: "",
    value: "",
  });
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false); // Flag for editing mode
  const [editId, setEditId] = useState(null); // ID of the item being edited
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<BookMaster | null>(null);
  const userRef = useRef(null);
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);

  const schema = z.object({
    book_number: z.string().min(1, t("errorsRequiredField")),
    value: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      book_number: editData?.book_number || "",
      value: editData?.value || null,
    },
  });
  const {
    formState: { errors },
    reset,
    control,
  } = form;
  const Form = FormProvider;
  const dynamicValues = {
    name: t("setting.book"),
  };

 
  // Populate form with selected item's data for editing
  function handleEdit(item) {
    setIsEditing(true);
    setEditId(item.book_id); // Save the ID of the item being edited

    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      book_number: item.book_number,
      value: item.value,
    });
  }

  function handleDelete(item: BookMaster): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    if (selectedItem) {
      console.log("selectedItem",selectedItem)
      deleteBook(selectedItem.book_number, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            if(response.message=="Already in use"){
              toast({
                title: "The item is already in use and cannot be deleted.",
                variant: "warning",
              });
            }
            else{
            toast({
              title:t("api.deleteSuccess"),
              variant: "success",
            });
          }
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {

          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const checkDuplicate = (bookNumber: string) => {
    return BookList.some(
      (item) =>
        item.book_number === bookNumber &&
        (!isEditing || item.book_id !== editId)
    );
  };

  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();

    if (checkDuplicate(data.book_number)) {
      toast({
        title: t("errors.bookExists"),
        variant: "destructive",
      });
      return;
    }

    if (isEditing) {
      updateBook(
        {
          id: editId,
          payload: data,
        },
        {
          onSuccess: (response) => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            reset({
              book_number: "",
              value: null,
            });
            setIsEditing(false);
            setEditId(null);
          },
          onError: (error) => {
            toast({
              title: "error",
              variant: "destructive",
            });
          },
        }
      );
    } else {
      createBook(data, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          reset({
            book_number: "",
            value: null,
          });
        },
        onError: (error) => {
          toast({
            title: error.message || t("api.error"),
            variant: "destructive",
          });
        },
      });
    }
  };

  // Columns definition for TanStackTable
  const columns: ColumnDef<BookMaster>[] = [
    {
      accessorKey: "book_number",
      header: t("setting.bookNumber"),
      cell: ({ row }) => <div>{row.original?.book_number}</div>,
    },
    ...(CanDelete
      ? [
          {
            accessorKey: "actions",
            header: t("Actions"),
            cell: ({ row }) => (
              <>
                {CanDelete && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleDelete(row.original)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">
        {t("setting.bookMaster")}
      </h1>

      {(canUpdate || CanCreate) && (
      <WhiteContainer>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid md:grid-cols-3 gap-x-3">
              <div>
                <FormField
                  control={form.control}
                  name="book_number"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        {t("setting.bookNumber")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <Input
                        {...field}
                        type="text"
                        id="book_number"
                        name="book_number"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder= {t("bookNumber")}
                        className="mt-1 block w-full"
                        required
                      />
                      {errors.book_number && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid mb-1 ml-1 max-md:flex max-md:justify-end pt-[32px]">
                <Button type="submit">
                  {isEditing ? t("update") : t("add")}
                </Button>
              </div>
            </div>
          </form>
        </Form>
        <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
          {t("allFieldAreRequire")}
        </p>
      </WhiteContainer>
      )}

      {/* Book Table */}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={BookList}
          searchKey={"searchBook"}
          searchColumn={"book_number"}
          loader={bookLoading ? true : false}

        />
      </WhiteContainer>

      <DeletePopUpScreen
        isOpen={isDeleteOpen}
        toggle={handleCancelDelete}
        itemName={t("book")}
        onDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default BookMaster;
