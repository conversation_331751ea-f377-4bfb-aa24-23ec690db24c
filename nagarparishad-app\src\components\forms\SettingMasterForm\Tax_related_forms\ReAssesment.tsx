import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import axios from "axios";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import AsyncSelect from "@/components/ui/react-select";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { z } from "zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { GlobalContext } from "@/context/GlobalContext";
import { useDepreciationRateController } from "@/controller/tax/DepreciationRateController";
import { ResponseData } from "@/model/auth/authServices";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Api from "@/services/ApiServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
import { useReAssesmentController } from "@/controller/tax/ReAssesmentController";



export interface ReAssesmentMaster {
  reassessment_range_id: string;
  start_range: string;
  end_range: string;
  is_active: boolean;
  is_current: boolean;
  is_published: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ReactselectInterface {
  value: string;
  label: string;
}

export interface PropertyClassMasterListApiOject {
  property_type_class_id: string;
  property_type_class: string;
}



const ReAssesmentMaster = ({ editData }: any) => {
  const [depreciationRates, setDepreciationRates] = useState([]);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Setting, FormName.ReassessmentMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Setting, FormName.ReassessmentMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Setting, FormName.ReassessmentMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Setting, FormName.ReassessmentMaster, Action.CanDelete);

  const {
    ReAssesmentList,
    createReAssesment,
    ReAssesmentLoading
  } = useReAssesmentController();
  const [formData, setFormData] = useState({
    start_range: "",
    end_range: "",
    is_active: true,
    is_current: true,
    is_published: true,
  });
  const { t } = useTranslation();
  const [financialYears, setFinancialYears] = useState([]);
  const [isEditing, setIsEditing] = useState(false); // Flag for editing mode
  const [editId, setEditId] = useState(null); // ID of the item being edited
  const [showDeleteDialog, setShowDeleteDialog] = useState(false); // For delete confirmation dialog
  const [deleteId, setDeleteId] = useState(null); // ID of item to delete
  const userRef = useRef(null);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const { setOpen, toggleCollapse, setMasterComponent } = useContext(GlobalContext);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<ReAssesmentMaster | null>(null);

  const schema = z.object({
    start_range: z.string().nonempty({ message: t("errorsRequiredField") }),
    end_range: z.string().nonempty({ message: t("errorsRequiredField") }),
    is_active: z.boolean(),
    is_current: z.boolean(),
    is_published: z.boolean(),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      start_range: editData?.start_range || "",
      end_range: editData?.end_range || "",
      is_active: editData?.is_active || true,
      is_current: true,
      is_published: true,
    },
  });
  const {
    formState: { errors },
    reset,
    control,
    watch,
  } = form;
  const Form = FormProvider;
  const dynamicValues = {
    name: t("setting.depreciationRate"),
  };

  const fetchFinancialYears = async () => {
    try {
      console.time("Fetch financial years");
      const response = await Api.fyYears();
      console.timeEnd("Fetch financial years");

      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const { propertyClassList } = usePropertyClassMasterController();

  const propertyClassOption: ReactselectInterface[] = propertyClassList?.map(
    (propertyClass: PropertyClassMasterListApiOject) => ({
      value: propertyClass.property_type_class_id,
      label: propertyClass.property_type_class,
    })
  );

  // Populate form with selected item's data for editing
  function handleEdit(item) {
    setIsEditing(true);
    setEditId(item.reassessment_range_id); // Save the ID of the item being edited

    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      start_range: item.start_range,
      end_range: item.end_range,
      is_active: item.is_active,
      is_current: true,
      is_published: true,
    });
  }

  function handleDeleteConfirmation(id) {
    setDeleteId(id);
    setShowDeleteDialog(true);
  }
  function handleDelete(item: ReAssesmentMaster): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  
  const checkDuplicate = (startRange: string, endRange: string) => {
    return ReAssesmentList.some(
      (item) =>
        item.start_range === startRange &&
        item.end_range === endRange &&
        (!isEditing || item.reassessment_range_id !== editId)
    );
  };

  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();

    console.log("Form event:", e);
    console.log("Form data:", data);

    if (checkDuplicate(data.start_range, data.end_range)) {
      toast({
        title: t(
          "या वर्षासाठी आणि  मालमत्ता वापर उपप्रकारसाठी दर आधीच अस्तित्वात आहे"
        ),
        variant: "destructive",
      });
      return;
    }

    const payload = {
      start_range: data.start_range,
      end_range: data.end_range,
      is_active: data.is_active,
      is_current: data.is_current,
      is_published: data.is_published,
    };

   
      console.log("in creation process");
      createReAssesment(payload, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          reset({
            start_range: '',
            end_range: '',
            is_active: true,
            is_current: true,
            is_published: true,
          });
        },
        onError: (error) => {
          console.log("error", error);
          toast({
            title: error.message || t("api.error"),
            variant: "destructive",
          });
        },
      });
    }
  

  // Parse the last ending year from ReAssesmentList
  const parseYearRange = (range) => {
    const [start, end] = range.split('-');
    return { start: parseInt(start, 10), end: parseInt(end, 10) };
  };

  const lastEndingYear = ReAssesmentList.reduce(
    (maxYear, item) => {
      const { end } = parseYearRange(item.end_range);
      return end > maxYear ? end : maxYear;
    },
    new Date().getFullYear() // Default to current year if ReAssesmentList is empty
  );

  const startRangeOptions = Array.from({ length: 51 }, (_, i) => lastEndingYear + i);
  const selectedStartRange = parseYearRange(watch("start_range")).start;
  const endRangeOptions = Array.from({ length: 51 }, (_, i) => selectedStartRange + i).filter(year => year > selectedStartRange);

  // Columns definition for TanStackTable
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "start_range",
      header: t("setting.startYear"),
      cell: ({ row }) => <div>{row.original?.start_range}</div>,
    },
    {
      accessorKey: "end_range",
      header: t("setting.endYear"),
      cell: ({ row }) => <div>{row.original?.end_range}</div>,
    },
    {
      accessorKey: "status",
      header: t("Status"),
      cell: ({ row }) => (
        <div className={row.original?.is_active ? "text-green-500" : "text-red-500"}>
          {row.original?.is_active ? "Active" : "Inactive"}
        </div>
      ),
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }) => (
              <>
                {canUpdate && row.original?.is_current && row.original?.is_active && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && row.original?.is_current && row.original?.is_active && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2 !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">{t("setting.reassessmentRange")}</h1>
      {(canUpdate || CanCreate) && (
        <WhiteContainer>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid md:grid-cols-5 gap-x-3">
                <div>
                  <FormField
                    control={form.control}
                    name="start_range"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t("setting.reassessmentRange")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Select
                          {...field}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder={t("selectStartYear")} />
                          </SelectTrigger>
                          <SelectContent>
                            {startRangeOptions.map((year) => (
                              <SelectItem key={year} value={`${year}-${year + 1}`}>
                                {`${year}-${year + 1}`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.start_range && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="end_range"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t("setting.endDate")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Select
                          {...field}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder={t("selectEndYear")} />
                          </SelectTrigger>
                          <SelectContent>
                            {endRangeOptions.map((year) => (
                              <SelectItem key={year} value={`${year}-${year + 1}`}>
                                {`${year}-${year + 1}`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.end_range && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel> {t("setting.status")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Select
                          {...field}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder={t("selectStatus")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={true}>Active</SelectItem>
                            <SelectItem value={false}>Inactive</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.is_active && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid-cols-subgrid mb-1 ml-1 max-md:flex max-md:justify-end pt-[32px]">
                  {canUpdate && isEditing && (
                    <Button type="submit">
                      {t("update")}
                    </Button>
                  )}
                  {CanCreate && !isEditing && (
                    <Button type="submit">
                      {t("add")}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </Form>
          <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
            {" "}
            {t("allFieldAreRequire")}{" "}
          </p>
        </WhiteContainer>
      )}

      {/* Depreciation Rates Table */}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={ReAssesmentList}
          searchKey={"start_range"}
          searchColumn={"start_range"}
          loader={ReAssesmentLoading ? true : false}
        />
      </WhiteContainer>

      
    </div>
  );
};

export default ReAssesmentMaster;
