import React, { useState, useEffect } from "react";
import React<PERSON><PERSON> from "react-json-view";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Printer } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Loader } from "@/components/globalcomponent/Loader";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { useNamunaNineController } from "@/controller/report/VarshikAkarniNine";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import BillHistoryData from "./BillHistoryData";
import Api from "@/services/ApiServices";
import { useLocation } from "react-router-dom";
import ReportApi from "@/services/ReportServices";
import PropertyApi from "@/services/PropertyServices";

const NamunaNine = () => {
  const { t } = useTranslation();
  const [printLoading, setPrintLoading] = useState(false);
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");
  const [propertyNumber, setPropertyNumber] = useState("");
  const [oldPropertyNumber, setOldPropertyNumber] = useState("");
  const [searchOnParameter, setSearchOnParameter] = useState("");
  const [loading, setLoading] = useState(false);
  const [namunaDetailNine, setnamanuDetail] = useState(null);
  const [calulationPrivew, setcalculationPrivew] = useState(null);
  const [financialYears, setFinancialYears] = useState([]);
  const [showGenerateButton, setShowGenerateButton] = useState(false);
  const [milkatKarId, setMilkatKarId] = useState("");
  const [propertyId, setPropertyId] = useState("");
  const [warshikKarId, setWarshikKarId] = useState("");
  const [recordGenerated, setRecordGenerated] = useState(false);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [updatedData, setUpdatedData] = useState({});
  const [billHistoryData, setBillHistoryData] = useState([]);
  const [selectedTaxPayer, setselectedTaxPayer] = useState<any>("");


  const wardList = useWardMasterController();
  const wardOptions = wardList?.wardList?.map((ward) => ({
    value: ward.ward_id,
    label: ward.ward_name,
  }));

  const loadWardOptions = (inputValue, callback) => {
    setTimeout(() => {
      callback(
        wardOptions?.filter((option) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };

  const { namunaNineDetails, namunaNineData } = useNamunaNineController();
  const location = useLocation();

  const number = location.state?.propertyNumber;
  useEffect(() => {
    if (number) {
      setPropertyNumber(number); // Set the property number from location
      setSearchOnParameter("propertyNumber"); // Set the search parameter
    }
  }, [number]);

  // Separate useEffect to trigger the search after state update
  useEffect(() => {
    if (number && propertyNumber && searchOnParameter && !oldPropertyNumber && selectedFinancialYear) {
      handleNamunaNine(); // Trigger the search function only if propertyNumber is set from location
    }
  }, [propertyNumber, searchOnParameter, selectedFinancialYear]);

  const handlePrintTax = async () => {
    setPrintLoading(true);
    const newTab = window.open('', '_blank'); // <-- Open tab immediately
  
    try {
      const selectedPropertyNumber = oldPropertyNumber || propertyNumber;
      const params = { propertyNumber: selectedPropertyNumber };
  
      const response = await ReportApi.printBill(params);
      setPrintLoading(false);
  
      if (response.status && response.data) {
        const blob = new Blob([response.data], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
  
        if (newTab) {
          newTab.location.href = url; // <-- Assign blob URL to already opened tab
          newTab.focus();
        }
  
        toast({
          title: "Tax report generated successfully.",
          variant: "success",
        });
        toast({
          title: "Tax printed successfully.",
          variant: "success",
        });
      } else {
        newTab?.close(); // Close if no data
        toast({
          title: "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      setPrintLoading(false);
      newTab?.close(); // Close on error
      toast({
        title: "Failed to fetch the PDF. Please try again.",
        variant: "destructive",
      });
    }
  };
  

  const handlePropertyNumberChange = (e) => {
    setPropertyNumber(e.target.value.trim().toUpperCase());
    setOldPropertyNumber("");
    setSearchOnParameter("propertyNumber");
  };

  const handleOldPropertyNumberChange = (e) => {
    setOldPropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyNumber("");
    setSearchOnParameter("old_propertyNumber");
  };

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);

        // Set the selected financial year to the one with is_current flag
        const currentYear = response.data.data.find(year => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  const handleNamunaNine = async () => {
    const selectedPropertyNumber = oldPropertyNumber || propertyNumber;
    if (!selectedFinancialYear) {
      toast({
        title: `${t("selectFinancialYear")}`,
        variant: "destructive",
      });
      return;
    }
    if (!selectedPropertyNumber) {
      setnamanuDetail(null);
      toast({
        title: `${t("enterPropertyNumber")}`,
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setnamanuDetail(null);
    try {
      const response = await Api.getNamunaNine(
        selectedPropertyNumber,
        searchOnParameter,
        selectedFinancialYear
      );
      
      if (response.status && response.data.statusCode === 200 && response.data.data) {
        const data = response.data.data;
        setPropertyId(data.property_id);

        if (
          data?.warshikKar &&
          Array.isArray(data.warshikKar) &&
          data.warshikKar.length > 0
        ) {
          setWarshikKarId(data.warshikKar[0].warshik_karId);
        } else {
          console.error("warshikKar is not defined or is not an array");
        }
        setMilkatKarId(data.milkat_kar_id);

        if (data.count_get__milkat_kar == 0) {
          toast({
            title: `${t("noDataFound")}`,
            variant: "destructive",
          });
        } else if (data.count_get__milkat_kar == 1) {
          setShowGenerateButton(true);
        } else {
          setnamanuDetail(data);
          const latestName = data?.property_owner_details.filter(
            (name) => name.is_payer === true
          );

          setselectedTaxPayer(latestName[0] || null);
          setcalculationPrivew(
            data.getTaxData?.map((item) => JSON.parse(item.tax_data))
          );
        }
      } else {
        toast({
          title: `${t("noDataFound")}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    const value = Number(num || 0);
    return value.toFixed(2);
  };

  const handleSelectChange = (name) => {
    console.log("nameee", name);
    setselectedTaxPayer(() => name);
  };

  const handleGenerateRecord = async () => {
    try {
      setLoading(true);


       const params = {
        financialYear: selectedFinancialYear,
        propertyNumber: propertyNumber || null,
        oldPropertyNumber: oldPropertyNumber || null,
      };

      const response = await Api.processWarshikKarAkarni(params);
   




      
      if (response.status && response.data.statusCode === 200) {
        toast({
          title: "रेकॉर्ड यशस्वीरित्या तयार झाले",
          variant: "success",
        });
        setRecordGenerated(true);
        setShowGenerateButton(false);
        handleNamunaNine();
      } else {
        toast({
          title: "Failed to generate record",
          description: "An error occurred while processing the record.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const newWarshiKar = async () => {
    try {
      setLoading(true);
      console.log("namunaDetailNine",namunaDetailNine)
      const response = await Api.processWarshikKarAkarni({
        financialYear:selectedFinancialYear,
        milkatKarId:namunaDetailNine?.milkat_kar_id
    });
      
      if (response.status && response.data.statusCode === 200) {
        toast({
          title: `${t("recordGenerated")}`,
          variant: "success",
        });
        setRecordGenerated(true);
        setShowGenerateButton(false);
        handleNamunaNine();
      } else {
        toast({
          title: "Failed to generate record",
          description: "An error occurred while processing the record.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const newTaxGenerate = async () => {
    try {
      const response = await Api.processWarshikKarAkarni(
        selectedFinancialYear,
        milkatKarId
      );
      
      if (response.status && response.data.statusCode === 200) {
        setLoading(true);
        setnamanuDetail(null);
        setLoading(false);
      }
    } catch (error) {
      toast({
        title: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = () => {
    setIsPopupVisible(true);
  };

  const handleClosePopup = () => {
    setIsPopupVisible(false);
  };

  const handleUpdateData = async () => {
    try {
      setLoading(true);

      // Preprocess updatedData to convert empty strings or non-numeric values to 0
      const processedData: any = Object.keys(updatedData).reduce((acc, key) => {
        const value = updatedData[key];
        acc[key] = value === "" || isNaN(value) ? 0 : Number(value);
        return acc;
      }, {});

      const processedDataSum = Object.keys(processedData).reduce((sum, key) => {
        // Skip the "total_tax_previous" key
        if (key === "total_tax_previous") {
          return sum;
        }
        return sum + processedData[key];
      }, 0);

      processedData["total_tax_previous"] = processedDataSum;
      processedData.owner_id = selectedTaxPayer?.property_owner_details_id || null;
      
      const response = await Api.updateWarshikKarAkarni(
        processedData,
        warshikKarId,
        propertyId
      );

      if (response.status && response.data.statusCode === 200) {
        toast({
          title: "Data updated successfully",
          variant: "success",
        });
        handleNamunaNine();
      } else {
        toast({
          title: "Failed to update data",
          description: "An error occurred while updating the record.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (key, value) => {
    // If the input is cleared, set the value to 0
    const updatedValue = value;
    setUpdatedData((prevData) => ({
      ...prevData,
      [key]: updatedValue,
    }));
  };

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const warshikKarItem = namunaDetailNine?.warshikKar[0];
  const warshikKarUser = namunaDetailNine?.property_owner_details;
  useEffect(() => {
    if (warshikKarItem && typeof warshikKarItem === "object") {
      // Include all_property_tax_sum in the filtered keys
      const filteredKeys = Object.keys(warshikKarItem).filter(
        (key) => key.includes("_previous") || key === "all_property_tax_sum"
      );

      // Create an object with the filtered keys and their corresponding values
      const setUpdatedDataWithChange = filteredKeys.reduce((obj, key) => {
        obj[key] = warshikKarItem[key];
        return obj;
      }, {});

      // Update the state with the new object
      setUpdatedData(setUpdatedDataWithChange);
    } else {
      console.error("warshikKarItem is not defined or is not an object");
    }
  }, [warshikKarItem]);
  console.log("warshikKarItem", warshikKarItem);
  return (
    <div className="flex h-fit ">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          {t("namunaNine.milkatKarAkarni")}
        </h1>
        <WhiteContainer>
          <div>
            <div className="mt-1 mb-3">
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
                <div className="grid-cols-subgrid">
                  <Label>
                    {t("financialYear")}
                    <span className="ml-1 text-red-500">*</span>
                  </Label>
                  <Select
                    onValueChange={setSelectedFinancialYear}
                    value={selectedFinancialYear}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectYear")} />
                    </SelectTrigger>
                    <SelectContent>
                      {financialYears.map((year) => (
                        <SelectItem
                          key={year.financial_year_range}
                          value={year.financial_year_range}
                        >
                          {year.financial_year_range}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("namunaEight.propertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    type="text"
                    placeholder={t("namunaEight.propertyNumber")}
                    transliterate={false}
                    value={propertyNumber}
                    onChange={handlePropertyNumberChange}
                    onKeyDown={(e) => e.key === "Enter" && handleNamunaNine()}
                  />
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("property.oldPropertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.oldPropertyNumber")}
                    value={oldPropertyNumber}
                    onChange={handleOldPropertyNumberChange}
                    onKeyDown={(e) => e.key === "Enter" && handleNamunaNine()}
                  />
                </div>

                <div className=" flex  items-end align-middle">
                  <Button
                    variant="submit"
                    type="submit"
                    className=""
                    onClick={handleNamunaNine}
                  >
                    {t("search")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        )}
        {showGenerateButton && (
          <WhiteContainer>
            <div className="flex justify-center my-4">
              <Button onClick={handleGenerateRecord} disabled={loading}>
                {loading ? " वार्षिक कर मागणी..." : t("वार्षिक कर मागणी")}
              </Button>
            </div>
          </WhiteContainer>
        )}
        {namunaDetailNine && !loading && !showGenerateButton && (
          <WhiteContainer>
            <Tabs defaultValue="owner" className="w-full ">
              <TabsList className="w-[100%] md:w-fit !h-12">
                <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="owner"
                >
                  {t("namunaEight.propertyDescription")}{" "}
                </TabsTrigger>
                {/* <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="billHistory"
                >
                  {"पेमेंट बिल इतिहास"}{" "}
                </TabsTrigger> */}
              </TabsList>
              <TabsContent value="owner">
                <div className="  ">
                  <div className=" grid md:grid-cols-3 gap-x-3">
                    <div className="grid-cols-subgrid">
                      <Label className="text-sm">
                        {t("namunaEight.incomeHolderName")}
                      </Label>
                      <Textarea
                        className="mt-1 block w-full font-semibold"
                        placeholder={t("namunaEight.incomeHolderName")}
                        value={namunaDetailNine?.property_owner_details?.map(
                          (user) => user?.name
                        )}
                        transliterate={false}
                        disabled={true}
                      />
                    </div>
                    <div className="grid-cols-subgrid">
                      <Label className="text-sm">
                        {t("namunaEight.propertyNumber")}
                      </Label>
                      <Input
                        className="mt-1 block w-full font-semibold"
                        value={namunaDetailNine?.propertyNumber}
                        placeholder={t("namunaEight.propertyNumber")}
                        disabled={true}
                      />
                    </div>
{/*
                    <div className="w-1/2">
                      <Label className="text-sm">करदात्याचे नाव</Label>
                      <Select
                        value={selectedTaxPayer}
                        onValueChange={handleSelectChange}
                        defaultValue={selectedTaxPayer || null}
                      >
                        <SelectTrigger className="mt-1 w-full">
                          <SelectValue placeholder="Select TaxPayer Name" />
                        </SelectTrigger>
                        <SelectContent className="max-w-[250px]">
                          {namunaDetailNine?.property_owner_details?.map(
                            (name) => (
                              <SelectItem
                                key={name.property_owner_details_id}
                                value={name}
                              >
                                {name.name}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </div> */}
                  </div>
                </div>
                <div className="mt-2 w-1/4">
                  <Label className="text-[13px]">{t("सूट ")} </Label>

                  <Input
                    className=" block w-full bg-white"
                    type="number"
                    value={warshikKarItem?.property_type_discount.toFixed(2) || 0}
                    disabled={true}
                  />
                </div>

                <div className="grid md:grid-cols-5 gap-x-3 mt-2">
                  <div className=" w-full py-4 bg-[#f3f4f6] my-3 flex-col justify-center items-center rounded-2xl ">
                    <div className="flex flex-col items-center justify-center w-full">
                      <h3 className="font-bold">
                        {t("namunaEight.taxesOnBuildings")}{" "}
                      </h3>

                      <div className="form-element !w-3/4">
                        <div className="mt-2">
                          <Label className="text-[13px]">
                            {t("namunaEight.pastDue")}{" "}
                          </Label>

                          <Input
                            className=" block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.pastDue")}
                            defaultValue={
                              warshikKarItem[`all_property_tax_sum`] || 0
                            }
                            value={updatedData[`all_property_tax_sum`]}
                            onChange={(e) =>
                              handleInputChange(
                                `all_property_tax_sum`,
                                e.target.value
                              )
                            }
                            min="0" // Add min attribute to prevent negative values

                          />
                        </div>

                        <div className="mt-1">
                          <Label className="text-[13px]">
                            {t("namunaEight.currentTax")}{" "}
                          </Label>
                          <Input
                            className="  block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.currentTax ")}
                            value={formatNumber(
                              warshikKarItem[
                                `all_property_tax_sum_current`
                              ].toFixed(2) || 0
                            )}
                            disabled={true}
                          />
                        </div>
                        <div className="mt-1">
                          <Label className="text-[13px]">
                            {t("namunaEight.total")}{" "}
                          </Label>
                          <Input
                            className=" block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.totalAmount ")}
                            value={formatNumber(
                              warshikKarItem[
                                `all_property_tax_sum_total`
                              ].toFixed(2) || 0
                            )}
                            disabled={true}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {Object.entries(namunaDetailNine.tax_types).map(
                    ([key, value]) => (
                      <div
                        className=" w-full py-4 bg-[#f3f4f6] my-3 flex-col justify-center items-center rounded-2xl "
                        key={key}
                      >
                        <div className="flex flex-col items-center justify-center w-full">
                          <h3 className="font-bold">{value} </h3>

                          <div className="form-element !w-[80%]">
                            <div className="mt-2">
                              <Label className="text-[13px]">
                                {t("namunaEight.pastDue")}{" "}
                              </Label>

                              <Input
                                className=" block w-full bg-white"
                                type="number"
                                placeholder={t("namunaEight.pastDue")}
                                defaultValue={
                                  warshikKarItem[`${key}_previous`] || 0
                                }
                                value={updatedData[`${key}_previous`]}
                                onChange={(e) =>
                                  handleInputChange(
                                    `${key}_previous`,
                                    e.target.value
                                  )
                                }
                                min="0"
                              />
                            </div>

                            <div className="mt-1">
                              <Label className="text-[13px]">
                                {t("namunaEight.currentTax")}{" "}
                              </Label>
                              <Input
                                className="  block w-full bg-white"
                                type="number"
                                placeholder={t("namunaEight.currentTax ")}
                                value={formatNumber(
                                  warshikKarItem[`${key}_current`] || 0
                                )}
                                disabled={true}
                              />
                            </div>
                            <div className="mt-1">
                              <Label className="text-[13px]">
                                {t("namunaEight.total")}{" "}
                              </Label>
                              <Input
                                className=" block w-full bg-white"
                                type="number"
                                placeholder={t("namunaEight.totalAmount ")}
                                value={formatNumber(
                                  warshikKarItem[`${key}`] || 0
                                )}
                                disabled={true}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  )}

                  <div className=" w-full py-4 bg-[#f3f4f6] my-3 flex-col justify-center items-center rounded-2xl">
                    <div className="flex flex-col items-center justify-center w-full">
                      <p className="font-bold">
                        {t("namunaEight.totalTax")} (₹)
                      </p>

                      <div className="form-element !w-3/4">
                        <div className="mt-2">
                          <Label className="text-[13px]">
                            {t("namunaEight.pastDue")}
                          </Label>

                          <Input
                            className=" block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.pastDue")}
                            value={
                              warshikKarItem[`total_tax_previous`] || 0
                            }
                            disabled={true}
                          />
                        </div>

                        <div className="mt-1">
                          <Label className="text-[13px]">
                            {t("namunaEight.currentTax")}
                          </Label>
                          <Input
                            className="  block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.currentTax")}
                            value={formatNumber(
                              warshikKarItem[`total_tax_current`] || 0
                            )}
                            disabled={true}
                          />
                        </div>
                        <div className="mt-1">
                          <Label className="text-[13px]">
                            {t("namunaEight.total")}{" "}
                          </Label>
                          <Input
                            className=" block w-full bg-white"
                            type="number"
                            placeholder={t("namunaEight.totalAmount ")}
                            value={formatNumber(
                              warshikKarItem[`total_tax`] || 0
                            )}
                            disabled={true}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="submit"
                      disabled={
                        namunaDetailNine?.updateStatus !== "milkatKarUpdated"
                      }
                      onClick={newWarshiKar}
                      className="mt-2"
                    >
                      {t("namunaEight.taxDemand")}
                    </Button>
                    
                    <Button
                      className={`bg-[#2c93d2] hover:bg-[#2c93d2] mt-2 ${printLoading ? "cursor-not-allowed opacity-50" : ""}`}
                      onClick={handlePrintTax}
                      disabled={printLoading}
                    >
                      <span className={printLoading ? "hidden" : "relative"}>
                        {t("paymentform.printButton")}
                      </span>
                      <Printer
                        className={`w-5 ${printLoading ? "animate-bounce" : "ml-4"}`}
                      />
                    </Button>

                    <Button
                      type="button"
                      variant="submit"
                      onClick={handleViewDetails}
                      className="mt-2"
                    >
                      View Details
                    </Button>

                    <Button
                      type="button"
                      variant="submit"
                      onClick={handleUpdateData}
                      className="mt-2"
                    >
                      Update Data
                    </Button>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="billHistory">
                <BillHistoryData data={billHistoryData} />
              </TabsContent>
            </Tabs>
          </WhiteContainer>
        )}

        {isPopupVisible && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
            <div className="bg-white p-6 rounded-lg shadow-lg max-h-[90vh] overflow-y-auto w-[50%]">
              <h2 className="text-lg font-semibold mb-4">Tax Data Preview</h2>
              <ReactJson
                src={calulationPrivew}
                theme="solarized"
                collapsed={false}
                enableClipboard={false}
                displayDataTypes={false}
                displayObjectSize={false}
              />
              <div className="flex justify-end mt-4">
                <Button onClick={handleClosePopup}>Close</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NamunaNine;
