import React, { useContext, useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { z, object } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { PropertyDetailsInterface } from "@/model/global-master";
import { Button } from "@/components/ui/button";
import { PropertyContext } from "@/context/PropertyContext";
import { PropertyRegistrationInterface } from "@/model/propertyregistration-master";
import { ChevronsRight, ChevronsLeft, Edit, Trash } from "lucide-react";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import { GlobalContext } from "@/context/GlobalContext";
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";
import { toast } from "@/components/ui/use-toast";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import PropertyApi from "@/services/PropertyServices";

const panCardRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

const AssetDetailForm: React.FC<PropertyDetailsInterface> = ({
  handleNextStep,
  handlePreviousStep,
}) => {
  const { t } = useTranslation();
  const { propertyInformation, setPropertyInformation } =
    useContext(PropertyContext);
  const { updateProperty, setUpdateProperty } = useContext(GlobalContext);
  const schema = z
    .object({
      owner_name: z.string().nonempty(t("errorsRequiredField")),
      mobile_number: z
        .string()
        .length(10, "फोन नंबर १० अंकांचा असणे अनिवार्य आहे")
        .optional()
        .or(z.literal("")), // Allow empty strings
      email_id: z.string().email("अवैध ईमेल").optional().or(z.literal("")),
      aadhar_number: z
        .string()
        .optional()
        .refine((value) => value === undefined || value.length === 14, {
          message: "आधार क्रमांक १२ अंकांचा असणे अनिवार्य आहे",
        })
        .or(z.literal("")),
      pan_card: z
        .string()
        .regex(panCardRegex, "अवैध पॅन कार्ड क्रमांक")
        .optional()
        .or(z.literal("")),
      gender: z.string().optional(), // Initially optional
      owner_type: z.string().optional(), // Initially optional
      marriage_flag: z.string(),
      partner_name: z.string().optional(),
    })
    .superRefine((data, ctx) => {
      if (!updateProperty) {
        if (!data.gender) {
          ctx.addIssue({
            path: ["gender"],
            message: t("errorsRequiredField"),
          });
        }
        if (!data.owner_type) {
          ctx.addIssue({
            path: ["owner_type"],
            message: t("errorsRequiredField"),
          });
        }
      }
    });

  const [editId, setEditId] = useState<number>(100);
  const [isMarried, setIsMarried] = useState(false);
  const [adduser, setAddUser] = useState([]);
  const [assetDetails, setAssetDetails] = useState<any>([]);
  const [currentDetails, setcurrentDetails] = useState<any>();
  const [selectedIndex, setSelectedIndex] = useState<any>(null);
  const [updateOwner, setUpdateOwner] = useState(false);
  const { ownerTypeList } = useOwnerTypeController();
  const [isDialogOpen, setIsDialogOpen] = useState(false); // Control dialog visibility
  const [currentItem, setCurrentItem] = useState(null); // Hold the current item being deleted
  const [currentIndex, setCurrentIndex] = useState<number | null>(null); // Hold the index of the item being deleted

  const toggleDialog = () => {
    setIsDialogOpen(!isDialogOpen);
  };

  useEffect(() => {
    const ownerDetails =
      propertyInformation?.AssessmentDetailsForm?.property_owner_details;
    setAssetDetails(ownerDetails);
    if (ownerDetails && ownerDetails.length > 0) {
      setcurrentDetails(ownerDetails[0]);
    } else {
      console.warn("No owner details found.");
    }
  }, [propertyInformation]);

  const ownerTypeMap = Object.fromEntries(
    ownerTypeList.map((ownerType) => [
      ownerType.owner_type_id,
      ownerType.owner_type,
    ])
  );

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    values: {
      owner_name: "",
      mobile_number: "",
      email_id: "",
      aadhar_number: "",
      pan_card: "",
      owner_type: "",
      marriage_flag: "no",
      partner_name: "",
      gender: "",
    },
  });

  useEffect(() => {
    if (currentDetails && updateOwner) {
      form.reset({
        owner_name: currentDetails.name,
        gender: currentDetails.gender,
        mobile_number: currentDetails.mobile_number,
        email_id: currentDetails.email_id,
        aadhar_number: formatAdharNo(currentDetails.aadhar_number),
        pan_card: currentDetails.pan_card || "",
        owner_type: currentDetails.owner_type,
        marriage_flag: currentDetails.marriage_flag,
        partner_name: currentDetails?.partner_name || "",
      });
    } else {
      form.reset({
        owner_name: "",
        gender: "",
        mobile_number: "",
        email_id: "",
        aadhar_number: "",
        pan_card: "",
        owner_type: "",
        marriage_flag: "no",
        partner_name: "",
      });
    }
  }, [currentDetails, updateOwner]);

  const {
    formState: { errors },
    watch,
    handleSubmit,
    clearErrors,
  } = form;
  const allValues = watch(); // This will watch all fields in the form

  const handleSave = async (data: any) => {
    try {
      let updatedAssetDetails = [...(assetDetails || [])]; // Ensure assetDetails is always an array
      const previousAssetDetails = [...updatedAssetDetails];

      const newAssetDetail = {
        name: data.owner_name.trim(), // Trim whitespace from name
        mobile_number: data.mobile_number,
        email_id: data.email_id,
        aadhar_number: data.aadhar_number,
        pan_card: data.pan_card || "",
        owner_type: data.owner_type,
        marriage_flag: data.marriage_flag,
        gender: data.gender,
        partner_name: data?.partner_name,
      };

      if (updateOwner && selectedIndex !== null) {
        const isAadharDuplicate = updatedAssetDetails.some(
          (item, index) =>
            item.aadhar_number &&
            item.aadhar_number === newAssetDetail.aadhar_number &&
            index !== selectedIndex
        );

        const isPanDuplicate = updatedAssetDetails.some(
          (item, index) =>
            item.pan_card &&
            item.pan_card === newAssetDetail.pan_card &&
            index !== selectedIndex
        );

        if (newAssetDetail.aadhar_number && isAadharDuplicate) {
          toast({
            title: `डुप्लिकेट आधार क्रमांक सापडला: ${newAssetDetail.aadhar_number}`,
            variant: "destructive",
          });
          return;
        }

        if (newAssetDetail.pan_card && isPanDuplicate) {
          toast({
            title: `डुप्लिकेट पॅन कार्ड क्रमांक सापडला: ${newAssetDetail.pan_card}`,
            variant: "destructive",
          });
          return;
        }

        const isDuplicate = updatedAssetDetails.some(
          (item, index) =>
            index !== selectedIndex &&
            item.name.trim() === newAssetDetail.name.trim() &&
            item.mobile_number === newAssetDetail.mobile_number &&
            item.email_id === newAssetDetail.email_id &&
            item.owner_type === newAssetDetail.owner_type
        );

        if (isDuplicate) {
          toast({
            title: `तपशीलांची पुनरावृत्ती आढळली.`,
            variant: "destructive",
          });
          return;
        }

        updatedAssetDetails[selectedIndex] = {
          ...updatedAssetDetails[selectedIndex],
          ...newAssetDetail,
        };

        setAssetDetails(updatedAssetDetails);

        toast({
          title: `मालमत्ता धारक यशस्वीरित्या अपडेट झाला.`,
          variant: "success",
        });

        setUpdateOwner(false);
        setSelectedIndex(null);
      } else {
        const isAadharDuplicate = updatedAssetDetails.some(
          (item) =>
            item.aadhar_number &&
            item.aadhar_number === newAssetDetail.aadhar_number
        );

        const isPanDuplicate = updatedAssetDetails.some(
          (item) => item.pan_card && item.pan_card === newAssetDetail.pan_card
        );

        if (newAssetDetail.aadhar_number && isAadharDuplicate) {
          toast({
            title: `डुप्लिकेट आधार क्रमांक सापडला: ${newAssetDetail.aadhar_number}`,
            variant: "destructive",
          });
          return;
        }

        if (newAssetDetail.pan_card && isPanDuplicate) {
          toast({
            title: `डुप्लिकेट पॅन कार्ड क्रमांक सापडला: ${newAssetDetail.pan_card}`,
            variant: "destructive",
          });
          return;
        }

        const duplicate = updatedAssetDetails.some(
          (item) =>
            item.name.trim() === newAssetDetail.name.trim() &&
            item.mobile_number === newAssetDetail.mobile_number &&
            item.email_id === newAssetDetail.email_id &&
            item.owner_type === newAssetDetail.owner_type
        );

        if (duplicate) {
          toast({
            title: `तपशीलांची पुनरावृत्ती आढळली.`,
            variant: "destructive",
          });
          return;
        }

        updatedAssetDetails.push(newAssetDetail);
        setAssetDetails(updatedAssetDetails);

        toast({
          title: `नवीन मालमत्ता धारक यशस्वीरित्या जोडला गेला.`,
          variant: "success",
        });
      }

      setAssetDetails(updatedAssetDetails);

      const differences = updatedAssetDetails
        .map((current, index) => {
          const previous = previousAssetDetails[index] || {};
          return {
            index,
            differences: Object.keys(current).reduce((acc, key) => {
              if (current[key] !== previous[key]) {
                acc[key] = { previous: previous[key], current: current[key] };
              }
              return acc;
            }, {}),
          };
        })
        .filter((item) => Object.keys(item.differences).length > 0);

      form.reset({
        owner_name: "",
        mobile_number: "",
        email_id: "",
        aadhar_number: "",
        pan_card: "",
        gender: "",
        owner_type: "",
        marriage_flag: "no",
        partner_name: "",
      });
      setIsMarried(false);
      setcurrentDetails(null);
    } catch (error) {
      console.error("Error saving data:", error);
    }
  };

  const columns = [
    {
      accessorKey: "zone",
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "owner_type",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("assetDetailsForm.ownerType")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {ownerTypeMap[row.original.owner_type] || "Unknown"}
        </div>
      ),
    },
    {
      accessorKey: "owner_name",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("assetDetailsForm.fullName")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.name}</div>
      ),
    },
    {
      accessorKey: "marriage_flag",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("assetDetailsForm.marrageStatus")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {row.original.marriage_flag == "yes" ? "विवाहित" : "अविवाहित"}
        </div>
      ),
    },
    {
      accessorKey: "mobile_number",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("assetDetailsForm.mobileNumber")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.mobile_number}</div>
      ),
    },
    {
      accessorKey: "email_id",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("assetDetailsForm.email")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => <div>{row.original.email_id}</div>,
    },
    {
      accessorKey: `${t("Actions")}`,
      enableHiding: false,
      cell: ({ row }: { row: any }) => (
        <div className="flex space-x-2">
          <button
            className="h-8 w-8 p-0 justify-center"
            onClick={() => handleEdit(row.original, row.index)}
          >
            <Edit className="text-blue-500" />
          </button>
          {/* {!updateProperty && (
            <button
              className="h-8 w-8 p-0 justify-center"
              onClick={() => handleDelete(row.original, row.index)}
            >
              <Trash className="text-red-500" />
            </button>
          )} */}

          <button
            className="h-8 w-8 p-0 justify-center"
            onClick={() => handleDelete(row.original, row.index)}
          >
            <Trash className="text-red-500" />
          </button>
        </div>
      ),
    },
  ];

  const userRef = useRef(null);

  const handleEdit = (rowData: any, index: number) => {
    setUpdateOwner(true);
    setcurrentDetails(rowData);
    setSelectedIndex(index);
    setIsMarried(rowData.marriage_flag === "yes");
  };

  const handleDelete = (rowData: any, index: number) => {
    setCurrentItem(rowData);
    setCurrentIndex(index);
    toggleDialog();
  };

  const confirmDelete = async () => {
    console.log("currentItem", currentItem);
    if (currentIndex !== null) {
      setAssetDetails((prevData: any[]) => {
        if (Array.isArray(prevData)) {
          const updatedData = [...prevData];
          updatedData.splice(currentIndex, 1);
          return updatedData;
        }
        return prevData;
      });

      if (currentItem?.property_owner_details_id) {
        const res = await PropertyApi.deleteOwner(
          currentItem?.property_owner_details_id,
          ""
        );
        if(!res.status) {
          toast({
            title: `Error`,
            variant: "destructive",
          });

      }
    }

      toast({
        title: `${"मालमत्ता धारक यशस्वीरित्या हटविला गेला."}`,
        variant: "success",
      });
    }
    toggleDialog();
  };

  useEffect(() => {
    console.log("Updated owner details:", assetDetails);
  }, [assetDetails]);

  const handleNext = () => {
    let finalData = [];
    if (adduser && adduser.length > 0) {
      const data1 = adduser.map((item) => ({
        ...item,
        aadhar_number: item.aadhar_number?.replace(/-/g, ""),
      }));
    }

    finalData = [...assetDetails];

    if (finalData.length > 0) {
      setPropertyInformation((prevState: PropertyRegistrationInterface) => ({
        ...prevState,
        AssessmentDetailsForm: {
          ...prevState.AssessmentDetailsForm,
          property_owner_details: finalData,
        },
      }));
    }

    setcurrentDetails(null);
    handleNextStep();
    setAssetDetails(finalData);
  };

  const onError = (errors) => {
    console.log("Validation errors:", errors);
  };

  return (
    <div className="h-fit w-full" ref={userRef && userRef}>
      <Form {...form}>
        <form onSubmit={handleSubmit(handleSave, onError)}>
          <h2 className="text-xl font-semibold font-Noto">
            {t("मालमत्ता धारकाची माहिती")}
          </h2>
          <hr className="my-2" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 gap-x-5">
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="owner_type"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      {t("assetDetailsForm.ownerType")}
                      {!updateProperty && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                        }}
                        value={field.value}
                        {...field}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue
                            placeholder={t("assetDetailsForm.ownerType")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {ownerTypeList.map((ownerType) => (
                              <SelectItem
                                key={ownerType.owner_type_id}
                                value={ownerType.owner_type_id}
                              >
                                {ownerType.owner_type}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    {errors.owner_type && (
                      <FormMessage className="ml-1">
                        Select Owner Type
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="owner_name"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {" "}
                      {t("assetDetailsForm.fullName")}{" "}
                      <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder={t("enterFullName")}
                        {...field}
                      />
                    </FormControl>
                    {errors.owner_name && (
                      <FormMessage className="ml-1">Enter Name</FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="mobile_number"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {" "}
                      {t("assetDetailsForm.mobileNumber")}{" "}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        transliterate={false}
                        type="text"
                        placeholder={t("assetDetailsForm.mobileNumber")}
                        {...field}
                      />
                    </FormControl>
                    {errors.mobile_number && (
                      <FormMessage className="ml-1">
                        Enter Mobile Number
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="email_id"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel> {t("assetDetailsForm.email")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        transliterate={false}
                        type="text"
                        placeholder={t("assetDetailsForm.email")}
                        {...field}
                      />
                    </FormControl>
                    {errors.email_id && (
                      <FormMessage className="ml-1">Enter Email ID</FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="aadhar_number"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>{t("assetDetailsForm.adharNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        transliterate={false}
                        type="text"
                        placeholder={t("assetDetailsForm.adharNumber")}
                        {...field}
                        onKeyDown={(e) => {
                          if (e.keyCode === 8) {
                            let value = e.target.value.replace(/\D/g, "");
                            if (value.length > 1 && value.length % 4 === 1) {
                              value = value.substring(0, value.length - 1);
                            }
                            field.onChange(value);
                          }
                        }}
                        onChange={(e) => {
                          let value = e.target.value
                            .replace(/\D/g, "")
                            .substring(0, 12);
                          const hasDash = value.includes("-");
                          if (hasDash) {
                            value = value
                              .replace(/(.{4})/g, "$1-")
                              .slice(0, 14);
                          } else {
                            value = value
                              .replace(/(.{4})/g, "$1-")
                              .slice(0, 14);
                          }
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    {errors.aadhar_number && (
                      <FormMessage className="ml-1">
                        Enter Aadhar Number
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="pan_card"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel> {t("assetDetailsForm.panNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        transliterate={false}
                        type="text"
                        placeholder={t("assetDetailsForm.panNumber")}
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value.toUpperCase();
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    {errors.pan_card && (
                      <FormMessage className="ml-1">
                        Enter PAN Number
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      {" "}
                      {t("assetDetailsForm.gender")}{" "}
                      {!updateProperty && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.onChange(value)}
                        value={field.value}
                        {...field}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue
                            placeholder={t("assetDetailsForm.gender")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="MALE">Male</SelectItem>
                            <SelectItem value="FEMALE">Female</SelectItem>
                            <SelectItem value="OTHER">Other</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    {errors.gender && (
                      <FormMessage className="ml-1">Select Gender</FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="marriage_flag"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      विवाह स्थिती <span className="ml-1 text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          const married = value === "yes";
                          field.onChange(value);
                          setIsMarried(married);
                        }}
                        value={field.value}
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="yes" />
                          </FormControl>
                          <FormLabel className="font-normal">विवाहित</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="no" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            अविवाहित
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    {errors.marriage_flag && (
                      <FormMessage className="ml-1">
                        Select one option
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            {isMarried && (
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="partner_name"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>पती/पत्नी चे नाव</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder={t("enterFullName")}
                          {...field}
                        />
                      </FormControl>
                      {errors.partner_name && (
                        <FormMessage className="ml-1">Enter Name</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>
          <div className="w-full justify-end flex mb-3 mt-3">
            {updateOwner ? (
              <Button type="submit">{t("update")}</Button>
            ) : (
              <Button type="submit">{t("submit")}</Button>
            )}
          </div>
        </form>
      </Form>
      <TanStackTable columns={columns} data={assetDetails} />
      <div className="w-full text-end">
        <Button
          variant="outline"
          type="button"
          className="mr-4 border-BlueText text-BlueText"
          onClick={handlePreviousStep}
        >
          {<ChevronsLeft className="w-5 h-5 font-bold " />}{" "}
        </Button>
        <Button
          type={"button"}
          variant="submit"
          onClick={() => {
            handleNext();
          }}
          disabled={!(assetDetails?.length > 0)}
        >
          {" "}
          {<ChevronsRight className="w-5 h-5 font-bold " />}
        </Button>
      </div>
      <DeletePopUpScreen
        isOpen={isDialogOpen}
        toggle={toggleDialog}
        itemName={currentItem?.name || ""}
        onDelete={confirmDelete}
      />
    </div>
  );
};

export default AssetDetailForm;
