import React, { useState, useEffect, useContext, useRef } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import AsyncSelect from "@/components/ui/react-select";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { DepreciationRateUpdateApi } from "@/model/tax/depreciationRateMasterInterface";
import { z } from "zod";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { GlobalContext } from "@/context/GlobalContext";
import { useDepreciationRateController } from "@/controller/tax/DepreciationRateController";
import { ResponseData } from "@/model/auth/authServices";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Api from "@/services/ApiServices";
import PropertyApi from "@/services/PropertyServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

export interface DepreciationRateMaster {
  depreciation_rate_id: string;
  from_age: number;
  to_age: number;
  financial_year: string;
  value: number;
  status: string;
  property_type_class_id: {
    property_type_class_id: string;
    property_type_class: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ReactselectInterface {
  value: string;
  label: string;
}

export interface PropertyClassMasterListApiOject {
  property_type_class_id: string;
  property_type_class: string;
}

interface DepreciationRateInterface {
  editData?: DepreciationRateUpdateApi;
}

const DepreciationRateMaster = ({ editData }: DepreciationRateInterface) => {
  const [depreciationRates, setDepreciationRates] = useState([]);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanRead
  );
  const canUpdate = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanUpdate
  );
  const CanCreate = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanCreate
  );
  const CanDelete = canPerformAction(
    ModuleName.Setting,
    FormName.DeprecationRate,
    Action.CanDelete
  );

  const {
    DepreciationRateList,
    createDepreciationRate,
    updateDepreciationRate,
    deleteDepreciationRate,
    propertyLoading,
  } = useDepreciationRateController();
  const [formData, setFormData] = useState({
    from_age: "",
    to_age: "",
    financial_year: "",
    reassessment_range_id: "",
    value: "",
    status: "Active",
    property_type_class_id: "",
  });
  const { t } = useTranslation();
  const [reassessmentRanges, setReassessmentRanges] = useState([]);
  const [isEditing, setIsEditing] = useState(false); // Flag for editing mode
  const [editId, setEditId] = useState(null); // ID of the item being edited
  const [showDeleteDialog, setShowDeleteDialog] = useState(false); // For delete confirmation dialog
  const [deleteId, setDeleteId] = useState(null); // ID of item to delete
  const userRef = useRef(null);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] =
    useState<DepreciationRateMaster | null>(null);

  const schema = z.object({
    financial_year: z.string().optional(), // Optional for backward compatibility
    reassessment_range_id: z.string().min(1, t("errorsRequiredField")),
    value: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
    status: z.string().min(1, t("errorsRequiredField")),
    property_type_class_id: z.string().min(1, t("errorsRequiredField")),
    from_age: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
    to_age: z.union([
      z.number().min(0, { message: t("errorsRequiredField") }),
      z.null(),
      z.string(),
    ]),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      financial_year: editData?.financial_year || "",
      reassessment_range_id: editData?.reassessment_range_id || "",
      value: editData?.value || null,
      status: editData?.status || "Active",
      property_type_class_id: editData?.property_type_class_id || "",
      from_age: editData?.from_age || null,
      to_age: editData?.to_age || null,
    },
  });
  const {
    formState: { errors },
    reset,
    control,
  } = form;
  const Form = FormProvider;
  const dynamicValues = {
    name: t("setting.depreciationRate"),
  };

  const fetchReassessmentRanges = async () => {
    try {
      console.time("Fetch reassessment ranges");
      PropertyApi.getReassessmentRanges((response) => {
        console.timeEnd("Fetch reassessment ranges");

        if (response.status && response.data.statusCode === 200) {
          const formattedData = response.data.data.map((item) => ({
            ...item,
            reassessment_id: item.reassessment_range_id,
            reassessment_range: `${item.start_range} to ${item.end_range}`,
          }));
          setReassessmentRanges(formattedData);
        }
      });
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchReassessmentRanges();
  }, []);

  const { propertyClassList } = usePropertyClassMasterController();

  const propertyClassOption: ReactselectInterface[] = propertyClassList?.map(
    (propertyClass: PropertyClassMasterListApiOject) => ({
      value: propertyClass.property_type_class_id,
      label: propertyClass.property_type_class,
    })
  );

  // Populate form with selected item's data for editing
  function handleEdit(item) {
    setIsEditing(true);
    setEditId(item.depreciation_rate_id); // Save the ID of the item being edited

    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
    reset({
      financial_year: item.financial_year,
      reassessment_range_id:
        item.reassessmentRange?.reassessment_range_id || "",
      value: item.value,
      status: item.status,
      property_type_class_id:
        item.property_type_class_id.property_type_class_id,
      from_age: item.from_age,
      to_age: item.to_age,
    });
  }

  function handleDeleteConfirmation(id) {
    setDeleteId(id);
    setShowDeleteDialog(true);
  }
  function handleDelete(item: DepreciationRateMaster): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteDepreciationRate(selectedItem.depreciation_rate_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
const checkDuplicate = (reassessmentRangeId: string, propertyClassId: string, fromAge: number |string, toAge: number) => {
  return DepreciationRateList.some(
    (item) =>
      item.reassessmentRange?.reassessment_range_id === reassessmentRangeId &&
      item.property_type_class_id.property_type_class_id === propertyClassId &&
      item.from_age === fromAge &&
      item.to_age === toAge &&
      (!isEditing || item.depreciation_rate_id !== editId)
  );
};


  const onSubmit = (data: z.infer<typeof schema>, e) => {
    e.preventDefault();

    console.log("Form event:", e);

    console.log("Form data:", data);

    if (
 checkDuplicate(
      data.reassessment_range_id,
      data.property_type_class_id,
      data.from_age,
      data.to_age
    )    ) {
      toast({
        title: t(
          "या पुनर्मूल्यांकन श्रेणीसाठी आणि मालमत्ता प्रकार विभागसाठी दर आधीच अस्तित्वात आहे"
        ),
        variant: "destructive",
      });
      return;
    }

    if (isEditing) {
      updateDepreciationRate(
        {
          id: editId,
          payload: data,
        },
        {
          onSuccess: (response) => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            reset({
              financial_year: "",
              reassessment_range_id: "",
              value: "",
              status: "Active",
              property_type_class_id: "",
              to_age: "",
              from_age: "",
            });
            setIsEditing(false);
            setEditId(null);
          },
          onError: (error) => {
            console.log("error", error);
            toast({
              title: "error",
              variant: "destructive",
            });
          },
        }
      );
    } else {
      console.log("in creationprocess");
      createDepreciationRate(data, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          reset({
            financial_year: "",
            reassessment_range_id: "",
            value: "",
            status: "Active",
            property_type_class_id: "",
            from_age: "",
            to_age: "",
          });
        },
        onError: (error) => {
          console.log("error", error);
          toast({
            title: error.message || t("api.error"),
            variant: "destructive",
          });
        },
      });
    }
  };
  // Columns definition for TanStackTable
  const columns: ColumnDef<DepreciationRateMaster>[] = [
    {
      accessorKey: "reassessmentRange",
      header: t("setting.reassessmentRange"),
      cell: ({ row }) => {
        const startRange = row.original?.reassessmentRange?.start_range;
        const endRange = row.original?.reassessmentRange?.end_range;
        return (
          <div>
            {startRange && endRange
              ? `${startRange} to ${endRange}`
              : row.original?.financial_year}
          </div>
        );
      },
    },
    {
      accessorKey: "property_type_class",
      header: t("मालमत्ता प्रकार विभाग"),
      cell: ({ row }) => (
        <div>{row.original.property_type_class_id.property_type_class}</div>
      ),
    },
    {
      accessorKey: "status",
      header: t("स्थिति"),
      cell: ({ row }) => <div>{row.original?.status}</div>,
    },
    {
      accessorKey: "value",
      header: t("दर"),
      cell: ({ row }) => <div>{row.original?.value}</div>,
    },
    {
      accessorKey: "from_age",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("वयोमर्यादेपासून")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original?.from_age}</div>,
    },
    {
      accessorKey: "to_age",
      header: t("वयोमर्यादेपर्यंत"),
      cell: ({ row }) => <div>{row.original?.to_age}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: "actions",
            header: t("Actions"),
            cell: ({ row }) => (
              <>
                {canUpdate && (
                  <button
                    className="h-8 w-8 p-0"
                    onClick={() => handleEdit(row.original)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {CanDelete && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleDelete(row.original)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">घट दर</h1>
      {(canUpdate || CanCreate) && (
        <WhiteContainer>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid md:grid-cols-5 gap-x-3">
                <div>
                  <FormField
                    control={form.control}
                    name="reassessment_range_id"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {t("setting.reassessmentRange")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>

                        <Select
                          {...field}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue
                              placeholder={t("setting.selectReassessment")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {reassessmentRanges.map((range) => (
                              <SelectItem
                                key={range.reassessment_range_id}
                                value={range.reassessment_range_id}
                              >
                                {`${range.start_range} to ${range.end_range}`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.reassessment_range_id && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  {/* <Label>{t("मालमत्ता प्रकार विभाग")}</Label> */}
                  <FormField
                    control={form.control}
                    name="property_type_class_id"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {" "}
                          {t("मालमत्ता प्रकार विभाग")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Controller
                          control={form.control}
                          name="property_type_class_id"
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t("master.propertyclass")}
                              className="mt-1 h-[2.5rem]"
                              value={
                                propertyClassOption.find(
                                  (option: any) =>
                                    option.value === controllerField.value
                                ) || null
                              }
                              options={propertyClassOption}
                              colourOptions={propertyClassOption}
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);
                              }}
                            />
                          )}
                        />
                        {errors.property_type_class_id && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  <FormField
                    control={form.control}
                    name="from_age"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {t("वयोमर्यादेपासून")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Input
                          {...field}
                          type="number"
                          id="from_age"
                          name="from_age"
                          value={field.value}
                          onChange={(event) =>
                            field.onChange(Number(event.target.value))
                          }
                          placeholder="Enter From Age"
                          className="mt-1 block w-full"
                          required
                        />
                        {errors.value && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="to_age"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {t("वयोमर्यादेपर्यंत")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Input
                          {...field}
                          type="number"
                          id="to_age"
                          name="to_age"
                          value={field.value}
                          onChange={(event) =>
                            field.onChange(Number(event.target.value))
                          }
                          placeholder="Enter To Age"
                          className="mt-1 block w-full"
                          required
                        />
                        {errors.to_age && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  {/* <Label>{t("setting.status")}</Label> */}
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {" "}
                          {t("setting.status")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Select
                          {...field}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder={t("selectStatus")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Active">Active</SelectItem>
                            <SelectItem value="Inactive">Inactive</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.status && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  {/* <Label>{t("setting.value")}</Label> */}
                  <FormField
                    control={form.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          {t("setting.value")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <Input
                          {...field}
                          type="float"
                          id="value"
                          name="value"
                          value={field.value}
                          // onChange={handleInputChange}
                          onChange={(event) =>
                            field.onChange(Number(event.target.value))
                          }
                          placeholder={t("setting.value")}
                          className="mt-1 block w-full"
                          required
                          min="0" // Add min attribute to prevent negative values
                          step="0.01" // Add step attribute to allow decimal places
                        />
                        {errors.value && (
                          <FormMessage className="ml-1">
                            {t("errors.requiredField")}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid-cols-subgrid mb-1 ml-1 max-md:flex max-md:justify-end pt-[32px]">
                  {canUpdate && isEditing && (
                    <Button type="submit">{t("update")}</Button>
                  )}
                  {CanCreate && !isEditing && (
                    <Button type="submit">{t("add")}</Button>
                  )}
                </div>
              </div>
            </form>
          </Form>
          <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
            {" "}
            {t("allFieldAreRequire")}{" "}
          </p>
        </WhiteContainer>
      )}

      {/* Depreciation Rates Table */}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={DepreciationRateList}
          searchKey={"मालमत्ता प्रकार विभाग"}
          searchColumn={"property_type_class"}
          loader={propertyLoading ? true : false}
        />
      </WhiteContainer>

      <DeletePopUpScreen
        isOpen={isDeleteOpen}
        toggle={handleCancelDelete}
        itemName={t("depreciationRate")}
        onDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default DepreciationRateMaster;
