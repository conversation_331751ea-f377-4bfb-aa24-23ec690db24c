import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React, { FC, useContext, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { object, z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import WhiteContainer from "../WhiteContainer";
import PropertyApi from "@/services/PropertyServices";
import { GlobalContext } from "@/context/GlobalContext";
import { toast } from "@/components/ui/use-toast";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { usePublicMasterData } from "@/hooks/usePublicMasterData";
import { Loader2, ZoomIn } from "lucide-react";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css"; // optional
import axios, { AxiosError, isAxiosError } from "axios";
import { Loader } from "@/components/globalcomponent/Loader";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import PublicApi from "@/services/PublicApiServcies";
const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

interface ServiceCardProps {
  icon: string;
  title: string;
  linkTo: string;
}

// const ServiceCard: FC<ServiceCardProps> = ({ icon, title, linkTo }) => {
//   return (
//     <Link to={linkTo} className="hover:no-underline">
//       <div className="bg-white rounded-lg shadow-lg p-4 flex flex-col items-center border cursor-pointer">
//         <div className="w-16 h-16 bg-green-200 rounded-full flex items-center justify-center mb-4">
//           <span className="text-2xl">{icon}</span>
//         </div>
//         <p className="font-medium text-xl">{title}</p>
//       </div>
//     </Link>
//   );
// };

const HomePageContent: FC = () => {
  const { t } = useTranslation();

  const { setSearchResults } = useContext(GlobalContext);

  const navigate = useNavigate();

  const schema = object({
    ownerName: z.string().min(1, { message: t("errorsRequiredField") }),
    zone: z.string().min(1, { message: t("errorsRequiredField") }),
    ward: z.string().min(1, { message: t("errorsRequiredField") }),
  });

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerName: "",
      zone: "",
      ward: "",
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = form;

  const [inputValue, setInputValue] = useState("");
  const [isAdvancedSearch, setIsAdvancedSearch] = useState(false);
  const [selectedWard, setSelectedWard] = useState("");
  const [loader, setLoader] = useState(false);

  // Use public APIs for zone/ward data (no authentication required)
  const { zoneList, wardList, loading: masterDataLoading, error: masterDataError } = usePublicMasterData();

  const [searchDisabled, setSearchDisabled] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null); // Track search errors
  const [isSearching, setIsSearching] = useState(false);
  const [otp, setOtp] = useState("");
  const [verificationId, setVerificationId] = useState("");
  const [propertyDetails, setPropertyDetails] = useState<any>(null);
  const [message, setMessage] = useState("");
  const [filteredResults, setFilteredResults] = useState([]);
  const [isOtpSent, setIsOtpSent] = useState(false); // Track OTP state
  const [isOtpComplete, setIsOtpComplete] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  // useEffect(() => {
  //   if (inputValue.trim()) {
  //     const results = fakeData.filter(
  //       (entry) =>
  //         entry.phone.includes(inputValue) ||
  //         entry.propertyNumber.includes(inputValue)
  //     );
  //     setFilteredResults(results);
  //   } else {
  //     setFilteredResults([]);
  //   }
  // }, [inputValue]);

  const handleInputChange = (event) => {
    setInputValue(event.target.value.trim().toUpperCase());
    setSearchError(null);
  };

  const handleReset = () => {
    setInputValue("");
    setSelectedWard("");
    setValue("ward", "");
    setValue("ownerName", "");
    setValue("zone", "");
    setSearchError(null);
  };

  const handleOnlinepayment = (item: any) => {
    navigate("/payment", {
      state: {
        rowData: item,
      },
    });
  };

  const toggleAdvancedSearch = () => {
    setIsAdvancedSearch((prev) => !prev);
  };

  // const handleSearch = async () => {
  //   try {
  //     const trimmedInputValue = inputValue.trim();

  //     if (!trimmedInputValue) {
  //       setSearchError(t("errorsRequiredField"));
  //       return;
  //     }
  //     navigate("/search-list");
  //     setSearchDisabled(true);
  //     PropertyApi.getPropertyDetailsBynumber(trimmedInputValue, (response) => {
  //       if (response.status) {
  //         setSearchResults(response.data);
  //         navigate("/search-list");
  //         // toast({
  //         //   title: t("api.fetch"),
  //         //   variant: "success",
  //         // });
  //       } else {
  //         toast({
  //           title: t("api.error"),
  //           description: response.data.message,
  //           variant: "destructive",
  //         });
  //       }
  //       setSearchDisabled(false);
  //     });
  //   } catch (error) {
  //     console.error("Error occurred while fetching property details:", error);
  //     toast({
  //       title: t("api.error"),
  //       variant: "destructive",
  //     });
  //     setSearchDisabled(false);
  //   }
  // };

  const handledSearch = async () => {
    try {
      const trimmedInputValue = inputValue.trim();

      if (!trimmedInputValue) {
        setSearchError(t("errorsRequiredField"));
        return;
      }

      setIsSearching(true);

      // setSearchDisabled(true);
      const response = await PublicApi.checkProperty(trimmedInputValue);

      if (response.status && response.data) {
        navigate("/search-list", {
          state: { searchResults: response.data.data },
        });
        toast({
          title: t("api.fetch"),
          variant: "success",
        });
      } else {
        toast({
          title: t("api.error"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error occurred while fetching property details:", error);
      toast({
        title: t("api.error"),
        variant: "destructive",
      });
    } finally {
      setSearchDisabled(false);
      setIsSearching(false);
    }
  };
  const handleSearch = async () => {
    try {
      // Determine whether the input is a mobile number or property number
      const requestData = {
        mobile_number: /^\d+$/.test(inputValue) ? inputValue : undefined, // Check if input is a number
        propertyNumber: /^\d+$/.test(inputValue) ? undefined : inputValue, // Assume it's a property number if not a number
      };

      const response = await PublicApi.sendOtp(inputValue);

      

      if (response.status) {
        setVerificationId(response.data.data.verificationId);
        alert(response.data.message); // Display the OTP message
      } else {
        alert("Error sending OTP");
      }

      if (verificationId) {
        try {
          const response = await PublicApi.validateOtp(verificationId, otp);


          if (response.status) {
            navigate("/search-list", {
              state: { searchResults: response.data.data },
            }); // Navigate to the result page
          } else {
            alert("Error validating OTP");
          }
        } catch (error) {
          alert("Error: " + (error as Error).message);
        }
      }
    } catch (error) {
      alert("Error: " + (error as Error).message);
    }
  };

  const handleSearchAndOtpS = async () => {
    try {
      if (!isOtpSent) {
        const otpResponse = await PublicApi.sendOtp(inputValue);

        if (otpResponse.data.statusCode === 201) {
          setVerificationId(otpResponse.data.data.data.verificationId);

          setIsOtpSent(true); // Mark OTP as sent
          toast({
            title: otpResponse.data.data.message,
            variant: "success",
          });
        } else {
          alert("Error sending OTP");
        }
      } else {
        if (!otp) {
          alert("Please enter the OTP");
          return;
        }

        const validateResponse = await PublicApi.validateOtp(
          verificationId,
          otp
        );

        if (validateResponse.status) {
          navigate("/search-list", {
            state: { searchResults: validateResponse.data },
          }); // Navigate to results page
        } else {
          alert("Error validating OTP");
        }
      }
    } catch (error) {
      alert("Error: " + (error as Error).message);
    }
  };

  const otpInputRef = useRef<HTMLInputElement>(null);

  // Handle OTP autofill
  const handleOtpInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 6 && /^\d*$/.test(value)) {
      setOtpValue(value);
      if (value.length === 6) {
        // Auto-submit when OTP is complete
        handleSearchAndOtp();
      }
    }
  };
  
  const handleResendOtp = async () => {
    try {


      const response = await await PublicApi.sendOtp(inputValue);

      if (response.data.statusCode === 201 && response.data.data) {
        setVerificationId(response.data.data.verificationId);

        // Extract and auto-fill OTP from response message
        const otpMatch = response.data.message.match(/OTP is (\d+)/);
        if (otpMatch && otpMatch[1]) {
          setOtpValue(otpMatch[1]);
        }

        toast({
          title: "OTP यशस्वीरित्या पाठवला",
          description: response.data.message,
          variant: "success",
        });
      }
    } catch (error) {
      if (isAxiosError(error)) {
        toast({
          title: "Failed to resend OTP",
          description: error.response?.data?.message || "An error occurred",
          variant: "destructive",
        });
      }
    }
  };
  const handleSearchAndOtp = async () => {
    setLoader(true);
    try {
      setIsSearching(true);
      if (!isOtpSent) {
        const requestData = {
          mobile_number: /^\d+$/.test(inputValue) ? inputValue : undefined,
          propertyNumber: /^\d+$/.test(inputValue) ? undefined : inputValue,
        };
  
        const response = await PublicApi.sendOtp(inputValue);
        if (response.status && response.data.data) {
          setVerificationId(response.data.data.verificationId);
          const otpMatch = response.data.message.match(/OTP is (\d+)/);
          if (otpMatch && otpMatch[1]) {
            setOtpValue(otpMatch[1]);
          }
          setIsOtpSent(true);
          setLoader(false);
          toast({
            title: "OTP यशस्वीरित्या पाठवला",
            description: response.data.message,
            variant: "success",
          });
        } else {
          setLoader(false);
          toast({
            title: `${t("noDataFound")}`,
            description: response.data.message,
            variant: "destructive",
          });
        }
      } else {
        if (!otpValue || otpValue.length !== 6) {
          setLoader(false);
          toast({
            title: "अवैध OTP",
            description: "कृपया वैध 6-अंकी OTP प्रविष्ट करा",
            variant: "destructive",
          });
          return;
        }
  
        const response = await PublicApi.validateOtp(verificationId, otpValue);
        if (response.status) {
          navigate("/search-list", {
            state: { searchResults: response.data.data },
          });
          toast({
            title: "Success",
            description: "OTP verified successfully",
            variant: "success",
          });
        } else {
          setLoader(false);
          toast({
            title: "Error",
            description: "OTP validation failed",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      setLoader(false);
      if (isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || "An error occurred";
        if (error.response) {
          const statusCode = error.response.status;
          if (errorMessage.toLowerCase().includes("mobile number not found")) {
            toast({
              title: "मोबाईल क्रमांक नोंदणीकृत नाही",
              variant: "destructive",
            });
          } else if (errorMessage.toLowerCase().includes("property number")) {
            toast({
              title: `${t("noDataFound")}`,
              variant: "destructive",
            });
          } else {
            switch (statusCode) {
              case 400:
                toast({
                  title: "Invalid Input",
                  description: errorMessage,
                  variant: "destructive",
                });
                break;
              case 401:
                toast({
                  title: "अवैध OTP",
                  description: "तुम्ही प्रविष्ट केलेला OTP चुकीचा आहे",
                  variant: "destructive",
                });
                break;
              default:
                toast({
                  title: `${t("noDataFound")}`,
                  variant: "destructive",
                });
            }
          }
        } else {
          toast({
            title: "Network Error",
            description: "Please check your internet connection",
            variant: "destructive",
          });
        }
      } else {
        console.error("Unexpected error:", error);
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
      }
    } finally {
      setIsSearching(false);
    }
  };
  const handleAdvancedSearch = async () => {
    event.preventDefault();
    setSearchDisabled(true);

    try {
      const formData = {
        ownerName: form.getValues("ownerName").trim(),
        zone: form.getValues("zone"),
        ward: form.getValues("ward"),
      };

      PropertyApi.getPropertyDetailsByName(
        3, // limit
        formData.ward,
        formData.zone,
        formData.ownerName,
        1, // page
        (response) => {
          if (response.status) {
            setSearchResults(response.data.data);
            // toast({
            //   title: t("succes"),
            //   variant: "success",
            // });
            navigate("/search-list");

            setSearchDisabled(false);
          } else {
            console.error("Failed to fetch property details:", response.data);
            toast({
              title: response.data.message,
              description: response.data.message,

              variant: "destructive",
            });
            setSearchDisabled(false);
          }
        }
      );
    } catch (error) {
      console.error("Error occurred while fetching property details:", error);
      toast({
        title: t("failedToSavePermissions"),
        variant: "destructive",
      });
      setSearchDisabled(false);
    }
  };

  const filteredZones = zoneList.filter(
    (zone) => zone.ward?.ward_id === selectedWard
  );

  return (
    <div className="bg-Secondary w-full py-9 flex flex-1 items-center">
      <div className="max-w-7xl mx-auto px-0 sm:px-6 lg:px-8 py-0 ">
        <WhiteContainer className="bg-transparent shadow-none">
          <div className="flex justify-center my-6 flex-col items-center">
            {!isAdvancedSearch ? (
              <>
                <div className="relative sm:w-[600px] w-full mx-auto">
                  <div className="flex relative !w-full">
                    <input
                      className="!rounded-full text-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-2 border-Graphite !w-full bg-white  h-12 px-4 shadow-sm"
                      placeholder={t("propertysearchholder")}
                      value={inputValue}
                      onChange={handleInputChange}
                      onKeyDown={(e) =>
                        e.key === "Enter" && handleSearchAndOtp()
                      }
                    />
                    {inputValue == "" ? (
                      <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                        <svg
                          className="h-5 w-5 text-zinc-400"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>
                      </div>
                    ) : (
                      <div
                        className="absolute inset-y-0 right-0 pr-4 flex items-center cursor-pointer"
                        onClick={() => setInputValue("")} // Clear the input value on click
                      >
                        <svg
                          className="h-5 w-5 text-zinc-400 cursor-pointer"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </div>
                    )}
                    {/* <Tippy content={t("advancesearch")}>
                      <ZoomIn
                        className="h-7 w-7 cursor-pointer ml-5 mt-3 "
                        onClick={toggleAdvancedSearch}
                      />
                    </Tippy> */}
                  </div>

                  <div className="text-center mt-2">
                    {/* <Button variant="submit" type="button"   className=""  onClick={handleSearch}  >
                      {t("search")}
                    </Button> */}
                    {isOtpSent && (
                      <div className="space-y-4">
                        <label className="block text-sm font-medium text-gray-700 text-center">
                          {/* Enter the 6-digit OTP sent to your mobile */}
                        </label>
                        <div className="flex justify-center">
                          <InputOTP
                            ref={otpInputRef}
                            maxLength={6}
                            value={otpValue}
                            onChange={setOtpValue}
                            className="gap-2 "
                          >
                            <InputOTPGroup className="gap-2">
                              {Array.from({ length: 6 }).map((_, i) => (
                                <InputOTPSlot
                                  key={i}
                                  index={i}
                                  className="w-10 h-12 text-lg border-2 bg-white focus:border-blue-500 focus:ring-blue-500"
                                />
                              ))}
                            </InputOTPGroup>
                          </InputOTP>
                        </div>
                      </div>
                    )}

                    {isOtpSent && (
                      <div className="flex justify-center items-center py-2">
                        <p className="text-sm font-semibold text-gray-500">
                          OTP मिळाला नाही ?
                        </p>
                        <p
                          onClick={handleResendOtp}
                          className="text-sm font-bold hover:text-dark-blue-900 cursor-pointer hover:underline mx-1"
                          style={{ color: "#2c93d2" }}
                        >
                          पुन्हा पाठवा
                        </p>
                      </div>
                    )}

                    {loader ? (
                      <Button disabled className="text-center mt-2">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin " />
                      </Button>
                    ) : (
                      <Button
                        className="text-center mt-2"
                        onClick={handleSearchAndOtp}
                      >
                        {isSearching
                          ? `${t("search")}`
                          : isOtpSent
                            ? `${t("search")}`
                            : `${t("search")}`}
                      </Button>
                    )}
                  </div>

                  {searchError && (
                    <p className="text-red-500 text-sm mt-1">{searchError}</p>
                  )}

                  {/* {inputValue && (
                    <div className="absolute w-full bg-white border mt-2 rounded-lg shadow-lg z-10 max-h-96 overflow-y-auto p-2">
                      {filteredResults.length > 0 ? (
                        filteredResults.slice(0, 3).map((result, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 rounded-lg   mb-1 hover:bg-Secondary transition-all"
                          >
                            <div className="flex items-center">
                              <div>
                                <p className="font-semibold text-zinc-900 dark:text-white">
                                  {result.name}
                                </p>
                                <p className="text-sm text-zinc-600 dark:text-zinc-400">
                                  {result.address}
                                </p>
                                <div>
                                  <p className="text-sm text-zinc-600 dark:text-zinc-400">
                                    <span className="text-black">
                                      Property Number:{" "}
                                    </span>
                                    {result.propertyNumber}
                                  </p>
                                  <p className="text-sm text-zinc-600 dark:text-zinc-400">
                                    <span className="text-black">
                                      Phone Number:{" "}
                                    </span>
                                    {result.phone}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="text-sm text-zinc-500 dark:text-zinc-400">
                              <Button
                                variant="submit"
                                onClick={() => handleOnlinepayment(result)}
                              >
                                {t("paytax")}{" "}
                              </Button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-4 text-center text-gray-500">
                          {t("noResults")}
                        </div>
                      )}
                    </div>
                  )} */}
                </div>

                {/* <div className="flex space-x-4 mt-5">
                  <Button
                    type="button"
                    variant="submit"
                    className="w-fit px-5 min-w-24 "
                    onClick={handleReset}
                  >
                    {t("reset")}
                  </Button>
                  <Button
                    onClick={handleSearch}
                    variant="submit"
                    className="w-fit px-5 min-w-24 "
                    disabled={searchDisabled}
                  >
                    {t("search")}
                  </Button>
                </div> */}
                <p
                  className="sm:text-base text-sm text-center mt-4 cursor-pointer underline text-black font-medium"
                  onClick={toggleAdvancedSearch}
                >
                  {isAdvancedSearch ? t("back") : ""}
                </p>
              </>
            ) : (
              <WhiteContainer className=" w-full p-8">
                <div className=" mx-auto space-y-4 ">
                  <h1 className="text-center font-semibold text-3xl">
                    {t("searchBy")}
                  </h1>
                  <Form {...form}>
                    <form onSubmit={handleSubmit(handleAdvancedSearch)}>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name="ward"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t("ward.wardLabel")}</FormLabel>
                              <Select
                                value={selectedWard}
                                onValueChange={(value) => {
                                  setSelectedWard(value);
                                  field.onChange(value);
                                }}
                              >
                                <SelectTrigger>
                                  {(wardList &&
                                    wardList.length > 0 &&
                                    wardList.filter(
                                      (ward: any) =>
                                        ward.ward_id === field.value
                                    )[0]?.ward_name) ||
                                    t("ward.selectWard")}
                                </SelectTrigger>
                                <SelectContent>
                                  {wardList.map((ward) => (
                                    <SelectItem
                                      key={ward.ward_id}
                                      value={ward.ward_id}
                                    >
                                      {ward.ward_name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>

                              {errors.ward && (
                                <FormMessage className=" mt-1">
                                  {errors.ward.message}
                                </FormMessage>
                              )}
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="zone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel> {t("zone.zoneLabel")}</FormLabel>
                              <Select
                                value={field.value}
                                onValueChange={(value) => {
                                  field.onChange(value);
                                }}
                                disabled={!selectedWard}
                              >
                                <SelectTrigger>
                                  {(filteredZones &&
                                    filteredZones?.length > 0 &&
                                    filteredZones.filter(
                                      (zone: any) =>
                                        zone.zone_id === field.value
                                    )[0]?.zoneName) ||
                                    t("zone.selectZone")}
                                </SelectTrigger>
                                <SelectContent>
                                  {filteredZones.map((zone) => (
                                    <SelectItem
                                      key={zone.zone_id}
                                      value={zone.zone_id}
                                    >
                                      {zone.zoneName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {errors.zone && (
                                <FormMessage className="mt-1">
                                  {errors.zone.message}
                                </FormMessage>
                              )}
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="ownerName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t("searchOwnerName")}</FormLabel>
                              <Input
                                className="mt-1"
                                placeholder={t("searchOwnerName")}
                                {...field}
                              />
                              {errors.ownerName && (
                                <FormMessage className=" mt-1">
                                  {errors.ownerName.message}
                                </FormMessage>
                              )}
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex space-x-4 mt-4 justify-center">
                        <Button
                          type="button"
                          variant="submit"
                          className="w-fit px-5 min-w-24 "
                          onClick={handleReset}
                        >
                          {t("reset")}
                        </Button>
                        <Button
                          type="submit"
                          variant="submit"
                          className="w-fit px-5 min-w-24 "
                          disabled={searchDisabled}
                        >
                          {t("search")}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </div>
                <p
                  className="sm:text-base text-sm text-center mt-4 cursor-pointer underline text-black font-medium"
                  onClick={toggleAdvancedSearch}
                >
                  {isAdvancedSearch ? t("back") : t("advancesearch")}
                </p>
              </WhiteContainer>
            )}
          </div>
        </WhiteContainer>
      </div>
    </div>
  );
};

export default HomePageContent;
