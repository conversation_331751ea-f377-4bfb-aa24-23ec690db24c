
const numeralMappings = {
    marathi: {
        '0': '०', '1': '१', '2': '२', '3': '३', '4': '४',
        '5': '५', '6': '६', '7': '७', '8': '८', '9': '९'
    },
    english: {
        '०': '0', '१': '1', '२': '2', '३': '3', '४': '4',
        '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'
    }
};

function convertToNumericValue(numeralString: string, language: 'marathi' | 'english'): string {
    let numericValue = '';
    for (let i = 0; i < numeralString.length; i++) {
        const digit = numeralString[i];
        if (Object.prototype.hasOwnProperty.call(numeralMappings[language], digit)) {
            numericValue += numeralMappings[language][digit];
        } else {
            numericValue += digit;
        }
    }
    return numericValue;
}

export function convertNumerals(numberString: string | number, lang: 'marathi' | 'english'): string {
    const numeralString = numberString.toString();
    return convertToNumericValue(numeralString, lang);
}

