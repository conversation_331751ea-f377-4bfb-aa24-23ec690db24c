import React, { createContext, useContext, useReducer, useEffect, useCallback, useState, useMemo } from 'react';
import { Notification, NotificationContextType, SSEMessage } from '@/types/notification';
import { useSSE } from '@/hooks/useSSE';
import { NotificationApi } from '@/services/NotificationServices';

const STORAGE_KEY = 'app_notifications';
const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Notification reducer
type NotificationAction =
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'UPDATE_NOTIFICATION'; payload: { id: string; updates: Partial<Notification> } }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'CLEAR_ALL' }
  | { type: 'LOAD_FROM_STORAGE'; payload: Notification[] };

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
}

const notificationReducer = (state: NotificationState, action: NotificationAction): NotificationState => {
  switch (action.type) {
    case 'ADD_NOTIFICATION':
      return {
        notifications: [action.payload, ...state.notifications],
        unreadCount: state.unreadCount + (action.payload.isRead ? 0 : 1) // Increment unread if not already read
      };

    case 'UPDATE_NOTIFICATION':
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.payload.id
          ? { ...notification, ...action.payload.updates, updatedAt: new Date() }
          : notification
      );
      // Recalculate unread count based on the new state
      const newUnreadCountAfterUpdate = updatedNotifications.filter(n => !n.isRead).length;
      return {
        notifications: updatedNotifications,
        unreadCount: newUnreadCountAfterUpdate
      };

    case 'REMOVE_NOTIFICATION':
      const removedNotification = state.notifications.find(n => n.id === action.payload);
      return {
        notifications: state.notifications.filter(notification => notification.id !== action.payload),
        unreadCount: removedNotification && !removedNotification.isRead ? state.unreadCount - 1 : state.unreadCount
      };

    case 'MARK_AS_READ':
      const markedReadNotifications = state.notifications.map(notification =>
        notification.id === action.payload
          ? { ...notification, isRead: true }
          : notification
      );
      const newUnreadCountAfterMarkRead = markedReadNotifications.filter(n => !n.isRead).length;
      return {
        notifications: markedReadNotifications,
        unreadCount: newUnreadCountAfterMarkRead
      };

    case 'MARK_ALL_AS_READ':
      return {
        notifications: state.notifications.map(notification => ({ ...notification, isRead: true })),
        unreadCount: 0
      };

    case 'CLEAR_ALL':
      return {
        notifications: [],
        unreadCount: 0
      };

    case 'LOAD_FROM_STORAGE':
      const unread = action.payload.filter(n => !n.isRead).length;
      return {
        notifications: action.payload,
        unreadCount: unread
      };

    default:
      return state;
  }
};

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const 
NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, {
    notifications: [],
    unreadCount: 0
  });

  // Track authentication status
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Initialize with current auth status to avoid initial false state
    const userData = localStorage.getItem('UserData');
    const accessToken = localStorage.getItem('AccessToken');
    return !!(userData && accessToken);
  });

  // Check authentication status
  useEffect(() => {
    const checkAuthStatus = () => {
      const userData = localStorage.getItem('UserData');
      const accessToken = localStorage.getItem('AccessToken');
      const isLoggedIn = !!(userData && accessToken);

      // Only update if the status actually changed
      setIsAuthenticated(prevAuth => {
        // If user logged out, clear notifications
        if (!isLoggedIn && prevAuth) {
          console.log('User logged out, clearing notifications');
          dispatch({ type: 'CLEAR_ALL' });
        }
        return isLoggedIn;
      });
    };

    // Listen for storage changes (login/logout events)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'UserData' || e.key === 'AccessToken') {
        checkAuthStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []); // Remove isAuthenticated dependency to prevent infinite loop

  // Load notifications from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        const notifications = JSON.parse(stored).map((n: any) => ({
          ...n,
          createdAt: new Date(n.createdAt),
          updatedAt: new Date(n.updatedAt)
        }));
        dispatch({ type: 'LOAD_FROM_STORAGE', payload: notifications });
      } catch (error) {
        console.error('Error loading notifications from storage:', error);
      }
    }
  }, []);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state.notifications));
  }, [state.notifications]);

  // SSE message handler
  const handleSSEMessage = useCallback((message: SSEMessage) => {
    console.log('Received SSE message:', message);
    // console.log('Received SSE message: 2', message.type, state);

    switch (message.type) {
      case 'progress_update':
        dispatch({
          type: 'UPDATE_NOTIFICATION',
          payload: {
            id: message.notificationId,
            updates: {
              progress: message.data.progress,
              message: message.data.message || 'Processing...',
              status: 'processing',
              metadata: message.data.metadata
            }
          }
        });
        break;

      case 'completion':
        dispatch({
          type: 'UPDATE_NOTIFICATION',
          payload: {
            id: message.notificationId,
            updates: {
              status: 'completed',
              progress: 100,
              message: message.data.message || 'Process completed successfully',
              downloadUrl: message.data.downloadUrl,
              type: 'success',
              isRead: false // Mark as unread when completed
            }
          }
        });
        break;

      case 'error':
        dispatch({
          type: 'UPDATE_NOTIFICATION',
          payload: {
            id: message.notificationId,
            updates: {
              status: 'failed',
              message: message.data.message || 'Process failed',
              type: 'error',
              isRead: false // Mark as unread when failed
            }
          }
        });
        break;

      case 'notification_update':
        dispatch({
          type: 'UPDATE_NOTIFICATION',
          payload: {
            id: message.notificationId,
            updates: message.data
          }
        });
        break;
    }
  }, []);

  // Fetch all notifications from the backend and manage state
  const fetchPendingNotifications = useCallback(async () => {
    try {
      // Fetch ALL notifications (read and unread) from the backend
      const result = await NotificationApi.getAllNotifications(true);
      console.log("All notifications from backend:", result);

      if (result.status) {
        const backendNotifications = result.data.data;
        const newNotificationsState: Notification[] = [];
        let newUnreadCount = 0;

        backendNotifications.forEach((backendNotif: any) => {
          const existingNotification = state.notifications.find(n => n.id === backendNotif.notificationId);
          const notification: Notification = {
            id: backendNotif.notificationId,
            title: backendNotif.title,
            message: backendNotif.message,
            type: backendNotif.type,
            status: backendNotif.status,
            downloadUrl: backendNotif.downloadUrl,
            createdAt: new Date(backendNotif.createdAt),
            updatedAt: new Date(backendNotif.updatedAt),
            persistent: true, // Frontend persistent flag
            isRead: backendNotif.isRead, // Use isRead from backend
            metadata: backendNotif.metadata
          };

          if (existingNotification) {
            // Update existing notification
            newNotificationsState.push({ ...existingNotification, ...notification });
          } else {
            // Add new notification
            newNotificationsState.push(notification);
          }

          if (!notification.isRead) {
            newUnreadCount++;
          }
        });

        // Filter out notifications that are no longer in the backend (e.g., deleted)
        const finalNotifications = state.notifications.filter(localNotif =>
          backendNotifications.some((backendNotif: any) => backendNotif.notificationId === localNotif.id)
        );

        // Add/update notifications from backend
        backendNotifications.forEach((backendNotif: any) => {
          if (!finalNotifications.some(n => n.id === backendNotif.notificationId)) {
            finalNotifications.push({
              id: backendNotif.notificationId,
              title: backendNotif.title,
              message: backendNotif.message,
              type: backendNotif.type,
              status: backendNotif.status,
              downloadUrl: backendNotif.downloadUrl,
              createdAt: new Date(backendNotif.createdAt),
              updatedAt: new Date(backendNotif.updatedAt),
              persistent: true,
              isRead: backendNotif.isRead,
              metadata: backendNotif.metadata
            });
          } else {
            const index = finalNotifications.findIndex(n => n.id === backendNotif.notificationId);
            finalNotifications[index] = {
              ...finalNotifications[index],
              status: backendNotif.status,
              isRead: backendNotif.isRead,
              downloadUrl: backendNotif.downloadUrl,
              message: backendNotif.message,
              progress: backendNotif.progress,
              updatedAt: new Date(backendNotif.updatedAt),
              metadata: backendNotif.metadata
            };
          }
        });

        // Sort by createdAt descending
        finalNotifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

        dispatch({ type: 'LOAD_FROM_STORAGE', payload: finalNotifications }); // Use LOAD_FROM_STORAGE to set the new state and recalculate unread count
      }
    } catch (error) {
      console.error('Error fetching all notifications:', error);
    }
  }, [state.notifications]); // Keep state.notifications in dependency array for comparison

  // Memoize SSE URL to prevent unnecessary re-renders


     const sseUrl =`${baseURL}/v1/notifications/stream`


 const { connect, disconnect }= useSSE(sseUrl, {
    onMessage: handleSSEMessage,
    onError: (error) => {
      // If error is due to authentication, check and update auth status only if needed
      if (error.type === 'error') {
        const userData = localStorage.getItem('UserData');
        const accessToken = localStorage.getItem('AccessToken');
        const shouldBeAuthenticated = !!(userData && accessToken);

        // Only update if the current state doesn't match the actual auth status
        if (isAuthenticated !== shouldBeAuthenticated) {
          setIsAuthenticated(shouldBeAuthenticated);
        }
      }
    },
    onOpen: () => {
      console.log('SSE Connected after authentication');
      // Fetch pending notifications when connection is established
      fetchPendingNotifications();
    },
    onClose: () => {
      console.log('SSE Disconnected');
      // Clear notifications on disconnect if user logged out
      // const userData = localStorage.getItem('UserData');
      // if (!userData) {
      //   dispatch({ type: 'CLEAR_ALL' });
      // }
    }
  });

  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt' | 'updatedAt'>): string => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newNotification: Notification = {
      ...notification,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });
    return id;
  }, []);

  const updateNotification = useCallback((id: string, updates: Partial<Notification>) => {
    dispatch({ type: 'UPDATE_NOTIFICATION', payload: { id, updates } });
  }, []);

  const removeNotification = useCallback((id: string) => {
    const notificationToRemove = state.notifications.find(n => n.id === id);
    if (!notificationToRemove) return;

    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });

    // Conditional backend action based on notification status
    if (notificationToRemove.status === 'pending' || notificationToRemove.status === 'processing') {
      // For pending/processing, mark as read (seen)
      NotificationApi.markNotificationAsRead(id).catch(error => {
        console.error('Failed to mark notification as read in backend:', error);
      });
    } else {
      // For completed/failed, delete from backend
      NotificationApi.deleteNotification(id).catch(error => {
        console.error('Failed to delete notification from backend:', error);
      });
    }

    // Update localStorage
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        const notifications = JSON.parse(stored);
        const updatedNotifications = notifications.filter((n: Notification) => n.id !== id);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedNotifications));
      } catch (error) {
        console.error('Error updating notifications in storage:', error);
      }
    }
  }, [state.notifications]); // Add state.notifications to dependency array

  const markAsRead = useCallback((id: string) => {
    const notificationToMark = state.notifications.find(n => n.id === id);
    if (!notificationToMark) return;

    dispatch({ type: 'MARK_AS_READ', payload: id });

    // Conditional backend action based on notification status
    if (notificationToMark.status === 'pending' || notificationToMark.status === 'processing') {
      // For pending/processing, mark as read (seen)
      NotificationApi.markNotificationAsRead(id).catch(error => {
        console.error('Failed to mark notification as read in backend:', error);
      });
    } else {
      // For completed/failed, delete from backend
      NotificationApi.deleteNotification(id).catch(error => {
        console.error('Failed to delete notification from backend:', error);
      });
    }
  }, [state.notifications]); // Add state.notifications to dependency array

  const markAllAsRead = useCallback(() => {
    // Get all unread notifications
    const unreadNotifications = state.notifications.filter(notification => !notification.isRead);

    dispatch({ type: 'MARK_ALL_AS_READ' });

    // Apply conditional backend action for each unread notification
    unreadNotifications.forEach(notification => {
      if (notification.status === 'pending' || notification.status === 'processing') {
        NotificationApi.markNotificationAsRead(notification.id).catch(error => {
          console.error(`Failed to mark notification ${notification.id} as read in backend:`, error);
        });
      } else {
        NotificationApi.deleteNotification(notification.id).catch(error => {
          console.error(`Failed to delete notification ${notification.id} from backend:`, error);
        });
      }
    });
  }, [state.notifications]); // Add state.notifications to dependency array

  const clearAll = useCallback(() => {
    // Get all notifications
    const allNotifications = state.notifications;

    // Separate notifications to be deleted from those to be marked as read
    const notificationsToDelete = allNotifications.filter(
      (notification) =>
        notification.status === 'completed' ||
        notification.status === 'failed' ||
        notification.status === 'delivered'
    );

    const notificationsToMarkAsRead = allNotifications.filter(
      (notification) =>
        notification.status === 'pending' ||
        notification.status === 'processing'
    );

    // Dispatch actions to update the state
    notificationsToDelete.forEach((notification) => {
      dispatch({ type: 'REMOVE_NOTIFICATION', payload: notification.id });
      NotificationApi.deleteNotification(notification.id).catch((error) => {
        console.error(
          `Failed to delete notification ${notification.id} from backend:`,
          error
        );
      });
    });

    notificationsToMarkAsRead.forEach((notification) => {
      if (!notification.isRead) {
        dispatch({ type: 'MARK_AS_READ', payload: notification.id });
        NotificationApi.markNotificationAsRead(notification.id).catch(
          (error) => {
            console.error(
              `Failed to mark notification ${notification.id} as read in backend:`,
              error
            );
          }
        );
      }
    });
  }, [state.notifications]);

  const downloadNotificationFile = useCallback(async (notificationId: string) => {
    try {
      const result = await NotificationApi.downloadNotificationFile(notificationId);
      if (result.status) {
        // Update notification to mark as downloaded
        updateNotification(notificationId, {
          message: 'File downloaded successfully',
          updatedAt: new Date()
        });
        return { success: true, message: 'File downloaded successfully' };
      } else {
        return { success: false, message: result.data?.message || 'Download failed' };
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      return { success: false, message: 'Download failed' };
    }
  }, [updateNotification]);

  const value: NotificationContextType = {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    addNotification,
    updateNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    downloadNotificationFile,
    connectToSSE:(token)=>{
      console.log('SSE Connection start');

      connect(token);
    },
    disconnectSSE:()=>{
      console.log('SSE Disconnection start');
      disconnect();
    }
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
