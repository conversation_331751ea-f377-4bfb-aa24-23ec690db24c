import WhiteContainer from "@/components/custom/WhiteContainer";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import AsyncSelect from "@/components/ui/react-select";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { ReactselectInterface } from "@/model/global-master";
import { WardObjectInterface } from "@/model/zone-master";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useStreetMasterController } from "@/controller/master/StreetMasterController";
import { StreetSelectOject } from "@/model/street-master";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";
import { PropertytypeMasterObject } from "@/model/propertytype-master";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useNamunaController } from "@/controller/report/MilkatKarAkarniEight";
import { Label } from "@/components/ui/label";
import axios from "axios";
import { Printer } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Loader } from "@/components/globalcomponent/Loader";
import { useLocation } from "react-router-dom";
import Api from "@/services/ApiServices";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import PropertyApi from "@/services/PropertyServices";

interface NamunaEightDetails {
  tax_property_id: string;
  bill_no: string;
  all_property_tax_sum: number;
  other_tax_sum_tax: number;
  total_tax: number;
  capital_value: number;
  bill_generation_date: string;
  property: {
    propertyNumber: string;
    old_propertyNumber: string;
    city_survey_number: string;
    address: string | null;
    house_or_apartment_name: string;
    note: string;
    zone: {
      zoneName: string;
    };
    ward: {
      ward_name: string;
    };
    street: {
      street_name: string;
    };
    water_connection_type: string;
    property_owner_details: {
      property_owner_details_id: string;
      name: string;
      owner_type: {
        owner_type: string;
      };
    }[];
  };
  tax_propertywise: {
    tax_value: number;
    tax: number;
    property_Usage_Details: {
      length: number;
      width: number;
      are_sq_meter: number;
      propertyType: {
        propertyType: string;
      };
    };
  }[];
  tax_property_other_taxes: {
    tax_type: string;
    amount: number;
  }[];
}

const NamunaEight = () => {
  //aded comment
  const { t } = useTranslation();
  const wardList: any = useWardMasterController();
  const wardOptions: ReactselectInterface[] = wardList?.wardList?.map(
    (ward: WardObjectInterface) => ({
      value: ward.ward_id,
      label: ward.ward_name,
    })
  );

  const { namunaEightDetails } = useNamunaController();
  const [printloading, setPrintLoading] = useState(false);
  const [oldPropertyNumber, setPropertyOldNumber] = useState<any>();
  const [propertyNumber, setpropertyNumber] = useState<any>();
  const [propertyNumbers, setpropertyNumbers] = useState<any>();
  const [searchOnParamter, setsearchOnParamter] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [showGenerateButton, setShowGenerateButton] = useState(false);
  const [recordGenerated, setRecordGenerated] = useState(false);
  const [milkatKarId, setMilkatKarId] = useState("");
  const [financialYears, setFinancialYears] = useState([]);
 const [selectedFinancialYear, setSelectedFinancialYear] =useState("");

  const [namanuDetail, setnamanuDetail] = useState(null);
  const location = useLocation();

 
  const loadWardOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };
  const number = location.state?.propertyNumber;
  useEffect(() => {
    if (number) {
      setpropertyNumber(number); // Set the property number from location
      setsearchOnParamter('propertyNumber'); // Set the search parameter
    }
  }, [number]);
  
  useEffect(() => {
    if (number && propertyNumber && searchOnParamter && !oldPropertyNumber && selectedFinancialYear) {
      handleNamunaEight(); // Trigger the search function only if propertyNumber is set from location and FY is selected
    }
  }, [propertyNumber, searchOnParamter, selectedFinancialYear]);
  
  
  const streetorRoadList: any = useStreetMasterController();
  const streetOptions: ReactselectInterface[] =
    streetorRoadList?.streetList?.map((street: StreetSelectOject) => ({
      value: street.street_id,
      label: street.street_name,
    }));
  const handlePrintTax = async () => {
    setPrintLoading(true);
    try {
      const response = await Api.getNamunaEightBill();
      
      if (response.status) {
        const pdfBlob = new Blob([response.data], { type: "application/pdf" });
        const pdfUrl = URL.createObjectURL(pdfBlob);
        window.open(pdfUrl);
      } else {
        toast({
          title: "Failed to fetch the PDF. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to fetch the PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setPrintLoading(false);
    }
  };
  const loadStreetOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        streetOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };

  const propertytypeList: any = usePropertytypeMasterController();
  const propertyOptions: ReactselectInterface[] =
    propertytypeList.propertytypeList?.map(
      (property: PropertytypeMasterObject) => ({
        value: property.propertyType_id,
        label: property.propertyType,
      })
    );

  const loadPropertyOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        propertyOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };
  const handleNamunaEight = async () => {
    const SelectedpropertyNumber = oldPropertyNumber || propertyNumber;
    if (!SelectedpropertyNumber) {
      setnamanuDetail(null);
      toast({
        title: `${t("enterPropertyNumber")}`,
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setnamanuDetail(null);

    try {
      const response = await Api.getMilkatKarAkarni(
        SelectedpropertyNumber,
        searchOnParamter,
        selectedFinancialYear
      );

      if (response.status && response.data.statusCode === 200 && response.data.data) {
        if (response.data.data.property_count === 0) {
          toast({
            title: `${t("noDataFound")}`,
            variant: "destructive",
          });
        } else if (response.data.data.property_count === 1) {
          setShowGenerateButton(true);
          setMilkatKarId(response.data.data.property_id);
        } else {
          setnamanuDetail(response.data.data);
        }
      } else {
        toast({
          title: `${t("noDataFound")}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
        const currentYear = response.data.data.find(year => year.is_current);
        if (currentYear) {
          setSelectedFinancialYear(currentYear.financial_year_range);
        }
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };
  useEffect(() => {
    fetchFinancialYears();
  }, []);
  
  
  useEffect(() => { }, [namanuDetail]);
  const tableHeaders = [
    "अ क्र ",
    "namunaEight.tableIncomeType",
    "मजला",
    "namunaEight.tableLengthHeading",
    "namunaEight.tableWidthHeading",
    "namunaEight.tableAreaHeading",
    "namunaEight.tableDepreciationRateHeading",
    "namunaEight.tableWeightingHeading",
    "namunaEight.tableTaxRateHeading",
    "namunaEight.tableTotalTaxHeading",
  ];

  const totalCapitalValue = namanuDetail?.milkatKar[0]?.milkatKarTax 
  ? namanuDetail.milkatKar[0].milkatKarTax.reduce((acc, item) => acc + (item?.capital_value || 0), 0) 
  : 0;   
  
  const handleOldNumberChange = (e) => {
    setPropertyOldNumber(e.target.value.trim().toUpperCase());
    setpropertyNumber(() => ''); // Clear the other field
    setsearchOnParamter('old_propertyNumber');
  };

  const handlePropertyNumberChange = (e) => {
    setpropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyOldNumber(() => ''); // Clear the other field
    setsearchOnParamter('propertyNumber');
  };

  const generateRecord = async () => {
    setLoading(true);
    try {
      const response = await Api.processMilkatKarAkarni(milkatKarId);
      
      if (response.status && response.data.statusCode === 200) {
        toast({
          title: `${t("recordGenerated")}`,
          variant: "success",
        });
        setRecordGenerated(true);
        setShowGenerateButton(false);
        handleNamunaEight(); 
      } else {
        toast({
          title: "Failed to generate record",
          description: "An error occurred while processing the record.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: `${t("contactAdministrator")}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  const [newTaxgenratingLoader, setnewTaxgenratingLoader] = useState(false)
  const newTaxGenerate = async () => {
    setnewTaxgenratingLoader(true)
    try {
      const response = await Api.processMilkatKarAkarni(
        namanuDetail?.property_id,
        namanuDetail?.ward.ward_name
      );
      
      if (response.status && response.data.statusCode === 200) {
        setLoading(true);
        setnamanuDetail(null);
        handleNamunaEight();
        setLoading(false);
      }
      setnewTaxgenratingLoader(false)

    } catch (error) {
      toast({
        title: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
      setnewTaxgenratingLoader(false)

    }
  }

console.log("namanuDetailnamanuDetail",namanuDetail)
  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          {t("namunaEight.milkatKarAkarni")}
        </h1>
        <WhiteContainer>
          <div className="flex gap-x-8 flex-wrap">
            {/* <div className="flex">
              <Checkbox className="mt-1" />{" "}
              <p className="ml-2 ">
                {t("namunaEight.namunaEightCheckboxOneValue")}
              </p>
            </div>
            <div className="flex">
              <Checkbox className="mt-1" />
              <p className="ml-2 ">
                {t("namunaEight.namunaEightCheckboxTwoValue")}{" "}
              </p>
            </div> */}
            {/* <div className="flex">
              <Checkbox className="mt-1" />
              <p className="ml-2 ">
                {" "}
                {t("namunaEight.namunaEightCheckboxThreeValue")}{" "}
              </p>
            </div> */}
          </div>
          <div>
            {/* <p className="w-full flex items-center justify-between  text-[16px] font-semibold">
              {t("namunaEight.milkatDharkachiMahiti")}
            </p> */}
            <div className="mt-4 mb-3">
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
              <div className="grid-cols-subgrid">
                  <Label>
                    {t("financialYear")}
                    <span className="ml-1 text-red-500">*</span>
                  </Label>
                  <Select
                    onValueChange={setSelectedFinancialYear}
                    value={selectedFinancialYear}
                  >
                    <SelectTrigger className="mt-1" >
                      <SelectValue placeholder={t("selectYear")} />
                    </SelectTrigger>
                    <SelectContent>
                      {financialYears.map((year) => (
                        <SelectItem
                          key={year.financial_year_range}
                          value={year.financial_year_range}
                        >
                          {year.financial_year_range}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid-cols-subgrid">
                  <Label>{t("property.propertyNumberColumn")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.propertyNumberColumn")}
                    value={propertyNumber}
                    onKeyDown={(e) => e.key === 'Enter' && handleNamunaEight()}
                    onChange={handlePropertyNumberChange}

                  />
                </div>
                <div className="grid-cols-subgrid">
                  <Label>{t("property.oldPropertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.oldPropertyNumber")}
                    value={oldPropertyNumber}
                    onChange={handleOldNumberChange}
                    onKeyDown={(e) => e.key === 'Enter' && handleNamunaEight()}
                  />
                </div>
                <div className="grid-cols-subgrid mt-1 flex items-end">
                  <Button variant="submit" onClick={handleNamunaEight}>
                    {t("search")}{" "}
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <p className="text-xs italic font-semibold mb-0 text-[#3c3c3c]">
            {" "}
            {t("selectAnyFieldNote")}{" "}
          </p>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />

          </div>
        )}
 {showGenerateButton && !loading  && !recordGenerated &&(
      <WhiteContainer>
        <div className="flex justify-center my-4">
          <Button onClick={generateRecord} disabled={loading}>
            {loading ? " मालमत्ता कर आकारणी ..." : t("मालमत्ता कर आकारणी")}
          </Button>
        </div>
      </WhiteContainer>
    )}
        {namanuDetail && !loading && !showGenerateButton && (
          <WhiteContainer>
            <div>
              <p className="w-full flex items-center justify-between  text-[19.5px] font-semibold mt-2 mb-3 ">
                {t("namunaEight.tableHeading")}
              </p>
            </div>

            <div>
              <div className="grid md:grid-cols-3 gap-x-3 gap-y-3 mt-2 mb-6 ml-1">
                <div className="grid-cols-subgrid ">
                  <Label>{t("property.propertyNumberColumn")}</Label>
                  <Input
                    className="m block w-full"
                    value={namanuDetail?.propertyNumber || "--"}
                    placeholder={t("property.propertyNumberColumn")}
                    disabled={true}
                  />
                </div>

                <div className="grid-cols-subgrid ">
                  <Label>{t("namunaEight.surveyNumber")}</Label>
                  <Input
                    className="m block w-full"
                    value={namanuDetail?.city_survey_number || "--"}
                    placeholder={t("namunaEight.surveyNumber")}
                    disabled={true}
                  />
                </div>

                <div className="grid-cols-subgrid ">
                  <Label>{t("namunaEight.streetName")}</Label>
                  <Input
                    placeholder={t("namunaEight.streetName")}
                    className=""
                    value={namanuDetail?.street?.street_name || "--"}
                    disabled={true}
                    transliterate={false}
                  />
                </div>

                <div className="grid-cols-subgrid ">
                  <Label>{t("namunaEight.waterType")}</Label>
                  <Input
                    placeholder={t("namunaEight.waterType")}
                    className=""
                    transliterate={false}
                    value={namanuDetail?.water_connection_type || "--"}
                    disabled={true}
                  />
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("namunaEight.incomeHolderName")}</Label>
                  <Textarea
                    className="mt-1 block w-full mr-3"
                    placeholder={t("namunaEight.incomeHolderName")}
                    value={
                      namanuDetail?.property_owner_details
                        ?.filter(
                          (owner) => owner.owner_type.owner_type === "स्वत:"
                        )
                        .map((owner) => owner.name)
                        .join(", ") || "--"
                    }
                    disabled={true}
                  />
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("namunaEight.occupancyHolder")}</Label>
                  <Textarea
                    className="mt-1 block w-full"
                    placeholder={t("namunaEight.occupancyHolderName")}
                    transliterate={false}
                    value={
                      namanuDetail?.property_owner_details
                        ?.filter(
                          (owner) => owner.owner_type.owner_type === "भोगवटादार"
                        )
                        .map((owner) => owner.name)
                        .join(", ") || "--"
                    }
                    disabled={true}
                  />
                </div>
              </div>
              <hr />
              <div>
                <div>
                  <p className="w-full flex items-center justify-between  text-[19.5px] font-semibold mt-5 mb-3">
                    मिळकत कराचे वर्णन
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mt-2 ml-1">
                  <div className="grid-cols-subgrid">
                    <Label>{t("namunaEight.buildingTax")}(₹)</Label>
                    <Input
                      className="mt-1 block w-full"
                      placeholder={t("namunaEight.buildingTax")}
                      value={
                        namanuDetail?.milkatKar?.[0]?.all_property_tax_sum?.toFixed(
                          2
                        ) || "--"
                      }
                      disabled={true}
                    />
                  </div>

                  {Object.keys(namanuDetail?.tax_types || {}).map(
                    (key, index) => (
                      <div className="grid-cols-subgrid" key={index}>
                        <Label>{namanuDetail.tax_types[key]}(₹)</Label>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={namanuDetail.tax_types[key]}
                          value={
                            namanuDetail?.milkatKar?.[0]?.[key]?.toFixed(2) ||
                            "--"
                          }
                          disabled={true}
                        />
                      </div>
                    )
                  )}

                  <div className="grid-cols-subgrid">
                    <Label>{t("namunaEight.capitalValue")}(₹)</Label>
                    <Input
                      className="mt-1 block w-full"
                      placeholder={t("namunaEight.capitalValue")}
                      value={totalCapitalValue ? totalCapitalValue.toFixed(2) : "--"}
                      disabled={true}
                    />
                  </div>

                  <div className="grid-cols-subgrid">
                    <Label>{t("namunaEight.totalTax")}(₹)</Label>
                    <Input
                      className="mt-1 block w-full"
                      placeholder={t("namunaEight.totalTax")}
                      value={
                        namanuDetail?.milkatKar?.[0]?.total_tax?.toFixed(2) ||
                        "--"
                      }
                      disabled={true}
                    />
                  </div>

                  <div className="col-span-full">
                    <p className="w-full flex items-center justify-between text-[16px] font-semibold">
                      {t("namunaEight.incomeNote")}
                    </p>
                    <Textarea
                      className="mt-1 block w-1/3"
                      transliterate={false}
                      value={namanuDetail?.note || "--"}
                      placeholder={t("namunaEight.incomeNotePlaceholder")}
                      disabled={true}
                    />
                  </div>
                </div>

                {namanuDetail?.milkatKar?.map((milkatKarItem, index) => (
                  <div key={index} className="mb-5 mt-4 overflow-x-auto ml-1">
                    <h3 className="font-bold text-lg mb-4">{t("financialYear")}: {milkatKarItem.financial_year}</h3>
                    <Table className="w-full text-sm text-left text-black border border-[#e5e7eb] income-tax-eight-table">
                      <TableHeader className="uppercase bg-gray-100 border-[#e5e7eb] font-bold">
                        <TableRow>
                          {tableHeaders.map((header) => (
                            <TableHead
                              key={header}
                              className="py-3 px-6 border-b border-[#e5e7eb] bg-[#f3f4f6] font-semibold text-black text-center text-base"
                            >
                              {t(header)}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {milkatKarItem?.milkatKarTax?.map((item, taxIndex) => (
                          <TableRow
                            key={taxIndex}
                            className="text-center border-b border-gray-500"
                          >
                            <TableCell className="py-3 px-6">{taxIndex + 1}</TableCell>
                            <TableCell className="py-3 px-6">
                              {item.property_usage_details?.propertyType?.propertyType || "--"}
                            </TableCell>

                                <TableCell className="py-3 px-6">
                              {item.property_usage_details?.floorType?.floor_name || "--"}
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.property_usage_details?.length} m
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.property_usage_details?.width} m
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.property_usage_details?.are_sq_meter} sq.m
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.depreciation_rate? `${item.depreciation_rate}%` : "--"}
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.weighting ? `${item.weighting}%` : "--"}
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.tax_value ? `${item.tax_value}%` : "--"}
                            </TableCell>
                            <TableCell className="py-3 px-6">
                              {item.property_usage_details?.propertyType?.propertyType =="पडसर" ? namanuDetail?.milkatKar?.[0]?.tax_type_8?.toFixed(2): item.tax?item.tax:"--" }
                          
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ))}

              </div>

              <div className="flex justify-end mb-4">
                <div className="flex align-bottom items-end">
                  <Button
                    variant="submit"
                    className="mt-3 mr-3"
                    disabled={newTaxgenratingLoader || namanuDetail?.updateStatus !== "Updated"}
                    onClick={newTaxGenerate}
                  >
                    {t("namunaEight.newTaxRate")}{" "}
                  </Button>
                  
                  {/* <Button
                    className={`bg-[#2c93d2] hover:bg-[#2c93d2] ${printloading ? "cursor-not-allowed opacity-50" : ""
                      }`}
                    onClick={handlePrintTax}
                    disabled={printloading}
                  >
                    <span className={printloading ? "hidden" : "relative"}>
                      {t("paymentform.printButton")}
                    </span>
                    <Printer
                      className={`w-5 ${printloading ? "animate-bounce" : "ml-4"
                        }`}
                    />
                  </Button> */}
                </div>
              </div>
            </div>
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default NamunaEight;
